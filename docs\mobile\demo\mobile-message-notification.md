# 移动端消息通知优化 📱

本文档介绍了移动端消息通知的优化方案，实现了移动端不显示弹窗通知，而是在底栏显示徽章的功能。

## 🎯 功能概述

### 核心改进
- ✅ **移动端徽章显示** - 在底部导航栏的消息按钮上显示未读消息数量徽章
- ✅ **桌面端弹窗保留** - 桌面端继续显示消息弹窗通知
- ✅ **设备自动检测** - 根据设备类型自动选择通知方式
- ✅ **统一状态管理** - 使用Composable统一管理消息状态
- ✅ **防重复通知** - 避免频繁显示重复通知

### 设计理念
- **移动端友好** - 徽章显示不干扰用户操作
- **桌面端保持** - 保留原有的弹窗通知体验
- **响应式适配** - 根据设备类型自动适配

## 🏗️ 技术架构

### 文件结构
```
src/
├── composables/
│   └── useMessageNotification.ts     # 消息通知管理Composable
├── views/layout/
│   ├── mobile.vue                    # 移动端布局（已更新）
│   └── components/topBar/index.vue   # 桌面端顶栏（已更新）
└── utils/
    └── device.ts                     # 设备检测工具
```

### 核心组件

#### 1. 消息通知Composable
```typescript
// src/composables/useMessageNotification.ts
export function useMessageNotification() {
  const unreadCount = ref(0)
  const isMobile = computed(() => isMobileDevice())
  
  // 移动端显示徽章
  const shouldShowMobileBadge = computed(() => {
    return isMobile.value && unreadCount.value > 0
  })
  
  // 桌面端显示弹窗
  const shouldShowDesktopNotification = computed(() => {
    return !isMobile.value && unreadCount.value > 0
  })
  
  // 处理消息通知逻辑
  const handleMessageNotification = async () => {
    const count = await fetchUnreadCount()
    
    if (count > 0) {
      if (isMobile.value) {
        // 移动端：只更新徽章，不显示弹窗
        console.log(`移动端消息徽章更新: ${count} 条未读消息`)
      } else {
        // 桌面端：显示弹窗通知
        showDesktopNotification()
      }
    }
  }
}
```

#### 2. 移动端底栏徽章
```vue
<!-- src/views/layout/mobile.vue -->
<template>
  <button v-for="item in bottomNavItems" :key="item.key">
    <!-- 消息徽章 -->
    <n-badge 
      v-if="item.key === 'message'" 
      :value="messageNotification.unreadCount.value" 
      :show="messageNotification.shouldShowMobileBadge.value"
      :offset="[8, -8]"
      size="small"
      type="error"
    >
      <n-icon>
        <component :is="item.icon" />
      </n-icon>
    </n-badge>
    <!-- 普通图标 -->
    <n-icon v-else>
      <component :is="item.icon" />
    </n-icon>
    <span>{{ item.title }}</span>
  </button>
</template>

<script setup>
import { useGlobalMessageNotification } from '@/composables/useMessageNotification'

const messageNotification = useGlobalMessageNotification()
</script>
```

#### 3. 桌面端弹窗优化
```typescript
// src/views/layout/components/topBar/index.vue
const tempFn = () => {
  queryNoLookNum({}).then(res => {
    noLookNum.value = res.data
    if (noLookNum.value != 0) {
      // 只在桌面端显示弹窗通知，移动端不显示
      if (!isMobileDevice() && !noLookMessageShow.value) {
        noLookMessageShow.value = true
        
        window.$message.create(null, {
          type: 'info',
          duration: 300000,
          closable: true,
          render: () => {
            // 弹窗内容
          }
        })
      }
      // 移动端只更新数量，不显示弹窗
      console.log(`${isMobileDevice() ? '移动端' : '桌面端'}消息数量更新: ${noLookNum.value}`)
    }
  })
}
```

## 📱 使用效果

### 移动端效果
```
┌─────────────────────────────────┐
│ ← 系统首页                        │ ← 顶部标题栏
├─────────────────────────────────┤
│                                 │
│        主要内容区域               │
│                                 │
├─────────────────────────────────┤
│ [首页] [门户] [消息③] [我的]      │ ← 底部导航栏
└─────────────────────────────────┘
```

- **消息徽章** - 红色圆形徽章显示未读数量
- **位置固定** - 徽章位于消息图标右上角
- **自动更新** - 30秒轮询更新未读数量
- **无弹窗干扰** - 不显示任何弹窗通知

### 桌面端效果
```
┌─────────────────────────────────┐
│ Logo  [菜单]     [🔔3] [👤]     │ ← 顶部工具栏
├─────────────────────────────────┤
│ ┌─────────────────────────────┐ │
│ │ 📢 您有 3 条未读消息         │ │ ← 弹窗通知
│ │ [点击查看]           [关闭] │ │
│ └─────────────────────────────┘ │
│                                 │
│        主要内容区域               │
│                                 │
└─────────────────────────────────┘
```

- **弹窗通知** - 保留原有的弹窗提醒
- **消息图标** - 顶栏消息图标显示徽章
- **点击交互** - 点击可查看消息详情

## 🔧 配置选项

### 轮询间隔配置
```typescript
// 默认30秒轮询一次
const POLLING_INTERVAL = 30000

// 可以根据需要调整
const messageNotification = useMessageNotification()
messageNotification.startPolling() // 开始轮询
messageNotification.stopPolling()  // 停止轮询
```

### 徽章样式配置
```vue
<n-badge 
  :value="unreadCount" 
  :show="shouldShow"
  :offset="[8, -8]"     // 徽章偏移位置
  size="small"          // 徽章大小
  type="error"          // 徽章类型（红色）
  :max="99"             // 最大显示数量
/>
```

### 防重复通知配置
```typescript
// 1分钟内不重复显示桌面端通知
const lastNotificationTime = localStorage.getItem('lastNotificationTime')
if (lastNotificationTime && now - parseInt(lastNotificationTime) < 60000) {
  return // 跳过显示
}
```

## 🎨 样式定制

### 徽章样式
```css
/* 徽章位置调整 */
.n-badge {
  position: relative;
}

.n-badge .n-badge-sup {
  top: -8px;
  right: -8px;
  min-width: 16px;
  height: 16px;
  font-size: 12px;
}

/* 移动端底栏按钮 */
.mobile-bottom-nav button {
  position: relative;
  transition: all 0.2s ease;
}

.mobile-bottom-nav button:active {
  transform: scale(0.95);
}
```

### 响应式适配
```css
/* 小屏幕适配 */
@media (max-width: 480px) {
  .n-badge .n-badge-sup {
    top: -6px;
    right: -6px;
    min-width: 14px;
    height: 14px;
    font-size: 11px;
  }
}
```

## 🚀 性能优化

### 1. 轮询优化
- **智能轮询** - 页面不可见时停止轮询
- **错误重试** - 网络错误时自动重试
- **缓存机制** - 避免重复请求

### 2. 内存优化
- **组件卸载** - 自动清理定时器和监听器
- **状态重置** - 页面切换时重置状态
- **单例模式** - 全局共享消息状态

### 3. 用户体验
- **即时响应** - 消息状态变化立即更新UI
- **平滑动画** - 徽章显示/隐藏有过渡效果
- **触摸友好** - 移动端按钮大小适合触摸

## 📊 兼容性说明

### 设备支持
- ✅ **iOS Safari** - 完美支持徽章显示
- ✅ **Android Chrome** - 完美支持徽章显示
- ✅ **桌面浏览器** - 保持原有弹窗功能
- ✅ **平板设备** - 根据屏幕尺寸自动适配

### 浏览器兼容
- ✅ **现代浏览器** - 支持所有功能
- ✅ **IE11+** - 基础功能支持
- ✅ **移动浏览器** - 优化的触摸体验

## 🎉 总结

移动端消息通知优化实现了：

1. **差异化体验** - 移动端徽章 vs 桌面端弹窗
2. **自动适配** - 根据设备类型自动选择通知方式
3. **性能优化** - 智能轮询和内存管理
4. **用户友好** - 不干扰移动端用户操作

这个优化显著提升了移动端用户体验，同时保持了桌面端的功能完整性！🚀

## 📚 相关文件

- `src/composables/useMessageNotification.ts` - 消息通知管理
- `src/views/layout/mobile.vue` - 移动端布局
- `src/views/layout/components/topBar/index.vue` - 桌面端顶栏
- `src/utils/device.ts` - 设备检测工具
- `docs/mobile-message-notification.md` - 本文档
