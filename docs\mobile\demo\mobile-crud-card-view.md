# 移动端CRUD卡片视图使用指南 📱

本文档介绍了CRUD组件的移动端卡片视图功能，包括配置方法、使用示例和最佳实践。

## 🎯 功能概览

### 核心特性
- ✅ **自动设备检测** - 根据屏幕尺寸自动切换卡片视图
- ✅ **视图切换功能** - 移动端支持卡片/表格视图切换
- ✅ **智能字段布局** - 支持配置标题、副标题、头部、主体、底部显示
- ✅ **视图配置** - 移动端专用的列配置组件
- ✅ **保持功能完整** - 完全兼容原有的增删改查功能
- ✅ **响应式设计** - 适配不同屏幕尺寸和设备类型

### 视图对比
```
桌面端：表格视图 (n-data-table)
移动端：卡片视图 (MobileCardView) + 表格视图切换
```

### 视图切换功能
移动端用户可以在卡片视图和表格视图之间自由切换：

```
┌─────────────────────────────────┐
│                    [卡片][表格] │ ← 视图切换按钮
├─────────────────────────────────┤
│ 📋 卡片视图内容...              │
│ 或                              │
│ 📊 表格视图内容...              │
└─────────────────────────────────┘
```

## 🔧 基础配置

### 1. 启用移动端卡片视图

```vue
<template>
  <j-crud
    :columns="columns"
    :query-method="queryData"
    :force-mobile-view="false"  <!-- 可选：强制使用移动端视图 -->
    :mobile-default-card-view="true"  <!-- 可选：移动端默认显示卡片视图 -->
    :enable-mobile-view-switch="true"  <!-- 可选：允许移动端视图切换 -->
    :show-mobile-view-config="true"  <!-- 可选：显示视图配置按钮 -->
    :mobile-view-title="'数据列表'"  <!-- 可选：移动端视图标题 -->
    <!-- 其他原有配置... -->
  />
</template>
```

### 2. 列配置扩展

为`CRUDColumnInterface`添加了以下移动端专用字段：

```typescript
interface CRUDColumnInterface {
  // 原有字段...
  
  // 移动端相关配置
  mobileTitle?: boolean        // 是否作为卡片标题显示
  mobileSubtitle?: boolean     // 是否作为卡片副标题显示
  mobileShow?: boolean         // 是否在移动端显示
  mobileOrder?: number         // 移动端显示顺序
  mobilePosition?: 'header' | 'body' | 'footer'  // 卡片中的显示位置
  mobileRender?: (row: T, index?: number) => VNode  // 移动端专用渲染函数
}
```

## 📋 配置示例

### 完整的列配置示例

```typescript
const columns: CRUDColumnInterface[] = [
  {
    title: '用户ID',
    key: 'id',
    width: 80,
    mobileShow: false, // 移动端不显示ID
  },
  {
    title: '用户名',
    key: 'username',
    required: true,
    mobileTitle: true, // 作为移动端卡片标题
    mobileOrder: 1,
  },
  {
    title: '姓名',
    key: 'name',
    required: true,
    mobileSubtitle: true, // 作为移动端卡片副标题
    mobileOrder: 2,
  },
  {
    title: '状态',
    key: 'status',
    type: ContainerValueType.SELECT,
    selection: [
      { label: '正常', value: 'active' },
      { label: '禁用', value: 'disabled' }
    ],
    mobilePosition: 'header', // 在移动端显示在头部（状态标签）
    mobileOrder: 3,
    render: (row) => {
      const statusMap = {
        active: { text: '正常', type: 'success' },
        disabled: { text: '禁用', type: 'error' }
      }
      const status = statusMap[row.status] || { text: '未知', type: 'default' }
      return h('n-tag', { type: status.type, size: 'small' }, () => status.text)
    }
  },
  {
    title: '邮箱',
    key: 'email',
    mobileOrder: 4, // 显示在主体区域
  },
  {
    title: '创建时间',
    key: 'createTime',
    mobilePosition: 'footer', // 在移动端显示在底部
    mobileOrder: 5,
  }
]
```

### 卡片布局结构

```
┌─────────────────────────────────┐
│ 卡片头部 (Header)                │
│ ┌─────────────┐ ┌─────────────┐ │
│ │ 主标题       │ │ 状态标签     │ │
│ │ 副标题       │ │ (header字段) │ │
│ └─────────────┘ └─────────────┘ │
├─────────────────────────────────┤
│ 卡片主体 (Body)                 │
│ • 邮箱：<EMAIL>        │
│ • 手机：138****8888             │
│ • 部门：技术部                  │
├─────────────────────────────────┤
│ 卡片底部 (Footer)               │
│ 创建时间：2024-01-01  [编辑][删除] │
└─────────────────────────────────┘
```

## 🎨 视图配置功能

### 移动端列配置组件

移动端提供了专用的列配置组件，支持：

1. **筛选配置** - 选择要显示的字段
2. **排序配置** - 拖拽调整字段显示顺序  
3. **布局配置** - 设置字段在卡片中的位置

### 配置界面功能

```
┌─────────────────────────────────┐
│ 视图配置                        │
├─────────────────────────────────┤
│ [筛选] [排序] [布局]            │
├─────────────────────────────────┤
│ 筛选：选择要显示的字段           │
│ ☑ 用户名  ☑ 姓名  ☐ 用户ID     │
│                                 │
│ 排序：拖拽调整显示顺序           │
│ ≡ 用户名 (1)                   │
│ ≡ 姓名 (2)                     │
│                                 │
│ 布局：配置字段位置               │
│ 主标题：[用户名 ▼]              │
│ 副标题：[姓名 ▼]                │
└─────────────────────────────────┘
```

## 🚀 使用示例

### 基础使用

```vue
<template>
  <j-crud
    :columns="columns"
    :query-method="queryData"
    :add-method="addData"
    :update-method="updateData"
    :del-method="deleteData"
    name="用户"
    row-key="id"
  />
</template>

<script setup lang="ts">
import { CRUDColumnInterface } from '@/types/comps/crud'

const columns: CRUDColumnInterface[] = [
  {
    title: '姓名',
    key: 'name',
    mobileTitle: true,
    mobileOrder: 1,
  },
  {
    title: '邮箱',
    key: 'email',
    mobileSubtitle: true,
    mobileOrder: 2,
  },
  // 更多列配置...
]

const queryData = async (params: any) => {
  // 查询逻辑
}

// 其他CRUD方法...
</script>
```

### 强制移动端视图

```vue
<template>
  <j-crud
    :columns="columns"
    :query-method="queryData"
    :force-mobile-view="true"  <!-- 强制使用移动端视图 -->
    mobile-view-title="客户管理"
  />
</template>
```

### 自定义移动端渲染

```typescript
const columns: CRUDColumnInterface[] = [
  {
    title: '头像',
    key: 'avatar',
    mobilePosition: 'header',
    mobileRender: (row) => {
      return h('n-avatar', {
        src: row.avatar,
        size: 'small',
        round: true
      })
    }
  },
  {
    title: '状态',
    key: 'status',
    mobilePosition: 'header',
    mobileRender: (row) => {
      const colors = {
        active: 'success',
        inactive: 'error',
        pending: 'warning'
      }
      return h('n-tag', {
        type: colors[row.status],
        size: 'small'
      }, () => row.statusText)
    }
  }
]
```

## 📱 响应式适配

### 设备检测

系统会自动检测设备类型：

```typescript
import { isMobile, isTablet, isDesktop } from '@/utils/device'

// 自动检测
if (isMobile()) {
  // 显示卡片视图
} else {
  // 显示表格视图
}
```

### 断点配置

```typescript
const BREAKPOINTS = {
  mobile: 768,    // 小于768px为移动端
  tablet: 1024,   // 768px-1024px为平板
  desktop: 1024   // 大于1024px为桌面端
}
```

## 🎯 最佳实践

### 1. 字段配置建议

```typescript
// ✅ 推荐的移动端字段配置
const mobileOptimizedColumns = [
  // 主要信息作为标题
  { key: 'name', mobileTitle: true, mobileOrder: 1 },
  
  // 次要信息作为副标题
  { key: 'email', mobileSubtitle: true, mobileOrder: 2 },
  
  // 状态信息显示在头部
  { key: 'status', mobilePosition: 'header', mobileOrder: 3 },
  
  // 详细信息显示在主体
  { key: 'phone', mobileOrder: 4 },
  { key: 'department', mobileOrder: 5 },
  
  // 时间信息显示在底部
  { key: 'createTime', mobilePosition: 'footer', mobileOrder: 6 },
  
  // 不重要的字段在移动端隐藏
  { key: 'id', mobileShow: false },
]
```

### 2. 性能优化

- 合理设置`mobileShow: false`隐藏不必要的字段
- 使用`mobileRender`优化复杂字段的移动端显示
- 避免在移动端显示过多字段

### 3. 用户体验

- 确保标题和副标题信息足够识别记录
- 重要状态信息放在头部便于快速识别
- 操作按钮保持在底部便于触摸

## 🔍 调试与测试

### 开发者工具

```javascript
// 在浏览器控制台中强制切换设备类型
window.__FORCE_MOBILE__ = true
location.reload()
```

### 测试不同设备

1. 使用浏览器开发者工具的设备模拟
2. 调整浏览器窗口大小测试响应式
3. 在真实移动设备上测试触摸交互

## 📚 相关文件

- `src/components/common/crud/index.vue` - CRUD主组件
- `src/components/common/crud/components/mobileCardView.vue` - 移动端卡片视图
- `src/components/common/crud/components/mobileColumnConfig.vue` - 移动端列配置
- `src/types/comps/crud.ts` - 类型定义
- `src/utils/device.ts` - 设备检测工具
- `src/views/modules/demo/mobile-crud-demo.vue` - 使用示例

## 🚨 注意事项

1. **兼容性**：移动端卡片视图完全兼容原有CRUD功能
2. **性能**：大数据量时建议启用分页和虚拟滚动
3. **样式**：卡片样式会自动适配当前主题
4. **事件**：所有原有事件（增删改查、行点击等）都正常工作

## 🔄 版本更新

- v1.0.0 - 初始版本，支持基础卡片视图
- v1.1.0 - 添加视图配置功能
- v1.2.0 - 支持自定义移动端渲染函数
