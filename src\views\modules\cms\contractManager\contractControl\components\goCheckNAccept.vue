<template>
  <div class="acceptance-container">
    <!-- 合同基本信息 -->
    <j-title-line title="合同基本信息" />
    <n-descriptions label-placement="left" :column="2" bordered size="small" class="contract-info">
      <n-descriptions-item label="合同编号">
        <n-text strong>{{ contractInfo.ctCode }}</n-text>
      </n-descriptions-item>
      <n-descriptions-item label="统一编号">
        <n-text>{{ contractInfo.ctUnifiedCode }}</n-text>
      </n-descriptions-item>
      <n-descriptions-item label="合同名称">
        <n-text strong type="primary">{{ contractInfo.ctName }}</n-text>
      </n-descriptions-item>
      <n-descriptions-item label="合同分类">
        <n-tag type="info">{{ getContractTypeName(contractInfo.ctTypeCode) }}</n-tag>
      </n-descriptions-item>
      <n-descriptions-item label="相对方">
        <n-text>{{ contractInfo.oppositeName }}</n-text>
      </n-descriptions-item>
      <n-descriptions-item label="合同金额">
        <n-text type="warning" strong>¥ {{ contractInfo.totalAmt || '0' }}</n-text>
      </n-descriptions-item>
      <n-descriptions-item label="签订日期">
        <n-text>{{ contractInfo.signTime }}</n-text>
      </n-descriptions-item>
      <n-descriptions-item label="有效期">
        <n-text>{{ contractInfo.validityStartDate }} 至 {{ contractInfo.validityEndDate }}</n-text>
      </n-descriptions-item>
      <n-descriptions-item label="使用科室">
        <n-text>{{ getOrgName(contractInfo.useOrg) }}</n-text>
      </n-descriptions-item>
      <n-descriptions-item label="管理科室">
        <n-text>{{ getOrgName(contractInfo.manageOrg) }}</n-text>
      </n-descriptions-item>
      <n-descriptions-item label="经办人">
        <n-text>{{ contractInfo.responsiblePersonName }}</n-text>
      </n-descriptions-item>
      <n-descriptions-item label="联系电话">
        <n-text>{{ contractInfo.responsiblePhone }}</n-text>
      </n-descriptions-item>
    </n-descriptions>

    <!-- 合同附件展示 -->
    <j-title-line title="合同文件" style="margin-top: 20px">
      <template #default>
        <n-button size="small" @click="openCompareDrawer" type="primary">
          <template #icon>
            <n-icon><compare-outline /></n-icon>
          </template>
          文件对比
        </n-button>
      </template>
    </j-title-line>
    <div class="contract-files-container">
      <div class="files-row">
        <!-- 合同原件 -->
        <div class="file-section">
          <h4 style="color: #2E8B57;">📄 合同原件 ({{ contractOriginalFiles.length }})</h4>
          <n-data-table
            v-if="contractOriginalFiles.length > 0"
            :columns="originalFileColumns"
            :data="contractOriginalFiles"
            :pagination="false"
            :bordered="false"
            size="small"
          />
          <div v-else class="no-files-tip">暂无合同原件</div>
        </div>

        <!-- 已签合同附件 -->
        <div class="file-section">
          <h4 style="color: #CD853F;">📋 已签合同附件 ({{ contractSignedFiles.length }})</h4>
          <n-data-table
            v-if="contractSignedFiles.length > 0"
            :columns="signedFileColumns"
            :data="contractSignedFiles"
            :pagination="false"
            :bordered="false"
            size="small"
          />
          <div v-else class="no-files-tip">暂无已签合同附件</div>
        </div>
      </div>
    </div>

    <!-- 验收计划配置 -->
    <j-title-line title="验收计划" style="margin-top: 20px" class="exp-space">
      <template #default>
        <div style="display: flex; flex-direction: row; margin-right: 20px">
          <n-popconfirm placement="left" @positive-click="clearKeyElements">
            <template #trigger>
              <n-button size="small" type="error" style="margin-right: 8px;">
                <template #icon>
                  <n-icon><delete-outline /></n-icon>
                </template>
                移除全部
              </n-button>
            </template>
            <span>移除全部验收要素？</span>
          </n-popconfirm>
          <n-button size="small" type="primary" @click="addKeyElement">
            <template #icon>
              <n-icon><add-outline /></n-icon>
            </template>
            添加要素
          </n-button>
        </div>
      </template>
    </j-title-line>
    <n-data-table ref="elements" :columns="elementsColumns" :data="elementsData" v-if="elementsData.length > 0" />
    <div v-else class="no-elements-tip">
      暂无验收要素，请点击"添加要素"创建验收计划
    </div>

    <!-- 验收记录信息 -->
    <j-title-line title="验收记录信息" style="margin-top: 20px" />
    <n-form ref="formRef" :model="acceptanceForm" :rules="formRules" label-placement="left" label-width="120px">
      <n-row :gutter="24">
        <n-col :span="8">
          <n-form-item label="验收类型" path="acceptanceType">
            <n-select 
              v-model:value="acceptanceForm.acceptanceType" 
              :options="acceptanceTypeOptions"
              @update:value="onAcceptanceTypeChange"
            />
          </n-form-item>
        </n-col>
        <n-col :span="8" v-if="acceptanceForm.acceptanceType === 'MILESTONE'">
          <n-form-item label="关联要素" path="linkedElementId">
            <n-select 
              v-model:value="acceptanceForm.linkedElementId"
              :options="elementSelectOptions"
              placeholder="选择要验收的里程碑要素"
            />
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item label="验收日期" path="acceptanceDate">
            <n-date-picker 
              v-model:formatted-value="acceptanceForm.acceptanceDate" 
              type="date"
              value-format="yyyy-MM-dd"
              clearable
            />
          </n-form-item>
        </n-col>
      </n-row>
      
      <n-row :gutter="24">
        <n-col :span="8">
          <n-form-item label="关联业务" path="relatedBusiness">
            <n-select 
              v-model:value="acceptanceForm.relatedBusiness"
              :options="relatedBusinessOptions"
            />
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item label="验收负责人" path="acceptancePerson">
            <j-form-item 
              :action-form="acceptanceForm" 
              :item="{key: 'acceptancePerson', type: ContainerValueType.EMP}"
            />
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item label="验收科室" path="acceptanceDept">
            <j-form-item 
              :action-form="acceptanceForm" 
              :item="{key: 'acceptanceDept', type: ContainerValueType.ORG}"
            />
          </n-form-item>
        </n-col>
      </n-row>

      <n-row :gutter="24">
        <n-col :span="8">
          <n-form-item label="验收结果" path="acceptanceResult">
            <n-select 
              v-model:value="acceptanceForm.acceptanceResult" 
              :options="acceptanceResultOptions"
            />
          </n-form-item>
        </n-col>
      </n-row>

      <n-row :gutter="24">
        <n-col :span="8">
          <n-form-item label="是否需要第三方" path="needThirdParty">
            <n-switch 
              v-model:value="acceptanceForm.needThirdParty"
            />
          </n-form-item>
        </n-col>
        <n-col :span="12" v-if="acceptanceForm.needThirdParty">
          <n-form-item label="第三方机构" path="thirdPartyOrg">
            <n-input 
              v-model:value="acceptanceForm.thirdPartyOrg" 
              placeholder="请输入第三方验收机构"
            />
          </n-form-item>
        </n-col>
      </n-row>

      <n-row :gutter="24">
        <n-col :span="24">
          <n-form-item label="验收说明" path="acceptanceDesc">
            <n-input 
              v-model:value="acceptanceForm.acceptanceDesc" 
              type="textarea" 
              :rows="3"
              placeholder="请填写验收情况和结论..."
            />
          </n-form-item>
        </n-col>
      </n-row>
    </n-form>

    <!-- 验收附件 -->
    <j-title-line title="验收附件" style="margin-top: 20px" />
    <div>
      <n-upload
        multiple
        :file-list="fileList"
        @update:file-list="fileChange"
        accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.png"
        :max="10"
      >
        <n-upload-dragger>
          <div style="margin-bottom: 12px">
            <n-icon size="48" :depth="3">
              <archive-icon />
            </n-icon>
          </div>
          <n-text style="font-size: 16px">点击或者拖动文件到该区域来上传验收资料</n-text>
          <n-p depth="3" style="margin: 8px 0 0 0">
            支持pdf、doc、excel、图片等格式，最多上传10个文件
          </n-p>
        </n-upload-dragger>
      </n-upload>
    </div>
    
    <!-- 已上传附件展示 -->
    <div v-if="attachmentList.length > 0" class="attachment-list">
      <n-space>
        <n-tag 
          v-for="(item, index) in attachmentList" 
          :key="index"
          closable
          @close="removeAttachment(index)"
          @click="previewAttachment(item)"
          style="cursor: pointer; margin: 5px"
        >
          {{ item.attName }}
        </n-tag>
      </n-space>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <n-space>
        <n-button type="primary" @click="submitAcceptance" :loading="submitting">
          保存验收记录
        </n-button>
        <n-button @click="closeModal">
          取消
        </n-button>
      </n-space>
    </div>
  </div>

  <!-- 文件预览 -->
  <j-preview 
    v-model:show="showPreview" 
    :oss-path="previewPath" 
    :oss-path-name="previewName" 
    :bucket="previewBucket"
  />

  <!-- 抽屉组件 -->
  <n-drawer
    v-model:show="showCompareDrawer"
    :width="drawerWidth"
    :height="drawerHeight"
    placement="left"
    resizable
    @update:width="handleWidthChange"
    @update:height="handleHeightChange"
  >
    <n-drawer-content title="原件比对" closable>
      <div class="comparison-container">
        <!-- 左侧文件列表 -->
        <div class="file-list-section">
          <h2 class="section-title">原始文件</h2>
          <n-data-table
            :columns="originalCompareColumns"
            :data="contractOriginalFiles"
            :row-key="row => row.value"
            @update:checked-row-keys="handleOriginalCheck"
          />
          <div class="preview-section" style="margin-top: 16px; height: 700px">
            <h2 class="section-title">原始文件预览</h2>
            <file-preview
              v-if="leftPreviewFile"
              :oss-path="leftPreviewFile.ossPath"
              :oss-path-name="leftPreviewFile.ossPathName"
              :bucket="leftPreviewFile.bucket"
            />
          </div>
        </div>

        <!-- 中间操作区 -->
        <div class="control-section">
          <div class="control-content">
            <h2 class="section-title">操作栏</h2>
            <n-space vertical style="margin-top: 20px">
              <n-button
                type="primary"
                :disabled="originalChecked.length === 0 || signedChecked.length === 0"
                @click="startCompare"
              >
                开始对比
              </n-button>
              <n-button @click="resetSelection">重置选择</n-button>
              <n-button @click="closeCompareDrawer" type="error">关闭对比</n-button>
            </n-space>

            <div class="note-section">
              <h2 class="section-title">我的备注</h2>
              <n-input
                v-model:value="compareNote"
                type="textarea"
                placeholder="请在此输入对比结论..."
                :autosize="{ minRows: 6, maxRows: 12 }"
              />
            </div>
          </div>
        </div>

        <!-- 右侧文件列表 -->
        <div class="file-list-section">
          <h2 class="section-title">签署文件</h2>
          <n-data-table
            :columns="signedCompareColumns"
            :data="contractSignedFiles"
            :row-key="row => row.value"
            @update:checked-row-keys="handleSignedCheck"
          />
          <div class="preview-section" style="margin-top: 16px; height: 700px">
            <h2 class="section-title">签署文件预览</h2>
            <file-preview
              v-if="rightPreviewFile"
              :oss-path="rightPreviewFile.ossPath"
              :oss-path-name="rightPreviewFile.ossPathName"
              :bucket="rightPreviewFile.bucket"
            />
          </div>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, PropType, h } from 'vue'
import { NIcon, UploadFileInfo, FormRules, NInput, NDatePicker, NButton, NSelect, NTag } from 'naive-ui'
import { AddOutline, ArchiveOutline as ArchiveIcon, ReloadOutline, GitCompareOutline as CompareOutline, TrashOutline as DeleteOutline } from '@vicons/ionicons5'
import { ContainerValueType } from '@/types/enums/enums'
import { IRes } from '@jtypes'
import { queryOrg } from '@/api/hrm/hrmOrg'
import { queryContractType } from '@/api/cms/contractType'
import { queryCmsContract } from '@/api/cms/contractManage/ContractWeb'
import { queryCmsContractDetail } from '@/api/cms/contractManage/ContractDetailWeb'
import { executeAcceptance, submitAcceptanceApply } from '@/api/cms/contractAcceptance/index'
import FilePreview from '../../contractDraft/components/filePreview.vue'
import JPGlobal from '@jutil'

// Props
const props = defineProps({
  contractData: {
    type: Object as PropType<any>,
    required: true
  },
  isEdit: {
    type: Boolean,
    default: true
  }
})

// Emits
const emits = defineEmits(['close', 'success'])

// 基础数据
const contractInfo = ref(props.contractData || {})
const orgList = ref<any[]>([])
const contractTypeList = ref<any[]>([])

// 文件上传
const fileList = ref<UploadFileInfo[]>([])
const attachmentList = ref<any[]>([])

// 加载状态
const submitting = ref(false)

// 文件预览
const showPreview = ref(false)
const previewPath = ref('')
const previewName = ref('')
const previewBucket = ref('cms')

// 验收申请表单
const acceptanceForm = ref({
  contractId: contractInfo.value.id,
  applyId: null, // 验收申请ID - 必填字段
  acceptanceType: 'FINAL', // MILESTONE-里程碑验收, FINAL-最终验收, STAGE-阶段验收
  acceptanceDate: null, // 更改为验收日期
  acceptancePerson: '',
  acceptanceDept: '',
  acceptanceResult: 'PASS', // 验收结果：PASS-通过，FAIL-不通过，CONDITIONAL-有条件通过
  needThirdParty: false,
  thirdPartyOrg: '',
  acceptanceDesc: '',
  linkedElementId: '', // 关联的验收要素ID
  relatedBusiness: '' // 关联业务
})

// 验收类型选项
const acceptanceTypeOptions = [
  { label: '最终验收', value: 'FINAL' },
  { label: '里程碑验收', value: 'MILESTONE' },
  { label: '阶段验收', value: 'STAGE' }
]

// 验收结果选项
const acceptanceResultOptions = [
  { label: '验收通过', value: 'PASS' },
  { label: '验收不通过', value: 'FAIL' },
  { label: '有条件通过', value: 'CONDITIONAL' }
]

// 关联业务选项
const relatedBusinessOptions = [
  { label: '不关联业务', value: '0' },
  { label: '固定资产入库', value: '1' },
  { label: '物资耗材入库', value: '2' }
]

// 表单验证规则
const formRules: FormRules = {
  acceptanceType: [{ required: true, message: '请选择验收类型' }],
  acceptanceDate: [{ required: true, message: '请选择验收日期' }],
  acceptancePerson: [{ required: true, message: '请选择验收负责人' }],
  acceptanceDept: [{ required: true, message: '请选择验收科室' }],
  acceptanceResult: [{ required: true, message: '请选择验收结果' }],
  relatedBusiness: [{ required: true, message: '请选择关联业务' }]
}

// 合同附件相关
const contractOriginalFiles = ref<any[]>([])
const contractSignedFiles = ref<any[]>([])

// 合同原件表格列配置
const originalFileColumns = [
  {
    title: '文件名称',
    key: 'label',
    render: (row: any) => {
      return h('span', { style: { color: '#333', fontWeight: '500' } }, row.label)
    }
  },
  {
    title: '操作',
    key: 'operation',
    width: 100,
    align: 'center' as const,
    render: (row: any) => {
      return h(
        NButton,
        {
          text: true,
          type: 'primary',
          size: 'small',
          onClick: () => previewOriginalFile(row)
        },
        { 
          default: () => [
            h('i', { class: 'icon-preview', style: { marginRight: '4px' } }),
            '预览'
          ]
        }
      )
    }
  }
]

// 已签合同附件表格列配置
const signedFileColumns = [
  {
    title: '文件名称',
    key: 'label',
    render: (row: any) => {
      return h('span', { style: { color: '#333', fontWeight: '500' } }, row.label)
    }
  },
  {
    title: '操作',
    key: 'operation',
    width: 100,
    align: 'center' as const,
    render: (row: any) => {
      return h(
        NButton,
        {
          text: true,
          type: 'primary',
          size: 'small',
          onClick: () => previewSignedFile(row)
        },
        { 
          default: () => [
            h('i', { class: 'icon-preview', style: { marginRight: '4px' } }),
            '预览'
          ]
        }
      )
    }
  }
]

// 预览合同原件
const previewOriginalFile = (file: any) => {
  console.log('预览合同原件:', file)
  if (file.value.includes('originalDraftOfContract/')) {
    // 原件文件使用 cms bucket
    previewPath.value = file.value
    previewName.value = file.label
    previewBucket.value = 'cms'
    showPreview.value = true
  } else {
    // 其他文件使用 temp bucket  
    previewPath.value = file.value
    previewName.value = file.label
    previewBucket.value = 'temp'
    showPreview.value = true
  }
}

// 预览已签合同附件
const previewSignedFile = (file: any) => {
  console.log('预览已签合同附件:', file)
  // 已签合同附件使用 cms bucket
  previewPath.value = file.value
  previewName.value = file.label
  previewBucket.value = 'cms'
  showPreview.value = true
}

// 加载合同附件数据
const loadContractFiles = async () => {
  try {
    // 获取合同ID，优先使用props.contractData.id
    const contractId = props.contractData?.id || contractInfo.value.id
    console.log('开始加载合同附件')
    console.log('props.contractData:', props.contractData)
    console.log('contractInfo.value:', contractInfo.value)
    console.log('使用的合同ID:', contractId)
    
    if (!contractId) {
      console.error('无法获取合同ID')
      window.$message.error('无法获取合同ID，请检查数据传递')
      return
    }
    
    // 获取合同基本信息（包含原件信息）
    console.log('调用queryCmsContract，参数:', { id: contractId })
    const contractRes = await queryCmsContract({ id: contractId })
    console.log('合同基本信息查询结果:', contractRes)
    
    if ( contractRes.data ) {
      const contract = contractRes.data[0]
      console.log('合同数据:', contract)
      
      // 处理合同原件
      if (contract.att && contract.attName) {
        console.log('原件att:', contract.att, 'attName:', contract.attName)
        const atts = contract.att.split(',').filter(Boolean) // 过滤空值
        const attNames = contract.attName.split(',').filter(Boolean) // 过滤空值
        
        contractOriginalFiles.value = []
        atts.forEach((att: string, index: number) => {
          if (att && attNames[index]) {
            contractOriginalFiles.value.push({
              value: att.trim(), // 去除空格
              label: attNames[index].trim() // 去除空格
            })
          }
        })
        console.log('合同原件文件:', contractOriginalFiles.value)
      } else {
        console.log('无合同原件数据，att:', contract.att, 'attName:', contract.attName)
      }
    } else {
      console.log('查询合同基本信息失败或无数据，response:', contractRes)
    }

    // 获取已签合同附件 - 使用applyId参数
    console.log('调用queryCmsContractDetail，参数:', { applyId: contractId })
    const detailRes = await queryCmsContractDetail({ applyId: contractId })
    console.log('合同详情查询结果:', detailRes)
    
    if (detailRes.data && detailRes.data.length > 0) {
      contractSignedFiles.value = []
      
      detailRes.data.forEach((item: any) => {
        console.log('合同详情项:', item)
        if (item.att && item.attName) {
          const atts = item.att.split(',').filter(Boolean) // 过滤空值
          const attNames = item.attName.split(',').filter(Boolean) // 过滤空值
          
          atts.forEach((att: string, index: number) => {
            if (att && attNames[index]) {
              contractSignedFiles.value.push({
                value: att.trim(), // 去除空格
                label: attNames[index].trim(), // 去除空格
                placeDetail: item.placeDetail || '',
                positionOrg: item.positionOrg || ''
              })
            }
          })
        }
      })
      console.log('已签合同附件:', contractSignedFiles.value)
    } else {
      console.log('无已签合同附件数据，response:', detailRes)
    }
  } catch (error) {
    console.error('加载合同附件失败:', error)
    window.$message.error('加载合同附件失败：' + (error instanceof Error ? error.message : String(error)))
  }
}

// 初始化数据
onMounted(async () => {
  console.log('goCheckNAccept组件初始化')
  console.log('传入的合同数据:', props.contractData)
  console.log('合同ID:', contractInfo.value.id)

  // 设置applyId（如果是从验收申请进入的）
  if (props.contractData?.applyId) {
    acceptanceForm.value.applyId = props.contractData.applyId
  }

  await loadInitData()
  await loadContractFiles()

  // 初始化验收要素数据 - 支持从合同数据中回显
  const existingElements = props.contractData?.acceptanceElements || []
  initAcceptanceElements(existingElements)
})

// 加载初始化数据
const loadInitData = async () => {
  try {
    // 加载组织架构
    const orgRes = await queryOrg({ activeFlag: '1' })
    if (orgRes.code === '200') {
      orgList.value = orgRes.data
    }

    // 加载合同类型
    const typeRes = await queryContractType({})
    if (typeRes.code === '200') {
      contractTypeList.value = typeRes.data
    }
  } catch (error) {
    console.error('加载初始化数据失败:', error)
  }
}

// 获取合同类型名称
const getContractTypeName = (typeCode: string) => {
  const type = contractTypeList.value.find(item => item.code === typeCode)
  return type ? type.name : typeCode
}

// 获取组织名称
const getOrgName = (orgId: string) => {
  const org = orgList.value.find(item => item.orgId === orgId)
  return org ? org.orgName : orgId
}

// 文件上传变化
const fileChange = (files: UploadFileInfo[]) => {
  fileList.value = files
}

// 移除附件
const removeAttachment = (index: number) => {
  attachmentList.value.splice(index, 1)
}

// 预览附件
const previewAttachment = (item: any) => {
  previewPath.value = item.att
  previewName.value = item.attName
  previewBucket.value = 'cms'
  showPreview.value = true
}

// 提交验收申请
const submitAcceptance = async () => {
  // 验证applyId是否存在
  if (!acceptanceForm.value.applyId) {
    window.$message.error('请先创建验收申请，再执行验收')
    return
  }

  // 表单验证
  const formRef = ref()
  try {
    await formRef.value?.validate()
  } catch (error) {
    window.$message.error('请完善表单信息')
    return
  }

  // 验收要素验证
  if (acceptanceForm.value.acceptanceType === 'MILESTONE' && !acceptanceForm.value.linkedElementId) {
    window.$message.error('里程碑验收必须选择关联的验收要素')
    return
  }

  // 验证验收要素完整性
  if (elementsData.value.length > 0) {
    const incompleteElement = elementsData.value.find(
      element => !element.keyElement || !element.nodeRequire || !element.timeNode
    )
    if (incompleteElement) {
      window.$message.error('请完善验收计划信息，确保所有字段都已填写')
      return
    }
  }

  submitting.value = true
  
  try {
    // 构建提交数据
    const formData = new FormData()
    
    // 添加基本表单数据
    Object.keys(acceptanceForm.value).forEach(key => {
      formData.append(key, (acceptanceForm.value as any)[key])
    })
    
    // 添加验收要素数据
    if (elementsData.value.length > 0) {
      formData.append('elementsData', JSON.stringify(elementsData.value))
    }
    
    // 添加附件
    fileList.value.forEach((file, index) => {
      if (file.file) {
        formData.append(`attFiles[${index}]`, file.file)
      }
    })

    // 先创建验收申请，再执行验收
    try {
      // 1. 先创建验收申请
      const applyFormData = new FormData()
      applyFormData.append('contractId', contractInfo.value.id)
      applyFormData.append('contractName', contractInfo.value.ctName || '')
      applyFormData.append('contractCode', contractInfo.value.ctCode || '')
      applyFormData.append('acceptanceType', acceptanceForm.value.acceptanceType)
      applyFormData.append('applyDate', new Date().toISOString().split('T')[0])
      applyFormData.append('acceptanceDate', acceptanceForm.value.acceptanceDate || new Date().toISOString().split('T')[0])
      applyFormData.append('applyReason', acceptanceForm.value.acceptanceDesc || '合同验收申请')
      applyFormData.append('hospitalId', 'zjxrmyy')

      // 添加必要的申请人信息
      applyFormData.append('applicantOrg', 'DEPT001')
      applyFormData.append('applicantOrgName', '合同管理科')
      applyFormData.append('applicant', 'USER001')
      applyFormData.append('applicantName', '系统用户')

      // 添加验收要素数据
      if (elementsData.value.length > 0) {
        applyFormData.append('elementsData', JSON.stringify(elementsData.value))
      }

      // 调用验收申请API
      const applyRes = await submitAcceptanceApply(applyFormData)
      if (applyRes.code !== '200') {
        window.$message.error('创建验收申请失败：' + applyRes.message)
        return
      }

      // 2. 再执行验收
      formData.append('applyId', applyRes.data?.id || '1')

      const res = await executeAcceptance(formData)
      if (res.code === '200') {
        window.$message.success('验收记录保存成功')
        emits('success')
        closeModal()
      } else {
        window.$message.error('验收记录保存失败：' + res.message)
      }
    } catch (error) {
      console.error('验收流程执行失败:', error)
      window.$message.error('验收流程执行失败，请重试')
    }
    
  } catch (error) {
    console.error('保存验收记录失败:', error)
    window.$message.error('保存失败')
  } finally {
    submitting.value = false
  }
}

// 关闭弹窗
const closeModal = () => {
  emits('close')
}

// 导出方法供父组件调用
defineExpose({
  submitAcceptance
})

// 调试合同数据
const debugContractData = () => {
  console.log('调试合同数据:', contractInfo.value)
  console.log('调试合同附件数据:', { contractOriginalFiles: contractOriginalFiles.value, contractSignedFiles: contractSignedFiles.value })
}

// 抽屉控制
const showCompareDrawer = ref(false)
const drawerWidth = ref(1650)
const drawerHeight = ref(window.innerHeight)

// 备注内容
const compareNote = ref('')

// 选中的行
const originalChecked = ref<string[]>([])
const signedChecked = ref<string[]>([])

// 预览文件信息
const leftPreviewFile = ref<{
  ossPath: string
  ossPathName: string
  bucket: string
} | null>(null)

const rightPreviewFile = ref<{
  ossPath: string
  ossPathName: string
  bucket: string
} | null>(null)

// 对比用的表格列配置
const originalCompareColumns = computed(() => [
  { 
    type: 'selection' as const, 
    width: 50 
  },
  { 
    title: '序号', 
    key: 'index', 
    width: 80, 
    render: (_row: any, index: number) => index + 1 
  },
  { 
    title: '文件名称', 
    key: 'label', 
    width: 200 
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    render: (row: any) => {
      return h(
        NButton,
        {
          text: true,
          type: 'primary',
          onClick: () => previewOriginalFileInCompare(row),
        },
        { default: () => '预览' }
      )
    },
  },
])

const signedCompareColumns = computed(() => [
  { 
    type: 'selection' as const, 
    width: 50 
  },
  { 
    title: '序号', 
    key: 'index', 
    width: 80, 
    render: (_row: any, index: number) => index + 1 
  },
  { 
    title: '文件名称', 
    key: 'label', 
    width: 200 
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    render: (row: any) => {
      return h(
        NButton,
        {
          text: true,
          type: 'primary',
          onClick: () => previewSignedFileInCompare(row),
        },
        { default: () => '预览' }
      )
    },
  },
])

// 处理抽屉宽度变化
const handleWidthChange = (width: number) => {
  drawerWidth.value = width
}

const handleHeightChange = (height: number) => {
  drawerHeight.value = height
}

// 处理选择
const handleOriginalCheck = (keys: string[]) => {
  originalChecked.value = keys
}

const handleSignedCheck = (keys: string[]) => {
  signedChecked.value = keys
}

// 重置选择
const resetSelection = () => {
  originalChecked.value = []
  signedChecked.value = []
  leftPreviewFile.value = null
  rightPreviewFile.value = null
}

// 预览原始文件（对比用）
const previewOriginalFileInCompare = (file: any) => {
  console.log('previewOriginalFileInCompare', file)
  leftPreviewFile.value = {
    ossPath: file.value,
    ossPathName: file.label,
    bucket: file.value.includes('originalDraftOfContract/') ? 'cms' : 'temp',
  }
}

// 预览签署文件（对比用）
const previewSignedFileInCompare = (file: any) => {
  console.log('previewSignedFileInCompare', file)
  rightPreviewFile.value = {
    ossPath: file.value,
    ossPathName: file.label,
    bucket: file.value.includes('signedContractFile/') ? 'cms' : 'temp',
  }
}

// 开始对比
const startCompare = () => {
  if (originalChecked.value.length === 0 || signedChecked.value.length === 0) {
    window.$message.warning('请先选择需要对比的文件')
    return
  }
  if (originalChecked.value.length > 1 || signedChecked.value.length > 1) {
    window.$message.warning('每次只能对比一对文件')
    return
  }

  // 获取选中的文件
  const originalFile = contractOriginalFiles.value.find(file => file.value === originalChecked.value[0])
  const signedFile = contractSignedFiles.value.find(file => file.value === signedChecked.value[0])

  if (!originalFile || !signedFile) {
    window.$message.error('无法获取选中的文件信息')
    return
  }

  // 预览选中的文件
  previewOriginalFileInCompare(originalFile)
  previewSignedFileInCompare(signedFile)

  window.$message.success('文件加载完成，请查看对比结果')
}

// 打开对比抽屉
const openCompareDrawer = () => {
  if (contractOriginalFiles.value.length === 0 && contractSignedFiles.value.length === 0) {
    window.$message.warning('暂无合同文件可供对比')
    return
  }
  showCompareDrawer.value = true
}

// 关闭对比抽屉
const closeCompareDrawer = () => {
  showCompareDrawer.value = false
  resetSelection()
  compareNote.value = ''
}

// 验收节点数据 - 参考uploadDraftedContract.vue
const elementsData = ref<any[]>([])

// 验收要素节点接口
interface KeyElementNode {
  keyElement: string
  nodeRequire: string
  timeNode: string
  seq: number
}

// 关键要素节点模板
const keyElementNode = {
  keyElement: '',
  nodeRequire: '',
  timeNode: '',
  seq: 0,
}

// 验收要素表格列配置 - 参考uploadDraftedContract.vue
const elementsColumns = computed(() => [
  {
    title: '验收要素',
    key: 'keyElement',
    width: 150,
    render: (row: any) => {
      return h(
        NInput,
        {
          value: row.keyElement,
          placeholder: '请输入验收要素',
          onUpdateValue: (val: string | null) => {
            if (val) {
              row.keyElement = val
            }
          },
        },
        {}
      )
    },
  },
  {
    title: '节点控制要求',
    key: 'nodeRequire',
    width: 200,
    render: (row: any) => {
      return h(
        NInput,
        {
          value: row.nodeRequire,
          placeholder: '请输入节点控制要求',
          onUpdateValue: (val: string | null) => {
            if (val) {
              row.nodeRequire = val
            }
          },
        },
        {}
      )
    },
  },
  {
    title: '时间节点',
    key: 'timeNode',
    width: 150,
    render: (row: any) => {
      return h(
        NDatePicker,
        {
          type: 'date',
          valueFormat: 'yyyy-MM-dd',
          formattedValue: row.timeNode || null,
          clearable: true,
          placeholder: '选择时间节点',
          onUpdateFormattedValue: (val: string | null) => {
            row.timeNode = val || ''
          },
        },
        {}
      )
    },
  },
  {
    title: '操作',
    key: 'operation',
    align: 'center' as const,
    width: 80,
    render: (row: any) => {
      return h(
        NButton,
        {
          text: true,
          type: 'error',
          size: 'small',
          onClick: (e: Event) => {
            e.stopPropagation()
            deleteOneKeyElement(row.seq)
          }
        },
        { 
          default: () => '删除'
        }
      )
    },
  },
])

// 新增关键要素 - 参考uploadDraftedContract.vue
const addKeyElement = () => {
  // 创建一个新的节点对象，并更新seq
  const newNode: KeyElementNode = {
    ...keyElementNode,
    seq: elementsData.value.length + 1, // seq设置为当前数组的长度加1
  }
  // 将新节点添加到数组中
  elementsData.value.push(newNode)
}

// 删除单个要素 - 参考uploadDraftedContract.vue
const deleteOneKeyElement = (seq: number) => {
  // 使用filter方法删除指定seq的元素
  elementsData.value = elementsData.value.filter(element => element.seq !== seq)
  // 使用sort方法重新排序
  elementsData.value.sort((a, b) => a.seq - b.seq)

  // 重置seq值，并按1,2,3,4,5...排序
  let currentSeq = 1
  elementsData.value.forEach(element => {
    element.seq = currentSeq
    currentSeq++
  })
}

// 删除所有要素 - 参考uploadDraftedContract.vue
const clearKeyElements = () => {
  elementsData.value.splice(0, elementsData.value.length)
}

// 验收要素选项（用于里程碑验收选择）
const elementSelectOptions = computed(() => {
  return elementsData.value
    .filter(element => element.keyElement && element.timeNode)
    .map(element => ({
      label: element.keyElement || `要素${element.seq}`,
      value: element.seq.toString()
    }))
})

// 验收类型变化处理
const onAcceptanceTypeChange = (value: string) => {
  // 如果不是里程碑验收，清空关联要素
  if (value !== 'MILESTONE') {
    acceptanceForm.value.linkedElementId = ''
  }
}

// 初始化验收要素数据 - 修改为使用elementsData
const initAcceptanceElements = (existingElements?: any[]) => {
  if (existingElements && existingElements.length > 0) {
    // 回显已有数据
    elementsData.value = existingElements.map((element, index) => ({
      keyElement: element.keyElement || '',
      nodeRequire: element.nodeRequire || '',
      timeNode: element.timeNode || '',
      seq: index + 1
    }))
  } else {
    // 初始化空数据
    elementsData.value = []
  }
}
</script>

<style scoped>
.acceptance-container {
  padding: 20px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.contract-info {
  margin-bottom: 20px;
}

.acceptance-elements {
  margin-bottom: 20px;
}

.acceptance-elements-table {
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.acceptance-elements-table :deep(.n-data-table-th) {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.acceptance-elements-table :deep(.n-data-table-td) {
  border-bottom: 1px solid #e9ecef;
}

.acceptance-elements-table :deep(.n-input) {
  border: none;
  background: transparent;
}

.acceptance-elements-table :deep(.n-input:focus) {
  background: #fff;
  border: 1px solid #409eff;
  border-radius: 4px;
}

.acceptance-elements-table :deep(.n-select) {
  border: none;
  background: transparent;
}

.acceptance-elements-table :deep(.n-date-picker) {
  border: none;
  background: transparent;
  width: 100%;
}

.elements-toolbar {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}

.attachment-list {
  margin-top: 10px;
  padding: 10px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.action-buttons {
  margin-top: 30px;
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
}

.contract-files-container {
  margin-top: 20px;
}

.files-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.file-section {
  flex: 1;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.file-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-section h4[style*="color: #2E8B57"] {
  color: #2E8B57 !important;
}

.file-section h4[style*="color: #CD853F"] {
  color: #CD853F !important;
}

.file-section .n-data-table {
  background: white;
  border-radius: 6px;
  overflow: hidden;
}

/* 当没有文件时的提示 */
.no-files-tip {
  text-align: center;
  color: #999;
  padding: 20px;
  background: #f9f9f9;
  border: 1px dashed #ddd;
  border-radius: 6px;
  margin: 10px 0;
}

/* 预览按钮样式 */
.file-section .n-button {
  font-size: 14px;
  padding: 4px 8px;
}

.comparison-container {
  height: 1000px;
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.file-list-section {
  flex: 1;
  min-width: 300px;
}

.control-section {
  width: 300px;
  padding: 0 16px;
  border-left: 1px solid #e8e8e8;
  border-right: 1px solid #e8e8e8;
}

.control-content {
  position: sticky;
  top: 0;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
}

.note-section {
  margin-top: 40px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.note-section :deep(.n-input) {
  background: #fff;
}

.preview-section {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  height: 800px;
}

.preview-section .section-title {
  margin: 0;
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
}

/* 当没有验收要素时的提示 */
.no-elements-tip {
  text-align: center;
  color: #999;
  padding: 20px;
  background: #f9f9f9;
  border: 1px dashed #ddd;
  border-radius: 6px;
  margin: 10px 0;
}

.exp-space {
  margin: 20px 0;
}
</style>
