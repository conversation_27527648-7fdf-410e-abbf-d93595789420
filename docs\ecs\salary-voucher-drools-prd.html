<!DOCTYPE html><html><head>
      <title>salary-voucher-drools-prd</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:////Users/<USER>/.cursor/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/katex.min.css">
      
      
      <script type="text/javascript" src="file:////Users/<USER>/.cursor/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/mermaid/mermaid.min.js" charset="UTF-8"></script>
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="ecs系统工资凭证模块drools重构prd文档">ECS系统工资凭证模块Drools重构PRD文档 </h1>
<h2 id="1-项目概述-">1. 项目概述 📋 </h2>
<h3 id="11-项目背景">1.1 项目背景 </h3>
<p>中江县人民医院智慧财务系统(ECS)中的工资凭证模块负责处理医院工资相关的财务凭证生成和审批流程。当前系统存在业务规则硬编码、规则维护困难、扩展性差等问题，需要引入Drools规则引擎进行重构优化。</p>
<h3 id="12-项目目标">1.2 项目目标 </h3>
<ul>
<li>🎯 <strong>业务规则外化</strong>：将工资凭证生成的业务规则从代码中抽离，使用Drools规则引擎管理</li>
<li>🔧 <strong>提升可维护性</strong>：业务人员可通过配置界面维护规则，无需修改代码</li>
<li>📈 <strong>增强扩展性</strong>：支持灵活的规则组合和动态规则调整</li>
<li>⚡ <strong>优化性能</strong>：通过规则引擎优化决策执行效率</li>
<li>🛡️ <strong>保证准确性</strong>：确保工资凭证生成的准确性和一致性</li>
</ul>
<h3 id="13-项目范围">1.3 项目范围 </h3>
<ul>
<li>工资凭证生成规则引擎化</li>
<li>工资类型判断规则配置</li>
<li>科目映射规则管理</li>
<li>辅助项配置规则</li>
<li>凭证生成条件规则</li>
<li>规则配置管理界面</li>
</ul>
<h2 id="2-现状分析-">2. 现状分析 🔍 </h2>
<h3 id="21-当前系统架构">2.1 当前系统架构 </h3>
<div class="mermaid">graph TD
    A[工资任务] --&gt; B[工资凭证处理]
    B --&gt; C{工资类型判断}
    C --&gt;|salaryType=1| D[工资计提]
    C --&gt;|salaryType=2| E[工资发放+三险两金]
    C --&gt;|salaryType=3| F[企业四险两金]
    C --&gt;|salaryType=4| G[工会经费]
    D --&gt; H[直接生成凭证]
    E --&gt; I[报销审批流程]
    F --&gt; H
    G --&gt; H
    H --&gt; J[ERP凭证生成]
    I --&gt; K[BPM审批]
    K --&gt; J
</div><h3 id="22-核心数据模型">2.2 核心数据模型 </h3>
<h4 id="221-核心数据模型关系图">2.2.1 核心数据模型关系图 </h4>
<div class="mermaid">erDiagram
    ECS_REIM_SALARY_TASK {
        INTEGER id PK "主键ID"
        INTEGER salary_id "工资任务ID"
        STRING ff_mth "工资发放月份"
        INTEGER num "工资条数"
        DECIMAL should_pay "应发合计汇总"
        DECIMAL reduce_pay "扣款合计汇总"
        DECIMAL real_pay "实发合计汇总"
        STRING salary_type "工资类型(1-4)"
        STRING reim_flag "是否报销标识"
        INTEGER reim_id "报销ID"
        STRING crter "制表人"
        DATETIME crte_time "制表时间"
    }

    ECS_REIM_SALARY_TASK_DETAIL {
        INTEGER id PK "主键ID"
        INTEGER task_id FK "任务ID"
        STRING org_id "科室ID"
        STRING emp_type "人员类型"
        STRING reim_name "报销项目"
        STRING reim_type "报销类型"
        DECIMAL reim_amt "报销金额"
        STRING reim_desc "报销摘要"
        STRING emp_code "员工编号"
        INTEGER emp_count "人数"
    }

    SALARY_RULE_CONFIG {
        INTEGER id PK "主键ID"
        STRING rule_name "规则名称"
        STRING rule_type "规则类型"
        TEXT rule_content "规则内容"
        STRING version "版本号"
        STRING status "状态"
        INTEGER priority "优先级"
        DATETIME create_time "创建时间"
        STRING creator "创建人"
    }

    SALARY_TYPE_RULE {
        INTEGER id PK "主键ID"
        STRING salary_type "工资类型"
        STRING process_type "处理类型"
        STRING template_code "模板代码"
        TEXT description "描述"
        STRING active_flag "激活标识"
    }

    SALARY_SUBJECT_MAPPING {
        INTEGER id PK "主键ID"
        STRING salary_type "工资类型"
        STRING dept_type "科室类型"
        STRING emp_type "人员类型"
        STRING reim_type "报销类型"
        STRING debit_subject "借方科目"
        STRING credit_subject "贷方科目"
        STRING budget_subject "预算科目"
        TEXT auxiliary_config "辅助项配置"
        INTEGER priority "优先级"
    }

    SALARY_SPECIAL_EMPLOYEE {
        INTEGER id PK "主键ID"
        STRING emp_code "员工编号"
        STRING emp_name "员工姓名"
        STRING special_type "特殊类型"
        STRING subject_code "科目代码"
        STRING auxiliary_code "辅助项代码"
        TEXT description "描述"
    }

    ECS_REIM_SALARY_TASK ||--o{ ECS_REIM_SALARY_TASK_DETAIL : "一对多"
    SALARY_TYPE_RULE ||--o{ ECS_REIM_SALARY_TASK : "规则控制"
    SALARY_SUBJECT_MAPPING ||--o{ ECS_REIM_SALARY_TASK_DETAIL : "科目映射"
    SALARY_SPECIAL_EMPLOYEE ||--o{ ECS_REIM_SALARY_TASK_DETAIL : "特殊处理"
</div><h4 id="222-工资任务表详细结构">2.2.2 工资任务表详细结构 </h4>
<blockquote>
<p>📋 <strong>表名</strong>: <code>ecs_reim_salary_task</code><br>
🎯 <strong>用途</strong>: 存储工资任务的汇总信息，是工资凭证生成的主要数据源</p>
</blockquote>
<div class="mermaid">graph TD
    A[🏥 工资任务表&lt;br/&gt;ecs_reim_salary_task] --&gt; B[📊 基础信息]
    A --&gt; C[💰 金额信息]
    A --&gt; D[🔄 流程信息]
    A --&gt; E[📝 审计信息]

    B --&gt; B1[🔑 id&lt;br/&gt;主键ID&lt;br/&gt;Integer]
    B --&gt; B2[🆔 salary_id&lt;br/&gt;工资任务ID&lt;br/&gt;Integer]
    B --&gt; B3[📅 ff_mth&lt;br/&gt;工资发放月份&lt;br/&gt;String]
    B --&gt; B4[📊 num&lt;br/&gt;工资条数&lt;br/&gt;Integer]

    C --&gt; C1[💵 should_pay&lt;br/&gt;应发合计汇总&lt;br/&gt;BigDecimal]
    C --&gt; C2[➖ reduce_pay&lt;br/&gt;扣款合计汇总&lt;br/&gt;BigDecimal]
    C --&gt; C3[💸 real_pay&lt;br/&gt;实发合计汇总&lt;br/&gt;BigDecimal]

    D --&gt; D1[🏷️ salary_type&lt;br/&gt;工资类型&lt;br/&gt;String]
    D --&gt; D2[✅ reim_flag&lt;br/&gt;是否报销标识&lt;br/&gt;String]
    D --&gt; D3[🔗 reim_id&lt;br/&gt;报销ID&lt;br/&gt;Integer]
    D --&gt; D4[📋 type&lt;br/&gt;类型标识&lt;br/&gt;String]

    E --&gt; E1[👤 crter&lt;br/&gt;制表人&lt;br/&gt;String]
    E --&gt; E2[⏰ crte_time&lt;br/&gt;制表时间&lt;br/&gt;DateTime]
    E --&gt; E3[📝 remark&lt;br/&gt;备注&lt;br/&gt;String]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
</div><p><strong>📊 字段详细说明表</strong></p>
<table>
<thead>
<tr>
<th>字段名</th>
<th>类型</th>
<th>长度</th>
<th>必填</th>
<th>说明</th>
<th>示例值</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>id</code></td>
<td>Integer</td>
<td>-</td>
<td>✅</td>
<td>主键ID，自增</td>
<td>1001</td>
</tr>
<tr>
<td><code>salary_id</code></td>
<td>Integer</td>
<td>-</td>
<td>✅</td>
<td>工资任务ID，关联HRM系统</td>
<td>202401001</td>
</tr>
<tr>
<td><code>ff_mth</code></td>
<td>String</td>
<td>10</td>
<td>✅</td>
<td>工资发放月份，格式：YYYY-MM</td>
<td>"2024-01"</td>
</tr>
<tr>
<td><code>num</code></td>
<td>Integer</td>
<td>-</td>
<td>✅</td>
<td>工资条数，统计人员数量</td>
<td>150</td>
</tr>
<tr>
<td><code>should_pay</code></td>
<td>BigDecimal</td>
<td>15,2</td>
<td>✅</td>
<td>应发合计汇总</td>
<td>1500000.00</td>
</tr>
<tr>
<td><code>reduce_pay</code></td>
<td>BigDecimal</td>
<td>15,2</td>
<td>✅</td>
<td>扣款合计汇总</td>
<td>300000.00</td>
</tr>
<tr>
<td><code>real_pay</code></td>
<td>BigDecimal</td>
<td>15,2</td>
<td>✅</td>
<td>实发合计汇总</td>
<td>1200000.00</td>
</tr>
<tr>
<td><code>salary_type</code></td>
<td>String</td>
<td>2</td>
<td>✅</td>
<td><strong>工资类型</strong>：1-工资计提，2-工资发放，3-企业四险两金，4-工会经费</td>
<td>"1"</td>
</tr>
<tr>
<td><code>reim_flag</code></td>
<td>String</td>
<td>1</td>
<td>✅</td>
<td>是否报销标识：0-未报销，1-已报销</td>
<td>"0"</td>
</tr>
<tr>
<td><code>reim_id</code></td>
<td>Integer</td>
<td>-</td>
<td>❌</td>
<td>报销ID，关联报销流程</td>
<td>3001</td>
</tr>
<tr>
<td><code>type</code></td>
<td>String</td>
<td>10</td>
<td>❌</td>
<td>类型标识</td>
<td>"SALARY"</td>
</tr>
<tr>
<td><code>crter</code></td>
<td>String</td>
<td>50</td>
<td>✅</td>
<td>制表人</td>
<td>"张三"</td>
</tr>
<tr>
<td><code>crte_time</code></td>
<td>DateTime</td>
<td>-</td>
<td>✅</td>
<td>制表时间</td>
<td>"2024-01-15 10:30:00"</td>
</tr>
<tr>
<td><code>remark</code></td>
<td>String</td>
<td>500</td>
<td>❌</td>
<td>备注信息</td>
<td>"2024年1月工资计提"</td>
</tr>
</tbody>
</table>
<h4 id="223-工资任务明细表详细结构">2.2.3 工资任务明细表详细结构 </h4>
<blockquote>
<p>📋 <strong>表名</strong>: <code>ecs_reim_salary_task_detail</code><br>
🎯 <strong>用途</strong>: 存储工资任务的明细信息，包含具体的工资项目和金额</p>
</blockquote>
<div class="mermaid">graph TD
    A[📄 工资任务明细表&lt;br/&gt;ecs_reim_salary_task_detail] --&gt; B[🔗 关联信息]
    A --&gt; C[🏢 组织信息]
    A --&gt; D[👥 员工信息]
    A --&gt; E[💼 报销信息]

    B --&gt; B1[🔑 id&lt;br/&gt;主键ID&lt;br/&gt;Integer]
    B --&gt; B2[🔗 task_id&lt;br/&gt;任务ID&lt;br/&gt;Integer FK]

    C --&gt; C1[🏢 org_id&lt;br/&gt;科室ID&lt;br/&gt;String]
    C --&gt; C2[🏥 org_name&lt;br/&gt;科室名称&lt;br/&gt;String]
    C --&gt; C3[📊 dept_type&lt;br/&gt;科室类型&lt;br/&gt;String]

    D --&gt; D1[👤 emp_code&lt;br/&gt;员工编号&lt;br/&gt;String]
    D --&gt; D2[📝 emp_name&lt;br/&gt;员工姓名&lt;br/&gt;String]
    D --&gt; D3[🏷️ emp_type&lt;br/&gt;人员类型&lt;br/&gt;String]
    D --&gt; D4[📊 emp_count&lt;br/&gt;人数&lt;br/&gt;Integer]

    E --&gt; E1[📋 reim_name&lt;br/&gt;报销项目&lt;br/&gt;String]
    E --&gt; E2[🏷️ reim_type&lt;br/&gt;报销类型&lt;br/&gt;String]
    E --&gt; E3[💰 reim_amt&lt;br/&gt;报销金额&lt;br/&gt;BigDecimal]
    E --&gt; E4[📝 reim_desc&lt;br/&gt;报销摘要&lt;br/&gt;String]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
</div><p><strong>📊 字段详细说明表</strong></p>
<table>
<thead>
<tr>
<th>字段名</th>
<th>类型</th>
<th>长度</th>
<th>必填</th>
<th>说明</th>
<th>示例值</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>id</code></td>
<td>Integer</td>
<td>-</td>
<td>✅</td>
<td>主键ID，自增</td>
<td>10001</td>
</tr>
<tr>
<td><code>task_id</code></td>
<td>Integer</td>
<td>-</td>
<td>✅</td>
<td>任务ID，外键关联工资任务表</td>
<td>1001</td>
</tr>
<tr>
<td><code>org_id</code></td>
<td>String</td>
<td>20</td>
<td>✅</td>
<td>科室ID</td>
<td>"1001"</td>
</tr>
<tr>
<td><code>org_name</code></td>
<td>String</td>
<td>100</td>
<td>❌</td>
<td>科室名称</td>
<td>"内科"</td>
</tr>
<tr>
<td><code>dept_type</code></td>
<td>String</td>
<td>10</td>
<td>✅</td>
<td><strong>科室类型</strong>：1-业务科室，2-管理科室</td>
<td>"1"</td>
</tr>
<tr>
<td><code>emp_type</code></td>
<td>String</td>
<td>20</td>
<td>✅</td>
<td><strong>人员类型</strong>：在编、招聘、临聘、借调、返聘</td>
<td>"在编"</td>
</tr>
<tr>
<td><code>reim_name</code></td>
<td>String</td>
<td>100</td>
<td>✅</td>
<td>报销项目名称</td>
<td>"岗位工资"</td>
</tr>
<tr>
<td><code>reim_type</code></td>
<td>String</td>
<td>50</td>
<td>✅</td>
<td><strong>报销类型</strong>：具体的工资项目类型</td>
<td>"postSalary"</td>
</tr>
<tr>
<td><code>reim_amt</code></td>
<td>BigDecimal</td>
<td>15,2</td>
<td>✅</td>
<td>报销金额</td>
<td>50000.00</td>
</tr>
<tr>
<td><code>reim_desc</code></td>
<td>String</td>
<td>200</td>
<td>❌</td>
<td>报销摘要</td>
<td>"2024年1月岗位工资"</td>
</tr>
<tr>
<td><code>emp_code</code></td>
<td>String</td>
<td>20</td>
<td>❌</td>
<td>员工编号（个人扣款时必填）</td>
<td>"EMP001"</td>
</tr>
<tr>
<td><code>emp_name</code></td>
<td>String</td>
<td>50</td>
<td>❌</td>
<td>员工姓名</td>
<td>"李四"</td>
</tr>
<tr>
<td><code>emp_count</code></td>
<td>Integer</td>
<td>-</td>
<td>❌</td>
<td>人数统计</td>
<td>10</td>
</tr>
</tbody>
</table>
<h4 id="224-工资类型分类图">2.2.4 工资类型分类图 </h4>
<div class="mermaid">graph LR
    A[💼 工资类型分类] --&gt; B[1️⃣ 工资计提]
    A --&gt; C[2️⃣ 工资发放]
    A --&gt; D[3️⃣ 企业四险两金]
    A --&gt; E[4️⃣ 工会经费]

    B --&gt; B1[📊 直接生成凭证]
    B --&gt; B2[🏢 按科室分类]
    B --&gt; B3[👥 按人员类型]

    C --&gt; C1[🔄 报销审批流程]
    C --&gt; C2[✅ 个人扣款配置检查]
    C --&gt; C3[🏦 银行存款发放]

    D --&gt; D1[📊 直接生成凭证]
    D --&gt; D2[🛡️ 五险一金]
    D --&gt; D3[💼 职业年金]

    E --&gt; E1[📊 直接生成凭证]
    E --&gt; E2[🏛️ 工会费用]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
</div><h3 id="23-当前业务规则分析">2.3 当前业务规则分析 </h3>
<h4 id="231-工资类型处理规则">2.3.1 工资类型处理规则 </h4>
<p>根据系统实际代码分析，工资类型(salaryType)定义如下：</p>
<ul>
<li><strong>salaryType=1</strong>：工资计提 → 直接生成凭证</li>
<li><strong>salaryType=2</strong>：工资发放+个人三险两金代扣 → 走报销审批流程</li>
<li><strong>salaryType=3</strong>：企业四险两金计提 → 直接生成凭证</li>
<li><strong>salaryType=4</strong>：工会经费 → 直接生成凭证</li>
</ul>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token comment">// 当前硬编码的业务逻辑（来自前端代码分析）</span>
<span class="token keyword keyword-switch">switch</span> <span class="token punctuation">(</span>row<span class="token punctuation">.</span>salaryType<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-case">case</span> <span class="token char">'1'</span><span class="token operator">:</span> <span class="token keyword keyword-return">return</span> <span class="token char">'工资计提'</span>           <span class="token comment">// 直接生成凭证</span>
    <span class="token keyword keyword-case">case</span> <span class="token char">'2'</span><span class="token operator">:</span> <span class="token keyword keyword-return">return</span> '工资发放<span class="token operator">+</span>三险两金'   <span class="token comment">// 报销审批流程</span>
    <span class="token keyword keyword-case">case</span> <span class="token char">'3'</span><span class="token operator">:</span> <span class="token keyword keyword-return">return</span> <span class="token char">'企业四险两金'</span>       <span class="token comment">// 直接生成凭证</span>
    <span class="token keyword keyword-case">case</span> <span class="token char">'4'</span><span class="token operator">:</span> <span class="token keyword keyword-return">return</span> <span class="token char">'工会经费'</span>          <span class="token comment">// 直接生成凭证</span>
<span class="token punctuation">}</span>

<span class="token comment">// 个人代扣类型工资凭证特殊处理逻辑</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>row<span class="token punctuation">.</span>salaryType <span class="token operator">==</span> <span class="token char">'2'</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// 判断个人扣款是否都配置了配置项,未配置需先配置</span>
    <span class="token keyword keyword-const">const</span> indiToConfig <span class="token operator">=</span> await <span class="token function">queryToConfigSalary</span><span class="token punctuation">(</span><span class="token punctuation">{</span>id<span class="token operator">:</span> checkedRowKeys<span class="token punctuation">.</span>value<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">}</span><span class="token punctuation">)</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>indiToConfig<span class="token punctuation">.</span>code <span class="token operator">==</span> <span class="token number">200</span> <span class="token operator">&amp;&amp;</span> indiToConfig<span class="token punctuation">.</span>data<span class="token punctuation">.</span>length <span class="token operator">&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// 弹出界面进行个人扣款配置</span>
        <span class="token keyword keyword-return">return</span> <span class="token string">"需要配置个人扣款"</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h4 id="232-详细业务规则分类">2.3.2 详细业务规则分类 </h4>
<h5 id="一-工资计提规则salarytype1">一、工资计提规则(salaryType=1) </h5>
<p><strong>🏥 人员类型分类图</strong></p>
<div class="mermaid">graph TD
    A[👥 人员类型分类] --&gt; B[📋 在编人员]
    A --&gt; C[🎯 招聘人员]
    A --&gt; D[⏰ 临聘人员]
    A --&gt; E[🔄 借调人员]
    A --&gt; F[🔙 返聘人员]

    B --&gt; B1[在编]
    B --&gt; B2[血防占编]

    C --&gt; C1[编外-医技]
    C --&gt; C2[编外-护理]
    C --&gt; C3[编外-辅助岗位]
    C --&gt; C4[编外-医技-见习]
    C --&gt; C5[编外-护理-见习]
    C --&gt; C6[编外-辅助岗位-见习]

    D --&gt; D1[编外-其他专技]
    D --&gt; D2[编外-后勤]
    D --&gt; D3[编外-其他专技-见习]
    D --&gt; D4[编外-后勤-见习]

    E --&gt; E1[借调]

    F --&gt; F1[返聘]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
    style F fill:#f1f8e9
</div><p><strong>🏢 科室类型与工资项目映射图</strong></p>
<div class="mermaid">graph LR
    A[🏥 科室类型] --&gt; B[🏥 业务科室&lt;br/&gt;deptType=1]
    A --&gt; C[🏢 管理科室&lt;br/&gt;deptType=2]

    B --&gt; B1[👥 在编/招聘人员]
    B --&gt; B2[⏰ 临聘人员]

    C --&gt; C1[📋 在编人员]
    C --&gt; C2[🎯 招聘人员]
    C --&gt; C3[⏰ 临聘人员]

    B1 --&gt; B1A[💰 岗位工资]
    B1 --&gt; B1B[📊 薪级工资]
    B1 --&gt; B1C[👩‍⚕️ 护士10%]
    B1 --&gt; B1D[🌍 地区附加津贴]
    B1 --&gt; B1E[⏰ 护龄补贴]
    B1 --&gt; B1F[🎯 基础绩效工资]
    B1 --&gt; B1G[➕ 人力临时增加]
    B1 --&gt; B1H[💼 财务临时增加]

    B2 --&gt; B2A[💰 岗位工资]
    B2 --&gt; B2B[➕ 人力临时增加]
    B2 --&gt; B2C[💼 财务临时增加]

    C1 --&gt; C1A[💰 岗位工资]
    C1 --&gt; C1B[📊 薪级工资]
    C1 --&gt; C1C[👩‍⚕️ 护士10%]
    C1 --&gt; C1D[🌍 地区附加津贴]
    C1 --&gt; C1E[⏰ 护龄补贴]
    C1 --&gt; C1F[🎯 基础绩效工资]
    C1 --&gt; C1G[➕ 人力临时增加]
    C1 --&gt; C1H[💼 财务临时增加]

    C2 --&gt; C2A[💰 岗位工资]
    C2 --&gt; C2B[📊 薪级工资]
    C2 --&gt; C2C[👩‍⚕️ 护士10%]
    C2 --&gt; C2D[🌍 地区附加津贴]
    C2 --&gt; C2E[⏰ 护龄补贴]
    C2 --&gt; C2F[🎯 基础绩效工资]
    C2 --&gt; C2G[➕ 人力临时增加]
    C2 --&gt; C2H[💼 财务临时增加]

    C3 --&gt; C3A[💰 岗位工资]
    C3 --&gt; C3B[➕ 人力临时增加]
    C3 --&gt; C3C[💼 财务临时增加]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
</div><p>根据系统常量定义(EcsConst.java)，人员类型分类如下：</p>
<ul>
<li><strong>在编人员</strong>：<code>["在编","血防占编"]</code></li>
<li><strong>招聘人员</strong>：<code>["编外-医技","编外-护理","编外-辅助岗位","编外-医技-见习","编外-护理-见习","编外-辅助岗位-见习"]</code></li>
<li><strong>临聘人员</strong>：<code>["编外-其他专技","编外-后勤","编外-其他专技-见习","编外-后勤-见习"]</code></li>
<li><strong>借调人员</strong>：<code>["借调"]</code></li>
<li><strong>返聘人员</strong>：<code>["返聘"]</code></li>
</ul>
<h5 id="二-企业四险两金计提规则salarytype3">二、企业四险两金计提规则(salaryType=3) </h5>
<p><strong>🛡️ 企业四险两金分类图</strong></p>
<div class="mermaid">graph TD
    A[🏢 企业四险两金] --&gt; B[🛡️ 社会保险]
    A --&gt; C[🏠 住房保障]
    A --&gt; D[💼 补充保障]
    A --&gt; E[🏛️ 其他费用]

    B --&gt; B1[👴 养老保险]
    B --&gt; B2[🏥 医疗保险]
    B --&gt; B3[💼 失业保险]
    B --&gt; B4[⚠️ 工伤保险]

    C --&gt; C1[🏠 住房公积金]

    D --&gt; D1[💰 职业年金]

    E --&gt; E1[🏛️ 工会经费]

    B1 --&gt; B1A[机关事业单位基本养老保险缴费&lt;br/&gt;pensionInsuranceEntp]
    B1 --&gt; B1B[职工企业基本养老保险缴费&lt;br/&gt;pensionInsuranceEntp2]

    B2 --&gt; B2A[职工基本医疗保险缴费&lt;br/&gt;medicalInsuranceEntp]

    B3 --&gt; B3A[职工失业保险缴费&lt;br/&gt;unemploymentInsuranceEntp]

    B4 --&gt; B4A[职工工伤保险缴费&lt;br/&gt;injrInsuEntp]

    C1 --&gt; C1A[住房公积金&lt;br/&gt;housingFundEntp]

    D1 --&gt; D1A[职业年金缴费&lt;br/&gt;occupationalAnnuityEntp]

    E1 --&gt; E1A[工会经费支出&lt;br/&gt;pubFeeEntp]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
</div><p><strong>💰 企业缴费项目明细表</strong></p>
<table>
<thead>
<tr>
<th>保险类型</th>
<th>项目名称</th>
<th>常量名称</th>
<th>科目代码</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>🛡️ <strong>社会保险</strong></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>👴 养老保险</td>
<td>机关事业单位基本养老保险缴费</td>
<td><code>pensionInsuranceEntp</code></td>
<td>********</td>
<td>事业单位人员</td>
</tr>
<tr>
<td>👴 养老保险</td>
<td>职工企业基本养老保险缴费</td>
<td><code>pensionInsuranceEntp2</code></td>
<td>********</td>
<td>企业职工</td>
</tr>
<tr>
<td>🏥 医疗保险</td>
<td>职工基本医疗保险缴费</td>
<td><code>medicalInsuranceEntp</code></td>
<td>********</td>
<td>基本医疗保险</td>
</tr>
<tr>
<td>💼 失业保险</td>
<td>职工失业保险缴费</td>
<td><code>unemploymentInsuranceEntp</code></td>
<td>********</td>
<td>失业保险</td>
</tr>
<tr>
<td>⚠️ 工伤保险</td>
<td>职工工伤保险缴费</td>
<td><code>injrInsuEntp</code></td>
<td>22110306</td>
<td>工伤保险</td>
</tr>
<tr>
<td>🏠 <strong>住房保障</strong></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>🏠 住房公积金</td>
<td>住房公积金</td>
<td><code>housingFundEntp</code></td>
<td>221104</td>
<td>住房公积金</td>
</tr>
<tr>
<td>💼 <strong>补充保障</strong></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>💰 职业年金</td>
<td>职业年金缴费</td>
<td><code>occupationalAnnuityEntp</code></td>
<td>********</td>
<td>职业年金</td>
</tr>
<tr>
<td>🏛️ <strong>其他费用</strong></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>🏛️ 工会经费</td>
<td>工会经费支出</td>
<td><code>pubFeeEntp</code></td>
<td>224199</td>
<td>工会经费</td>
</tr>
</tbody>
</table>
<p>根据系统常量定义，企业缴纳项目包括：</p>
<ul>
<li>机关事业单位基本养老保险缴费 (pensionInsuranceEntp)</li>
<li>职工企业基本养老保险缴费 (pensionInsuranceEntp2)</li>
<li>职工基本医疗保险缴费 (medicalInsuranceEntp)</li>
<li>职工失业保险缴费 (unemploymentInsuranceEntp)</li>
<li>职工工伤保险缴费 (injrInsuEntp)</li>
<li>住房公积金 (housingFundEntp)</li>
<li>职业年金缴费 (occupationalAnnuityEntp)</li>
<li>工会经费支出 (pubFeeEntp)</li>
</ul>
<h5 id="三-工资发放个人三险两金代扣规则salarytype2">三、工资发放+个人三险两金代扣规则(salaryType=2) </h5>
<p><strong>👤 个人代扣项目分类图</strong></p>
<div class="mermaid">graph TD
    A[👤 个人代扣项目] --&gt; B[🛡️ 社会保险]
    A --&gt; C[💰 税费]
    A --&gt; D[🏠 生活费用]
    A --&gt; E[⏰ 临时扣款]

    B --&gt; B1[👴 养老保险&lt;br/&gt;pensionInsurance]
    B --&gt; B2[🏥 医疗保险&lt;br/&gt;medicalInsurance]
    B --&gt; B3[💼 失业保险&lt;br/&gt;unemploymentInsurance]
    B --&gt; B4[🏠 住房基金&lt;br/&gt;housingFund]
    B --&gt; B5[💰 职业年金&lt;br/&gt;occupationalAnnuity]

    C --&gt; C1[💰 个人所得税&lt;br/&gt;personalIncomeTaxDeduction]

    D --&gt; D1[🏠 房租费&lt;br/&gt;rent]
    D --&gt; D2[💧 水费&lt;br/&gt;waterCharge]

    E --&gt; E1[👥 人力临时扣款&lt;br/&gt;temporaryReduceSalary]
    E --&gt; E2[💼 财务临时扣款&lt;br/&gt;temporaryReduceSalary2]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
</div><p><strong>💳 工资发放凭证结构图</strong></p>
<div class="mermaid">graph LR
    A[💳 工资发放凭证] --&gt; B[📊 借方科目]
    A --&gt; C[📊 贷方科目]

    B --&gt; B1[💰 应付职工薪酬-基本工资&lt;br/&gt;salaryTotalBase]
    B --&gt; B2[🌍 应付职工薪酬-津贴补贴&lt;br/&gt;221102]
    B --&gt; B3[🎯 应付职工薪酬-绩效工资&lt;br/&gt;221103]
    B --&gt; B4[➖ 冲账科目（负数）]
    B --&gt; B5[🔄 特殊往来账处理]

    C --&gt; C1[🛡️ 个人五险一金]
    C --&gt; C2[💰 个人所得税]
    C --&gt; C3[🏠 生活费用扣款]
    C --&gt; C4[⏰ 临时扣款]
    C --&gt; C5[🏦 银行存款&lt;br/&gt;bankDeposit]

    C1 --&gt; C1A[👴 养老保险 ********]
    C1 --&gt; C1B[🏥 医疗保险 ********]
    C1 --&gt; C1C[💼 失业保险 ********]
    C1 --&gt; C1D[🏠 住房公积金 221104]
    C1 --&gt; C1E[💰 职业年金 ********]

    C2 --&gt; C2A[💰 个人所得税 210207]

    C3 --&gt; C3A[🏠 房租费 224199]
    C3 --&gt; C3B[💧 水费 224199]

    C4 --&gt; C4A[👥 人力临时扣款 224199]
    C4 --&gt; C4B[💼 财务临时扣款 224199]

    style A fill:#e3f2fd
    style B fill:#ffebee
    style C fill:#e8f5e8
</div><p><strong>📊 个人代扣项目明细表</strong></p>
<table>
<thead>
<tr>
<th>扣款类型</th>
<th>项目名称</th>
<th>常量名称</th>
<th>科目代码</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>🛡️ <strong>社会保险</strong></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>👴 养老保险</td>
<td>养老保险</td>
<td><code>pensionInsurance</code></td>
<td>********</td>
<td>个人缴纳部分</td>
</tr>
<tr>
<td>🏥 医疗保险</td>
<td>医疗保险</td>
<td><code>medicalInsurance</code></td>
<td>********</td>
<td>个人缴纳部分</td>
</tr>
<tr>
<td>💼 失业保险</td>
<td>失业保险</td>
<td><code>unemploymentInsurance</code></td>
<td>********</td>
<td>个人缴纳部分</td>
</tr>
<tr>
<td>🏠 住房基金</td>
<td>住房基金</td>
<td><code>housingFund</code></td>
<td>221104</td>
<td>个人缴纳部分</td>
</tr>
<tr>
<td>💰 职业年金</td>
<td>职业年金</td>
<td><code>occupationalAnnuity</code></td>
<td>********</td>
<td>个人缴纳部分</td>
</tr>
<tr>
<td>💰 <strong>税费</strong></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>💰 个人所得税</td>
<td>个人所得税</td>
<td><code>personalIncomeTaxDeduction</code></td>
<td>210207</td>
<td>代扣代缴</td>
</tr>
<tr>
<td>🏠 <strong>生活费用</strong></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>🏠 房租费</td>
<td>房租费</td>
<td><code>rent</code></td>
<td>224199</td>
<td>住房租金</td>
</tr>
<tr>
<td>💧 水费</td>
<td>水费</td>
<td><code>waterCharge</code></td>
<td>224199</td>
<td>水电费用</td>
</tr>
<tr>
<td>⏰ <strong>临时扣款</strong></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>👥 人力临时扣款</td>
<td>人力临时扣款</td>
<td><code>temporaryReduceSalary</code></td>
<td>224199</td>
<td>人力部门扣款</td>
</tr>
<tr>
<td>💼 财务临时扣款</td>
<td>财务临时扣款</td>
<td><code>temporaryReduceSalary2</code></td>
<td>224199</td>
<td>财务部门扣款</td>
</tr>
</tbody>
</table>
<p>根据系统常量定义，个人代扣项目包括：</p>
<ul>
<li>养老保险 (pensionInsurance)</li>
<li>医疗保险 (medicalInsurance)</li>
<li>失业保险 (unemploymentInsurance)</li>
<li>住房基金 (housingFund)</li>
<li>职业年金 (occupationalAnnuity)</li>
<li>个人所得税 (personalIncomeTaxDeduction)</li>
<li>房租费 (rent)</li>
<li>水费 (waterCharge)</li>
<li>人力临时扣款 (temporaryReduceSalary)</li>
<li>财务临时扣款 (temporaryReduceSalary2)</li>
</ul>
<h4 id="233-科目映射规则">2.3.3 科目映射规则 </h4>
<ul>
<li><strong>部门类型判断</strong>：业务科室 vs 管理科室</li>
<li><strong>人员类型判断</strong>：在编、招聘、临聘</li>
<li><strong>工资项目分类</strong>：基本工资、津贴补贴、绩效工资</li>
<li><strong>借贷方向</strong>：根据业务类型自动判断</li>
<li><strong>辅助项配置</strong>：部门、项目、资金性质</li>
</ul>
<h4 id="234-特殊业务处理规则">2.3.4 特殊业务处理规则 </h4>
<p>根据系统实际代码分析，特殊人员处理规则如下：</p>
<p><strong>特殊往来账处理人员</strong>：</p>
<ul>
<li>维修班人员：邹毅、田晓辉、刘黎、刘亨强、陈洪浩、吕勇、苟鹏、古代勇、林贤培、张丽、邓钟</li>
<li>总务科特殊人员：吴思琪</li>
<li>住院收费室特殊人员：罗亚婕</li>
<li>处理方式：借方科目使用"其他应收款-其他121803[往来单位4414]"</li>
</ul>
<p><strong>单采血浆站特殊处理</strong>：</p>
<ul>
<li>适用范围：血浆站全体人员</li>
<li>处理方式：借方科目使用"其他应收款-其他121803[往来单位7004]"</li>
</ul>
<p><strong>个人扣款配置检查</strong>：</p>
<ul>
<li>工资发放类型(salaryType=2)需要检查个人扣款配置</li>
<li>未配置的员工需要先进行个人扣款配置才能生成凭证</li>
</ul>
<p><strong>预算科目映射</strong>：</p>
<ul>
<li>预算科目：7201010301[事业支出-医院支出其他资金支出-基本支出]</li>
<li>适用于所有工资类型的预算分录生成</li>
</ul>
<h3 id="24-现有业务代码路径分析">2.4 现有业务代码路径分析 </h3>
<h4 id="241-核心业务代码位置">2.4.1 核心业务代码位置 </h4>
<p>基于系统代码分析，当前工资凭证相关的业务逻辑分布在以下文件中：</p>
<p><strong>后端代码路径</strong>：</p>
<pre data-role="codeBlock" data-info="" class="language-text"><code>med-ecs/src/main/java/com/jp/med/ecs/modules/reimMgt/
├── service/
│   ├── read/impl/
│   │   └── EcsReimSalaryTaskReadServiceImpl.java     # 工资任务查询服务
│   └── write/impl/
│       └── EcsReimSalaryTaskWriteServiceImpl.java    # 工资任务写入服务
├── controller/
│   └── EcsReimSalaryTaskController.java              # 工资任务控制器
└── constant/
    └── EcsConst.java                                 # 常量定义（人员类型、工资项目等）

med-common/src/main/java/com/jp/med/common/
├── dto/ecs/
│   ├── EcsReimSalaryTask.java                        # 工资任务DTO
│   └── EcsReimSalaryTaskDetail.java                  # 工资任务明细DTO
└── constant/
    ├── MedConst.java                                 # 医疗系统常量
    └── ReimTypeConst.java                            # 报销类型常量
</code></pre><p><strong>前端代码路径</strong>：</p>
<pre data-role="codeBlock" data-info="" class="language-text"><code>src/views/modules/
├── ecs/reimMgt/salaryReim/index.vue                  # 工资报销管理页面
├── ecs/reimNew/salaryReim/index.vue                  # 新版工资报销页面
└── erp/
    ├── vcrGen/vcrPane/components/salaryVcr.vue       # 工资凭证生成组件
    └── config/salaryConfig/index.vue                 # 工资凭证科目映射配置

src/api/erp/vcr/vcrGen/vcrGen.ts                      # 凭证生成API接口
</code></pre><h4 id="242-关键业务逻辑代码片段">2.4.2 关键业务逻辑代码片段 </h4>
<p><strong>工资类型判断逻辑</strong>（位于前端组件）：</p>
<pre data-role="codeBlock" data-info="javascript" class="language-javascript javascript"><code><span class="token comment">// src/views/modules/ecs/reimNew/salaryReim/index.vue:330-337</span>
<span class="token keyword control-flow keyword-switch">switch</span> <span class="token punctuation">(</span>row<span class="token punctuation">.</span><span class="token property-access">salaryType</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-case">case</span> <span class="token string">'1'</span><span class="token operator">:</span> <span class="token keyword control-flow keyword-return">return</span> <span class="token string">'工资计提'</span>           <span class="token comment">// 直接生成凭证</span>
    <span class="token keyword keyword-case">case</span> <span class="token string">'2'</span><span class="token operator">:</span> <span class="token keyword control-flow keyword-return">return</span> <span class="token string">'工资发放+三险两金'</span>   <span class="token comment">// 报销审批流程</span>
    <span class="token keyword keyword-case">case</span> <span class="token string">'3'</span><span class="token operator">:</span> <span class="token keyword control-flow keyword-return">return</span> <span class="token string">'企业四险两金'</span>       <span class="token comment">// 直接生成凭证</span>
    <span class="token keyword keyword-case">case</span> <span class="token string">'4'</span><span class="token operator">:</span> <span class="token keyword control-flow keyword-return">return</span> <span class="token string">'工会经费'</span>          <span class="token comment">// 直接生成凭证</span>
<span class="token punctuation">}</span>

<span class="token comment">// src/views/modules/erp/vcrGen/vcrPane/components/salaryVcr.vue:427-435</span>
<span class="token keyword control-flow keyword-if">if</span> <span class="token punctuation">(</span>row<span class="token punctuation">.</span><span class="token property-access">salaryType</span> <span class="token operator">==</span> <span class="token string">'2'</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// 个人代扣类型工资凭证，判断个人扣款是否都配置了配置项</span>
    <span class="token keyword keyword-const">const</span> indiToConfig <span class="token operator">=</span> <span class="token keyword control-flow keyword-await">await</span> <span class="token function">queryToConfigSalary</span><span class="token punctuation">(</span><span class="token punctuation">{</span><span class="token literal-property property">id</span><span class="token operator">:</span> checkedRowKeys<span class="token punctuation">.</span><span class="token property-access">value</span><span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">}</span><span class="token punctuation">)</span>
    <span class="token keyword control-flow keyword-if">if</span> <span class="token punctuation">(</span>indiToConfig<span class="token punctuation">.</span><span class="token property-access">code</span> <span class="token operator">==</span> <span class="token number">200</span> <span class="token operator">&amp;&amp;</span> indiToConfig<span class="token punctuation">.</span><span class="token property-access">data</span><span class="token punctuation">.</span><span class="token property-access">length</span> <span class="token operator">&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// 弹出界面进行个人扣款配置</span>
        indiToConfigModal<span class="token punctuation">.</span><span class="token property-access">value</span> <span class="token operator">=</span> <span class="token boolean">true</span>
        <span class="token keyword control-flow keyword-return">return</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>人员类型常量定义</strong>（位于EcsConst.java）：</p>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token comment">// med-ecs/src/main/java/com/jp/med/ecs/modules/reimMgt/constant/EcsConst.java:271-284</span>
<span class="token comment">//在编</span>
<span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token constant">ESTAB_STR_ARR</span> <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">{</span><span class="token string">"在编"</span><span class="token punctuation">,</span><span class="token string">"血防占编"</span><span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">//招聘</span>
<span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token constant">HIRE_STR_ARR</span> <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">{</span><span class="token string">"编外-医技"</span><span class="token punctuation">,</span><span class="token string">"编外-护理"</span><span class="token punctuation">,</span><span class="token string">"编外-辅助岗位"</span><span class="token punctuation">,</span>
                                    <span class="token string">"编外-医技-见习"</span><span class="token punctuation">,</span><span class="token string">"编外-护理-见习"</span><span class="token punctuation">,</span><span class="token string">"编外-辅助岗位-见习"</span><span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">//临聘</span>
<span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token constant">TEMP_HIRE_STR_ARR</span> <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">{</span><span class="token string">"编外-其他专技"</span><span class="token punctuation">,</span><span class="token string">"编外-后勤"</span><span class="token punctuation">,</span>
                                         <span class="token string">"编外-其他专技-见习"</span><span class="token punctuation">,</span><span class="token string">"编外-后勤-见习"</span><span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">//借调</span>
<span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token constant">SECONDMENT_STR_ARR</span> <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">{</span><span class="token string">"借调"</span><span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">//返聘</span>
<span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token constant">REHIRE_STR_ARR</span> <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">{</span><span class="token string">"返聘"</span><span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><p><strong>工资项目常量定义</strong>（位于EcsConst.java）：</p>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token comment">// med-ecs/src/main/java/com/jp/med/ecs/modules/reimMgt/constant/EcsConst.java:184-267</span>
<span class="token comment">//岗位工资</span>
<span class="token class-name">String</span> <span class="token constant">POST_SALARY</span> <span class="token operator">=</span> <span class="token string">"postSalary"</span><span class="token punctuation">;</span>
<span class="token comment">// 薪级工资</span>
<span class="token class-name">String</span> <span class="token constant">SAL_GRADE_SALARY</span> <span class="token operator">=</span> <span class="token string">"salGradeSalary"</span><span class="token punctuation">;</span>
<span class="token comment">// 护士 10%</span>
<span class="token class-name">String</span> <span class="token constant">NURSE_SALARY</span> <span class="token operator">=</span> <span class="token string">"nurseSalary"</span><span class="token punctuation">;</span>
<span class="token comment">// 地区附加津贴</span>
<span class="token class-name">String</span> <span class="token constant">AREA_SALARY</span> <span class="token operator">=</span> <span class="token string">"areaSalary"</span><span class="token punctuation">;</span>
<span class="token comment">// 护龄津贴</span>
<span class="token class-name">String</span> <span class="token constant">AGE_SALARY</span> <span class="token operator">=</span> <span class="token string">"ageSalary"</span><span class="token punctuation">;</span>
<span class="token comment">// 基础性绩效</span>
<span class="token class-name">String</span> <span class="token constant">BASIC_PERF</span> <span class="token operator">=</span> <span class="token string">"basicPerf"</span><span class="token punctuation">;</span>
<span class="token comment">// 人力临时增加</span>
<span class="token class-name">String</span> <span class="token constant">TEMPORARY_ADD_SALARY</span> <span class="token operator">=</span> <span class="token string">"temporaryAddSalary"</span><span class="token punctuation">;</span>
<span class="token comment">// 财务临时增加</span>
<span class="token class-name">String</span> <span class="token constant">TEMPORARY_ADD_SALARY2</span> <span class="token operator">=</span> <span class="token string">"temporaryAddSalary2"</span><span class="token punctuation">;</span>

<span class="token comment">// 养老保险</span>
<span class="token class-name">String</span> <span class="token constant">PENSION_INSURANCE</span> <span class="token operator">=</span> <span class="token string">"pensionInsurance"</span><span class="token punctuation">;</span>
<span class="token comment">// 医疗保险</span>
<span class="token class-name">String</span> <span class="token constant">MEDICAL_INSURANCE</span> <span class="token operator">=</span> <span class="token string">"medicalInsurance"</span><span class="token punctuation">;</span>
<span class="token comment">// 失业保险</span>
<span class="token class-name">String</span> <span class="token constant">UNEMPLOYMENT_INSURANCE</span> <span class="token operator">=</span> <span class="token string">"unemploymentInsurance"</span><span class="token punctuation">;</span>
<span class="token comment">// 住房基金</span>
<span class="token class-name">String</span> <span class="token constant">HOUSING_FUND</span> <span class="token operator">=</span> <span class="token string">"housingFund"</span><span class="token punctuation">;</span>
<span class="token comment">// 职业年金</span>
<span class="token class-name">String</span> <span class="token constant">OCCUPATION_ANNUITY</span> <span class="token operator">=</span> <span class="token string">"occupationalAnnuity"</span><span class="token punctuation">;</span>
<span class="token comment">//个人所得税</span>
<span class="token class-name">String</span> <span class="token constant">PERSON_TAX</span> <span class="token operator">=</span> <span class="token string">"personalIncomeTaxDeduction"</span><span class="token punctuation">;</span>
</code></pre><h3 id="25-存在问题">2.5 存在问题 </h3>
<ol>
<li><strong>规则硬编码</strong>：业务规则直接写在Java代码和前端组件中，修改需要重新部署</li>
<li><strong>维护困难</strong>：业务人员无法直接修改规则，依赖开发人员</li>
<li><strong>扩展性差</strong>：新增工资类型或规则变更需要修改多处代码</li>
<li><strong>测试复杂</strong>：规则变更影响面广，测试成本高</li>
<li><strong>一致性风险</strong>：多处相似逻辑容易出现不一致</li>
<li><strong>代码分散</strong>：业务逻辑分散在前后端多个文件中，难以统一管理</li>
</ol>
<h2 id="3-解决方案设计-">3. 解决方案设计 🎨 </h2>
<h3 id="31-drools规则引擎架构">3.1 Drools规则引擎架构 </h3>
<div class="mermaid">graph TB
    A[工资凭证请求] --&gt; B[规则引擎入口]
    B --&gt; C[事实对象构建]
    C --&gt; D[Drools规则引擎]
    D --&gt; E[规则执行]
    E --&gt; F[结果收集]
    F --&gt; G[凭证生成决策]
    
    H[规则配置管理] --&gt; I[规则文件生成]
    I --&gt; J[规则热加载]
    J --&gt; D
    
    K[规则管理界面] --&gt; H
</div><h3 id="32-核心组件设计">3.2 核心组件设计 </h3>
<h4 id="321-规则引擎服务salaryvoucherruleservice">3.2.1 规则引擎服务(SalaryVoucherRuleService) </h4>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token annotation punctuation">@Service</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">SalaryVoucherRuleService</span> <span class="token punctuation">{</span>
    
    <span class="token doc-comment comment">/**
     * 执行工资凭证规则
     */</span>
    <span class="token keyword keyword-public">public</span> <span class="token class-name">SalaryVoucherDecision</span> <span class="token function">executeRules</span><span class="token punctuation">(</span><span class="token class-name">SalaryTaskFact</span> fact<span class="token punctuation">)</span><span class="token punctuation">;</span>
    
    <span class="token doc-comment comment">/**
     * 重载规则
     */</span>
    <span class="token keyword keyword-public">public</span> <span class="token keyword keyword-void">void</span> <span class="token function">reloadRules</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h4 id="322-事实对象fact-objects">3.2.2 事实对象(Fact Objects) </h4>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token comment">// 工资任务事实</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">SalaryTaskFact</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> salaryType<span class="token punctuation">;</span>      <span class="token comment">// 工资类型：1-工资计提，2-工资发放，3-企业四险两金，4-工会经费</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">BigDecimal</span> shouldPay<span class="token punctuation">;</span>   <span class="token comment">// 应发金额</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">BigDecimal</span> reducePay<span class="token punctuation">;</span>   <span class="token comment">// 扣款金额</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">BigDecimal</span> realPay<span class="token punctuation">;</span>     <span class="token comment">// 实发金额</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> ffMth<span class="token punctuation">;</span>           <span class="token comment">// 发放月份</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">Integer</span> num<span class="token punctuation">;</span>            <span class="token comment">// 工资条数</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> bchno<span class="token punctuation">;</span>           <span class="token comment">// 批次号</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">List</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">SalaryDetailFact</span><span class="token punctuation">&gt;</span></span> details<span class="token punctuation">;</span> <span class="token comment">// 明细</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">ProcessType</span> processType<span class="token punctuation">;</span> <span class="token comment">// 处理类型</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> voucherTemplate<span class="token punctuation">;</span> <span class="token comment">// 凭证模板</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">List</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">VoucherEntry</span><span class="token punctuation">&gt;</span></span> voucherEntries<span class="token punctuation">;</span> <span class="token comment">// 凭证分录</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">List</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">String</span><span class="token punctuation">&gt;</span></span> validationErrors<span class="token punctuation">;</span> <span class="token comment">// 验证错误</span>
<span class="token punctuation">}</span>

<span class="token comment">// 工资明细事实</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">SalaryDetailFact</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> orgId<span class="token punctuation">;</span>           <span class="token comment">// 科室ID</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> orgName<span class="token punctuation">;</span>         <span class="token comment">// 科室名称</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> deptType<span class="token punctuation">;</span>        <span class="token comment">// 部门类型：业务科室、管理科室</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> reimType<span class="token punctuation">;</span>        <span class="token comment">// 报销类型：岗位工资、薪级工资、护士10%等</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">BigDecimal</span> reimAmt<span class="token punctuation">;</span>     <span class="token comment">// 报销金额</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> empCode<span class="token punctuation">;</span>         <span class="token comment">// 员工编号</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> empName<span class="token punctuation">;</span>         <span class="token comment">// 员工姓名</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> empType<span class="token punctuation">;</span>         <span class="token comment">// 人员类型：在编、招聘、临聘</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">Integer</span> empCount<span class="token punctuation">;</span>       <span class="token comment">// 人数</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> fundType<span class="token punctuation">;</span>        <span class="token comment">// 资金类型</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> subjectCode<span class="token punctuation">;</span>     <span class="token comment">// 会计科目代码</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> budgetSubject<span class="token punctuation">;</span>   <span class="token comment">// 预算科目代码</span>
<span class="token punctuation">}</span>

<span class="token comment">// 工资上下文事实</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">SalaryContextFact</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> hospitalCode<span class="token punctuation">;</span>    <span class="token comment">// 医院代码</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> currentMonth<span class="token punctuation">;</span>    <span class="token comment">// 当前月份</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> currentYear<span class="token punctuation">;</span>     <span class="token comment">// 当前年度</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">Map</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">String</span><span class="token punctuation">,</span> <span class="token class-name">String</span><span class="token punctuation">&gt;</span></span> deptMapping<span class="token punctuation">;</span> <span class="token comment">// 部门映射</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">Map</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">String</span><span class="token punctuation">,</span> <span class="token class-name">String</span><span class="token punctuation">&gt;</span></span> subjectMapping<span class="token punctuation">;</span> <span class="token comment">// 科目映射</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">List</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">String</span><span class="token punctuation">&gt;</span></span> specialEmployees<span class="token punctuation">;</span> <span class="token comment">// 特殊员工列表</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">Map</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">String</span><span class="token punctuation">,</span> <span class="token class-name">Object</span><span class="token punctuation">&gt;</span></span> systemConfig<span class="token punctuation">;</span> <span class="token comment">// 系统配置</span>
<span class="token punctuation">}</span>

<span class="token comment">// 个人扣款配置事实</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">PersonalDeductionConfig</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> empCode<span class="token punctuation">;</span>         <span class="token comment">// 员工编号</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> deductionType<span class="token punctuation">;</span>   <span class="token comment">// 扣款类型</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">BigDecimal</span> amount<span class="token punctuation">;</span>      <span class="token comment">// 扣款金额</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> status<span class="token punctuation">;</span>          <span class="token comment">// 状态</span>
<span class="token punctuation">}</span>
</code></pre><h4 id="323-决策结果decision-objects">3.2.3 决策结果(Decision Objects) </h4>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token comment">// 凭证生成决策</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">SalaryVoucherDecision</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">ProcessType</span> processType<span class="token punctuation">;</span>    <span class="token comment">// 处理类型：直接生成/报销流程</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> voucherTemplate<span class="token punctuation">;</span>     <span class="token comment">// 凭证模板</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">List</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">VoucherEntry</span><span class="token punctuation">&gt;</span></span> entries<span class="token punctuation">;</span> <span class="token comment">// 凭证分录</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">List</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">BudgetEntry</span><span class="token punctuation">&gt;</span></span> budgetEntries<span class="token punctuation">;</span> <span class="token comment">// 预算分录</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">List</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">String</span><span class="token punctuation">&gt;</span></span> validationErrors<span class="token punctuation">;</span> <span class="token comment">// 验证错误</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">Map</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">String</span><span class="token punctuation">,</span> <span class="token class-name">Object</span><span class="token punctuation">&gt;</span></span> params<span class="token punctuation">;</span> <span class="token comment">// 附加参数</span>
    <span class="token keyword keyword-private">private</span> <span class="token keyword keyword-boolean">boolean</span> success<span class="token punctuation">;</span>            <span class="token comment">// 执行是否成功</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> message<span class="token punctuation">;</span>             <span class="token comment">// 执行消息</span>
<span class="token punctuation">}</span>

<span class="token comment">// 凭证分录</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">VoucherEntry</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> subjectCode<span class="token punctuation">;</span>         <span class="token comment">// 会计科目代码</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> subjectName<span class="token punctuation">;</span>         <span class="token comment">// 会计科目名称</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">BigDecimal</span> debitAmount<span class="token punctuation">;</span>     <span class="token comment">// 借方金额</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">BigDecimal</span> creditAmount<span class="token punctuation">;</span>    <span class="token comment">// 贷方金额</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> direction<span class="token punctuation">;</span>           <span class="token comment">// 借贷方向：DEBIT/CREDIT</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">Map</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">String</span><span class="token punctuation">,</span> <span class="token class-name">String</span><span class="token punctuation">&gt;</span></span> auxiliaryItems<span class="token punctuation">;</span> <span class="token comment">// 辅助项</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> description<span class="token punctuation">;</span>         <span class="token comment">// 摘要</span>
<span class="token punctuation">}</span>

<span class="token comment">// 预算分录</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">BudgetEntry</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> budgetSubject<span class="token punctuation">;</span>       <span class="token comment">// 预算科目代码</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> budgetSubjectName<span class="token punctuation">;</span>   <span class="token comment">// 预算科目名称</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">BigDecimal</span> amount<span class="token punctuation">;</span>          <span class="token comment">// 金额</span>
    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> description<span class="token punctuation">;</span>         <span class="token comment">// 说明</span>
<span class="token punctuation">}</span>

<span class="token comment">// 处理类型枚举</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-enum">enum</span> <span class="token class-name">ProcessType</span> <span class="token punctuation">{</span>
    <span class="token function">DIRECT_VOUCHER</span><span class="token punctuation">(</span><span class="token string">"直接生成凭证"</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    <span class="token function">REIMBURSEMENT_FLOW</span><span class="token punctuation">(</span><span class="token string">"报销审批流程"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> description<span class="token punctuation">;</span>

    <span class="token class-name">ProcessType</span><span class="token punctuation">(</span><span class="token class-name">String</span> description<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>description <span class="token operator">=</span> description<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 凭证模板枚举</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-enum">enum</span> <span class="token class-name">VoucherTemplate</span> <span class="token punctuation">{</span>
    <span class="token function">SALARY_ACCRUAL</span><span class="token punctuation">(</span><span class="token string">"工资计提"</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    <span class="token function">SALARY_PAYMENT</span><span class="token punctuation">(</span><span class="token string">"工资发放"</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    <span class="token function">ENTERPRISE_INSURANCE</span><span class="token punctuation">(</span><span class="token string">"企业四险两金"</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    <span class="token function">UNION_FEE</span><span class="token punctuation">(</span><span class="token string">"工会经费"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-private">private</span> <span class="token class-name">String</span> description<span class="token punctuation">;</span>

    <span class="token class-name">VoucherTemplate</span><span class="token punctuation">(</span><span class="token class-name">String</span> description<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>description <span class="token operator">=</span> description<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="33-规则分类设计">3.3 规则分类设计 </h3>
<h4 id="331-工资类型判断规则">3.3.1 工资类型判断规则 </h4>
<p><strong>🔄 工资类型判断规则流程图</strong></p>
<div class="mermaid">flowchart TD
    A[📋 工资任务输入] --&gt; B{🏷️ 工资类型判断}

    B --&gt;|salaryType=1| C[📊 R001_工资计提类型判断]
    B --&gt;|salaryType=2| D[💳 R002_工资发放类型判断]
    B --&gt;|salaryType=3| E[🛡️ R003_企业四险两金类型判断]
    B --&gt;|salaryType=4| F[🏛️ R004_工会经费类型判断]
    B --&gt;|其他值| G[❌ R005_未知工资类型处理]

    C --&gt; C1[✅ 直接生成凭证&lt;br/&gt;DIRECT_VOUCHER]
    C --&gt; C2[📋 工资计提模板&lt;br/&gt;SALARY_ACCRUAL]

    D --&gt; D1[🔄 报销审批流程&lt;br/&gt;REIMBURSEMENT_FLOW]
    D --&gt; D2[💳 工资发放模板&lt;br/&gt;SALARY_PAYMENT]

    E --&gt; E1[✅ 直接生成凭证&lt;br/&gt;DIRECT_VOUCHER]
    E --&gt; E2[🛡️ 企业保险模板&lt;br/&gt;ENTERPRISE_INSURANCE]

    F --&gt; F1[✅ 直接生成凭证&lt;br/&gt;DIRECT_VOUCHER]
    F --&gt; F2[🏛️ 工会经费模板&lt;br/&gt;UNION_FEE]

    G --&gt; G1[❌ 验证错误&lt;br/&gt;ValidationError]

    C1 --&gt; H[📄 生成凭证]
    D1 --&gt; I[🔄 启动审批流程]
    E1 --&gt; H
    F1 --&gt; H
    G1 --&gt; J[❌ 终止处理]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#ffebee
    style H fill:#e8f5e8
    style I fill:#fff3e0
    style J fill:#ffebee
</div><p><strong>📋 规则详细说明表</strong></p>
<table>
<thead>
<tr>
<th>规则编号</th>
<th>规则名称</th>
<th>优先级</th>
<th>条件</th>
<th>动作</th>
<th>结果</th>
</tr>
</thead>
<tbody>
<tr>
<td>R001</td>
<td>工资计提类型判断</td>
<td>100</td>
<td>salaryType == "1"</td>
<td>设置直接生成凭证</td>
<td>工资计提凭证</td>
</tr>
<tr>
<td>R002</td>
<td>工资发放类型判断</td>
<td>100</td>
<td>salaryType == "2"</td>
<td>设置报销审批流程</td>
<td>工资发放审批</td>
</tr>
<tr>
<td>R003</td>
<td>企业四险两金类型判断</td>
<td>100</td>
<td>salaryType == "3"</td>
<td>设置直接生成凭证</td>
<td>企业保险凭证</td>
</tr>
<tr>
<td>R004</td>
<td>工会经费类型判断</td>
<td>100</td>
<td>salaryType == "4"</td>
<td>设置直接生成凭证</td>
<td>工会经费凭证</td>
</tr>
<tr>
<td>R005</td>
<td>未知工资类型处理</td>
<td>50</td>
<td>salaryType not in ("1","2","3","4")</td>
<td>添加验证错误</td>
<td>错误提示</td>
</tr>
</tbody>
</table>
<p><strong>📝 Drools规则实现</strong></p>
<p>将工资类型判断细分为具体的业务规则：</p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>// 规则组：工资类型处理规则
rule "R001_工资计提类型判断"
    salience 100
when
    $task: SalaryTaskFact(salaryType == "1")
then
    $task.setProcessType(ProcessType.DIRECT_VOUCHER);
    $task.setVoucherTemplate("SALARY_ACCRUAL");
    $task.setRuleApplied("R001");
    System.out.println("应用规则R001：工资计提直接生成凭证");
end

rule "R002_工资发放类型判断"
    salience 100
when
    $task: SalaryTaskFact(salaryType == "2")
then
    $task.setProcessType(ProcessType.REIMBURSEMENT_FLOW);
    $task.setFlowTemplate("SALARY_PAYMENT");
    $task.setRuleApplied("R002");
    System.out.println("应用规则R002：工资发放走报销审批流程");
end

rule "R003_企业四险两金类型判断"
    salience 100
when
    $task: SalaryTaskFact(salaryType == "3")
then
    $task.setProcessType(ProcessType.DIRECT_VOUCHER);
    $task.setVoucherTemplate("ENTERPRISE_INSURANCE");
    $task.setRuleApplied("R003");
    System.out.println("应用规则R003：企业四险两金直接生成凭证");
end

rule "R004_工会经费类型判断"
    salience 100
when
    $task: SalaryTaskFact(salaryType == "4")
then
    $task.setProcessType(ProcessType.DIRECT_VOUCHER);
    $task.setVoucherTemplate("UNION_FEE");
    $task.setRuleApplied("R004");
    System.out.println("应用规则R004：工会经费直接生成凭证");
end

rule "R005_未知工资类型处理"
    salience 50
when
    $task: SalaryTaskFact(salaryType not in ("1", "2", "3", "4"))
then
    $task.addValidationError("未知的工资类型：" + $task.getSalaryType());
    $task.setRuleApplied("R005");
    System.out.println("应用规则R005：未知工资类型错误处理");
end
</code></pre><h4 id="332-工资计提科目映射规则">3.3.2 工资计提科目映射规则 </h4>
<p>将工资计提科目映射细分为每个具体的工资项目规则：</p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>**🏥 业务科室工资计提规则流程图**

```mermaid
flowchart TD
    A[📋 业务科室工资计提&lt;br/&gt;salaryType=1 &amp; deptType=1] --&gt; B{👥 人员类型判断}

    B --&gt;|在编/招聘人员| C[🎯 在编招聘人员规则组]
    B --&gt;|临聘人员| D[⏰ 临聘人员规则组]

    C --&gt; C1[💰 R101_岗位工资计提]
    C --&gt; C2[📊 R103_薪级工资计提]
    C --&gt; C3[👩‍⚕️ R104_护士10%计提]
    C --&gt; C4[🌍 R105_地区附加津贴计提]
    C --&gt; C5[⏰ R106_护龄补贴计提]
    C --&gt; C6[🎯 R107_基础绩效工资计提]
    C --&gt; C7[➕ R108_人力临时增加计提]
    C --&gt; C8[💼 R109_财务临时增加计提]

    D --&gt; D1[💰 R102_临聘岗位工资计提]
    D --&gt; D2[➕ 人力临时增加计提]
    D --&gt; D3[💼 财务临时增加计提]

    C1 --&gt; E[📄 生成凭证分录]
    C2 --&gt; E
    C3 --&gt; E
    C4 --&gt; E
    C5 --&gt; E
    C6 --&gt; E
    C7 --&gt; E
    C8 --&gt; E
    D1 --&gt; E
    D2 --&gt; E
    D3 --&gt; E

    E --&gt; F[💰 借方：**********&lt;br/&gt;业务活动费用-其他经费-人员经费]
    E --&gt; G[💳 贷方：221101/221102/221103&lt;br/&gt;应付职工薪酬]

    F --&gt; H[📋 辅助项配置&lt;br/&gt;部门+项目]
    G --&gt; I[✅ 凭证生成完成]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#ffebee
    style G fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#e8f5e8
</code></pre><p><strong>📊 业务科室工资计提规则映射表</strong></p>
<table>
<thead>
<tr>
<th>规则编号</th>
<th>工资项目</th>
<th>人员类型</th>
<th>借方科目</th>
<th>贷方科目</th>
<th>辅助项</th>
</tr>
</thead>
<tbody>
<tr>
<td>R101</td>
<td>岗位工资</td>
<td>在编/招聘</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R102</td>
<td>岗位工资</td>
<td>临聘</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R103</td>
<td>薪级工资</td>
<td>在编/招聘</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R104</td>
<td>护士10%</td>
<td>在编/招聘</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R105</td>
<td>地区附加津贴</td>
<td>全部</td>
<td>**********</td>
<td>221102</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R106</td>
<td>护龄补贴</td>
<td>全部</td>
<td>**********</td>
<td>221102</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R107</td>
<td>基础绩效工资</td>
<td>全部</td>
<td>**********</td>
<td>221103</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R108</td>
<td>人力临时增加</td>
<td>全部</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R109</td>
<td>财务临时增加</td>
<td>全部</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
</tr>
</tbody>
</table>
<p><strong>📝 Drools规则实现</strong></p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>// 规则组：业务科室工资计提规则
rule "R101_业务科室在编招聘岗位工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "岗位工资",
        deptType == "1", // 业务科室
        empType in ("在编", "血防占编", "编外-医技", "编外-护理", "编外-辅助岗位",
                   "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********"); // 业务活动费用-其他经费-人员经费
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("岗位工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "岗位工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101"); // 应付职工薪酬-基本工资
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("岗位工资计提-" + $detail.getOrgName());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R101");
end

rule "R102_业务科室临聘岗位工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "岗位工资",
        deptType == "1", // 业务科室
        empType in ("编外-其他专技", "编外-后勤", "编外-其他专技-见习", "编外-后勤-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("临聘岗位工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "临聘岗位工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("临聘岗位工资计提-" + $detail.getOrgName());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R102");
end

rule "R103_业务科室薪级工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "薪级工资",
        deptType == "1",
        empType in ("在编", "血防占编", "编外-医技", "编外-护理", "编外-辅助岗位",
                   "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("薪级工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "薪级工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R103");
end

rule "R104_业务科室护士10%计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "护士10%",
        deptType == "1",
        empType in ("在编", "血防占编", "编外-医技", "编外-护理", "编外-辅助岗位",
                   "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("护士10%计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "护士津贴"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R104");
end

rule "R105_业务科室地区附加津贴计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "地区附加津贴",
        deptType == "1"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("地区附加津贴计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "地区津贴"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221102"); // 应付职工薪酬-国家统一规定的津贴补贴
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R105");
end

rule "R106_业务科室护龄补贴计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "护龄补贴",
        deptType == "1"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("护龄补贴计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "护龄津贴"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221102");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R106");
end

rule "R107_业务科室基础绩效工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "基础绩效工资",
        deptType == "1"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("基础绩效工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "绩效工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221103"); // 应付职工薪酬-规范津贴补贴
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R107");
end

rule "R108_业务科室人力临时增加计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "人力临时增加",
        deptType == "1"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("人力临时增加计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "临时工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R108");
end

rule "R109_业务科室财务临时增加计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "财务临时增加",
        deptType == "1"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("财务临时增加计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "财务调整"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R109");
end
</code></pre><h4>3.3.3 管理科室工资计提规则</h4>
<p><strong>🏢 管理科室工资计提规则流程图</strong></p>
<div class="mermaid">flowchart TD
    A[🏢 管理科室工资计提&lt;br/&gt;salaryType=1 &amp; deptType=2] --&gt; B{👥 人员类型判断}

    B --&gt;|在编人员| C[📋 在编人员规则组]
    B --&gt;|招聘人员| D[🎯 招聘人员规则组]
    B --&gt;|临聘人员| E[⏰ 临聘人员规则组]

    C --&gt; C1[💰 R201_在编岗位工资计提]
    C --&gt; C2[📊 R204_薪级工资计提]
    C --&gt; C3[🌍 R205_地区附加津贴计提]

    D --&gt; D1[💰 R202_招聘岗位工资计提]
    D --&gt; D2[📊 招聘薪级工资计提]
    D --&gt; D3[🌍 招聘地区附加津贴计提]

    E --&gt; E1[💰 R203_临聘岗位工资计提]
    E --&gt; E2[➕ 临聘人力临时增加]
    E --&gt; E3[💼 临聘财务临时增加]

    C1 --&gt; F[📄 生成凭证分录]
    C2 --&gt; F
    C3 --&gt; F
    D1 --&gt; F
    D2 --&gt; F
    D3 --&gt; F
    E1 --&gt; F
    E2 --&gt; F
    E3 --&gt; F

    F --&gt; G[💰 借方：5201030199&lt;br/&gt;单位管理费用-其他经费-人员经费]
    F --&gt; H[💳 贷方：221101/221102/221103&lt;br/&gt;应付职工薪酬]

    G --&gt; I[📋 辅助项配置&lt;br/&gt;部门+项目]
    H --&gt; J[✅ 凭证生成完成]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#ffebee
    style H fill:#e8f5e8
    style I fill:#fff3e0
    style J fill:#e8f5e8
</div><p><strong>📊 管理科室工资计提规则映射表</strong></p>
<table>
<thead>
<tr>
<th>规则编号</th>
<th>工资项目</th>
<th>人员类型</th>
<th>借方科目</th>
<th>贷方科目</th>
<th>辅助项</th>
</tr>
</thead>
<tbody>
<tr>
<td>R201</td>
<td>岗位工资</td>
<td>在编</td>
<td>5201030199</td>
<td>221101</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R202</td>
<td>岗位工资</td>
<td>招聘</td>
<td>5201030199</td>
<td>221101</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R203</td>
<td>岗位工资</td>
<td>临聘</td>
<td>5201030199</td>
<td>221101</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R204</td>
<td>薪级工资</td>
<td>全部</td>
<td>5201030199</td>
<td>221101</td>
<td>部门+项目</td>
</tr>
<tr>
<td>R205</td>
<td>地区附加津贴</td>
<td>全部</td>
<td>5201030199</td>
<td>221102</td>
<td>部门+项目</td>
</tr>
</tbody>
</table>
<p><strong>📝 Drools规则实现</strong></p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>// 规则组：管理科室工资计提规则
rule "R201_管理科室在编岗位工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "岗位工资",
        deptType == "2", // 管理科室
        empType in ("在编", "血防占编")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("5201030199"); // 单位管理费用-其他经费-人员经费
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("管理科室岗位工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "岗位工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R201");
end

rule "R202_管理科室招聘岗位工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "岗位工资",
        deptType == "2",
        empType in ("编外-医技", "编外-护理", "编外-辅助岗位",
                   "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("5201030199");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("管理科室招聘岗位工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "招聘岗位工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R202");
end

rule "R203_管理科室临聘岗位工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "岗位工资",
        deptType == "2",
        empType in ("编外-其他专技", "编外-后勤", "编外-其他专技-见习", "编外-后勤-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("5201030199");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("管理科室临聘岗位工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "临聘岗位工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R203");
end

// 管理科室其他工资项目规则（薪级工资、护士10%、津贴等）
rule "R204_管理科室薪级工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "薪级工资",
        deptType == "2"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("5201030199");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("管理科室薪级工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "薪级工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R204");
end

rule "R205_管理科室地区附加津贴计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "地区附加津贴",
        deptType == "2"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("5201030199");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("管理科室地区附加津贴计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "地区津贴"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221102");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R205");
end

#### 3.3.4 工资发放科目映射规则 {#334-工资发放科目映射规则 }

**💳 工资发放科目映射规则流程图**

```mermaid
flowchart TD
    A[💳 工资发放处理&lt;br/&gt;salaryType=2] --&gt; B{🔄 处理类型判断}

    B --&gt;|冲销处理| C[📊 工资冲销规则组]
    B --&gt;|个人代扣| D[👤 个人代扣规则组]
    B --&gt;|实发处理| E[🏦 银行存款规则]

    C --&gt; C1[💰 R301_基本工资冲销]
    C --&gt; C2[🌍 R302_津贴补贴冲销]
    C --&gt; C3[🎯 R303_绩效工资冲销]

    D --&gt; D1[👴 R311_个人养老保险代扣]
    D --&gt; D2[🏥 R312_个人医疗保险代扣]
    D --&gt; D3[💼 R313_个人失业保险代扣]
    D --&gt; D4[🏠 R314_个人住房公积金代扣]
    D --&gt; D5[💰 R315_个人职业年金代扣]
    D --&gt; D6[💰 R316_个人所得税代扣]
    D --&gt; D7[🏛️ R317_工会会费代扣]
    D --&gt; D8[🏠 R318_房租费代扣]
    D --&gt; D9[💧 R319_水费代扣]
    D --&gt; D10[👥 R320_人力临时扣款]
    D --&gt; D11[💼 R321_财务临时扣款]

    E --&gt; E1[🏦 R330_银行存款实发工资]

    C1 --&gt; F[📄 生成凭证分录]
    C2 --&gt; F
    C3 --&gt; F
    D1 --&gt; F
    D2 --&gt; F
    D3 --&gt; F
    D4 --&gt; F
    D5 --&gt; F
    D6 --&gt; F
    D7 --&gt; F
    D8 --&gt; F
    D9 --&gt; F
    D10 --&gt; F
    D11 --&gt; F
    E1 --&gt; F

    F --&gt; G[💰 借方：221101/221102/221103&lt;br/&gt;应付职工薪酬冲销]
    F --&gt; H[💳 贷方：********-22110306/221104/210207/224199&lt;br/&gt;个人代扣项目]
    F --&gt; I[🏦 贷方：100201&lt;br/&gt;银行存款]

    G --&gt; J[✅ 凭证生成完成]
    H --&gt; J
    I --&gt; J

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#ffebee
    style H fill:#e8f5e8
    style I fill:#f1f8e9
    style J fill:#e8f5e8
</code></pre><p><strong>📊 工资发放规则映射表</strong></p>
<table>
<thead>
<tr>
<th>规则类型</th>
<th>规则编号</th>
<th>项目名称</th>
<th>借方科目</th>
<th>贷方科目</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>冲销规则</strong></td>
<td>R301</td>
<td>基本工资冲销</td>
<td>221101</td>
<td>**********</td>
<td>负数冲销</td>
</tr>
<tr>
<td></td>
<td>R302</td>
<td>津贴补贴冲销</td>
<td>221102</td>
<td>**********</td>
<td>负数冲销</td>
</tr>
<tr>
<td></td>
<td>R303</td>
<td>绩效工资冲销</td>
<td>221103</td>
<td>**********</td>
<td>负数冲销</td>
</tr>
<tr>
<td><strong>个人代扣</strong></td>
<td>R311</td>
<td>个人养老保险</td>
<td>-</td>
<td>********</td>
<td>个人缴纳部分</td>
</tr>
<tr>
<td></td>
<td>R312</td>
<td>个人医疗保险</td>
<td>-</td>
<td>********</td>
<td>个人缴纳部分</td>
</tr>
<tr>
<td></td>
<td>R313</td>
<td>个人失业保险</td>
<td>-</td>
<td>********</td>
<td>个人缴纳部分</td>
</tr>
<tr>
<td></td>
<td>R314</td>
<td>个人住房公积金</td>
<td>-</td>
<td>221104</td>
<td>个人缴纳部分</td>
</tr>
<tr>
<td></td>
<td>R315</td>
<td>个人职业年金</td>
<td>-</td>
<td>********</td>
<td>个人缴纳部分</td>
</tr>
<tr>
<td></td>
<td>R316</td>
<td>个人所得税</td>
<td>-</td>
<td>210207</td>
<td>代扣代缴</td>
</tr>
<tr>
<td></td>
<td>R317</td>
<td>工会会费</td>
<td>-</td>
<td>224199</td>
<td>个人缴纳</td>
</tr>
<tr>
<td></td>
<td>R318</td>
<td>房租费</td>
<td>-</td>
<td>224199</td>
<td>生活费用</td>
</tr>
<tr>
<td></td>
<td>R319</td>
<td>水费</td>
<td>-</td>
<td>224199</td>
<td>生活费用</td>
</tr>
<tr>
<td></td>
<td>R320</td>
<td>人力临时扣款</td>
<td>-</td>
<td>224199</td>
<td>临时扣款</td>
</tr>
<tr>
<td></td>
<td>R321</td>
<td>财务临时扣款</td>
<td>-</td>
<td>224199</td>
<td>临时扣款</td>
</tr>
<tr>
<td><strong>实发处理</strong></td>
<td>R330</td>
<td>银行存款实发</td>
<td>-</td>
<td>100201</td>
<td>实际发放</td>
</tr>
</tbody>
</table>
<p><strong>📝 Drools规则实现</strong></p>
<p>将工资发放科目映射细分为每个具体的代扣代缴项目：</p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>// 规则组：工资发放冲销规则
rule "R301_基本工资冲销"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "基本工资冲销") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("221101"); // 应付职工薪酬-基本工资
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("基本工资冲销-" + $detail.getOrgName());

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("**********"); // 负数冲销
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("基本工资冲销-" + $detail.getOrgName());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R301");
end

rule "R302_津贴补贴冲销"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "津贴补贴冲销") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("221102"); // 应付职工薪酬-国家统一规定的津贴补贴
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("津贴补贴冲销-" + $detail.getOrgName());

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("**********"); // 负数冲销
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("津贴补贴冲销-" + $detail.getOrgName());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R302");
end

rule "R303_绩效工资冲销"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "绩效工资冲销") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("221103"); // 应付职工薪酬-规范津贴补贴
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("绩效工资冲销-" + $detail.getOrgName());

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("**********"); // 负数冲销
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("绩效工资冲销-" + $detail.getOrgName());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R303");
end

// 规则组：个人代扣项目规则
rule "R311_个人养老保险代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "养老保险") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-养老保险-个人缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人养老保险代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R311");
end

rule "R312_个人医疗保险代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "医疗保险") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-医疗保险-个人缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人医疗保险代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R312");
end

rule "R313_个人失业保险代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "失业保险") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-失业保险-个人缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人失业保险代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R313");
end

rule "R314_个人住房公积金代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "住房公积金") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221104"); // 应付职工薪酬-住房公积金-个人缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人住房公积金代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R314");
end

rule "R315_个人职业年金代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "职业年金") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-职业年金-个人缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人职业年金代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R315");
end

rule "R316_个人所得税代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "个人所得税") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("210207"); // 应交税费-个人所得税
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人所得税代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R316");
end

rule "R317_工会会费代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "工会会费") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-工会会费
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("工会会费代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R317");
end

rule "R318_房租费代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "房租费") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-房租费
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("房租费代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R318");
end

rule "R319_水费代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "水费") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-水费
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("水费代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R319");
end

rule "R320_人力临时扣款"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "人力临时扣款") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-临时扣款
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("人力临时扣款-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R320");
end

rule "R321_财务临时扣款"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "财务临时扣款") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-临时扣款
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("财务临时扣款-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R321");
end

rule "R330_银行存款实发工资"
    salience 70
when
    $task: SalaryTaskFact(salaryType == "2")
    $realPay: BigDecimal() from $task.realPay
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("100201"); // 银行存款-自有存款-中国银行
    creditEntry.setCreditAmount($realPay);
    creditEntry.setDescription("实发工资-银行存款");
    creditEntry.setAuxiliaryItems(buildAuxItems("资金性质", "自有资金"));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R330");
end
</code></pre><h4 id="334-企业四险两金计提规则">3.3.4 企业四险两金计提规则 </h4>
<p><strong>🛡️ 企业四险两金计提规则流程图</strong></p>
<div class="mermaid">flowchart TD
    A[🏢 企业四险两金计提&lt;br/&gt;salaryType=3] --&gt; B{🛡️ 保险类型判断}

    B --&gt;|养老保险| C[👴 R401_企业养老保险计提]
    B --&gt;|医疗保险| D[🏥 R402_企业医疗保险计提]
    B --&gt;|失业保险| E[💼 R403_企业失业保险计提]
    B --&gt;|工伤保险| F[⚠️ R404_企业工伤保险计提]
    B --&gt;|住房公积金| G[🏠 R405_企业住房公积金计提]
    B --&gt;|职业年金| H[💰 R406_企业职业年金计提]

    C --&gt; C1[📄 生成凭证分录]
    D --&gt; D1[📄 生成凭证分录]
    E --&gt; E1[📄 生成凭证分录]
    F --&gt; F1[📄 生成凭证分录]
    G --&gt; G1[📄 生成凭证分录]
    H --&gt; H1[📄 生成凭证分录]

    C1 --&gt; I[💰 借方：**********&lt;br/&gt;业务活动费用-其他经费-人员经费]
    D1 --&gt; I
    E1 --&gt; I
    F1 --&gt; I
    G1 --&gt; I
    H1 --&gt; I

    C1 --&gt; J[💳 贷方：********&lt;br/&gt;应付职工薪酬-养老保险-企业缴纳]
    D1 --&gt; K[💳 贷方：********&lt;br/&gt;应付职工薪酬-医疗保险-企业缴纳]
    E1 --&gt; L[💳 贷方：********&lt;br/&gt;应付职工薪酬-失业保险-企业缴纳]
    F1 --&gt; M[💳 贷方：22110306&lt;br/&gt;应付职工薪酬-工伤保险-企业缴纳]
    G1 --&gt; N[💳 贷方：221104&lt;br/&gt;应付职工薪酬-住房公积金-企业缴纳]
    H1 --&gt; O[💳 贷方：********&lt;br/&gt;应付职工薪酬-职业年金-企业缴纳]

    I --&gt; P[📋 辅助项配置&lt;br/&gt;部门+项目]
    J --&gt; Q[✅ 凭证生成完成]
    K --&gt; Q
    L --&gt; Q
    M --&gt; Q
    N --&gt; Q
    O --&gt; Q

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style H fill:#fff8e1
    style I fill:#ffebee
    style P fill:#fff3e0
    style Q fill:#e8f5e8
</div><p><strong>📊 企业四险两金规则映射表</strong></p>
<table>
<thead>
<tr>
<th>规则编号</th>
<th>保险项目</th>
<th>常量名称</th>
<th>借方科目</th>
<th>贷方科目</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>R401</td>
<td>企业养老保险</td>
<td>pensionInsuranceEntp</td>
<td>**********</td>
<td>********</td>
<td>机关事业单位基本养老保险</td>
</tr>
<tr>
<td>R402</td>
<td>企业医疗保险</td>
<td>medicalInsuranceEntp</td>
<td>**********</td>
<td>********</td>
<td>职工基本医疗保险</td>
</tr>
<tr>
<td>R403</td>
<td>企业失业保险</td>
<td>unemploymentInsuranceEntp</td>
<td>**********</td>
<td>********</td>
<td>职工失业保险</td>
</tr>
<tr>
<td>R404</td>
<td>企业工伤保险</td>
<td>injrInsuEntp</td>
<td>**********</td>
<td>22110306</td>
<td>职工工伤保险</td>
</tr>
<tr>
<td>R405</td>
<td>企业住房公积金</td>
<td>housingFundEntp</td>
<td>**********</td>
<td>221104</td>
<td>住房公积金</td>
</tr>
<tr>
<td>R406</td>
<td>企业职业年金</td>
<td>occupationalAnnuityEntp</td>
<td>**********</td>
<td>********</td>
<td>职业年金</td>
</tr>
</tbody>
</table>
<p><strong>📝 Drools规则实现</strong></p>
<p>将企业缴纳项目细分为每个具体的保险和公积金规则：</p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>// 规则组：企业四险两金计提规则
rule "R401_企业养老保险计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "机关事业单位基本养老保险缴费") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********"); // 业务活动费用-其他经费-人员经费
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业养老保险计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "养老保险"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-养老保险-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R401");
end

rule "R402_企业医疗保险计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "职工基本医疗保险缴费") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业医疗保险计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "医疗保险"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-医疗保险-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R402");
end

rule "R403_企业失业保险计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "职工失业保险缴费") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业失业保险计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "失业保险"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-失业保险-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R403");
end

rule "R404_企业工伤保险计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "职工工伤保险缴费") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业工伤保险计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "工伤保险"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("22110306"); // 应付职工薪酬-社会保险费-工伤保险-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R404");
end

rule "R405_企业住房公积金计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "住房公积金") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业住房公积金计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "住房公积金"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221104"); // 应付职工薪酬-住房公积金-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R405");
end

rule "R406_企业职业年金计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "职业年金缴费") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业职业年金计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "职业年金"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-职业年金-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R406");
end
</code></pre><h4 id="335-工会经费计提规则">3.3.5 工会经费计提规则 </h4>
<p><strong>🏛️ 工会经费计提规则流程图</strong></p>
<div class="mermaid">flowchart TD
    A[🏛️ 工会经费计提&lt;br/&gt;salaryType=4] --&gt; B[📋 工会经费支出识别]

    B --&gt; C[💰 R501_工会经费计提]

    C --&gt; D[📄 生成凭证分录]

    D --&gt; E[💰 借方：**********&lt;br/&gt;业务活动费用-其他经费-人员经费]
    D --&gt; F[💳 贷方：224199&lt;br/&gt;其他应付款-工会经费]

    E --&gt; G[📋 辅助项配置&lt;br/&gt;部门+项目]
    F --&gt; H[✅ 凭证生成完成]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#ffebee
    style F fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#e8f5e8
</div><p><strong>📊 工会经费计提规则映射表</strong></p>
<table>
<thead>
<tr>
<th>规则编号</th>
<th>项目名称</th>
<th>常量名称</th>
<th>借方科目</th>
<th>贷方科目</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>R501</td>
<td>工会经费计提</td>
<td>pubFeeEntp</td>
<td>**********</td>
<td>224199</td>
<td>工会经费支出</td>
</tr>
</tbody>
</table>
<p><strong>📝 Drools规则实现</strong></p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>// 规则组：工会经费计提规则
rule "R501_工会经费计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "4")
    $detail: SalaryDetailFact(reimType == "工会经费支出") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********"); // 业务活动费用-其他经费-人员经费
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("工会经费计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "工会经费"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-工会经费
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R501");
end
</code></pre><h4 id="336-特殊业务处理规则">3.3.6 特殊业务处理规则 </h4>
<p><strong>🔧 特殊人员处理规则流程图</strong></p>
<div class="mermaid">flowchart TD
    A[👥 特殊人员识别&lt;br/&gt;salaryType=2] --&gt; B{🔍 员工身份判断}

    B --&gt;|维修班人员| C[🔧 R601_维修班特殊往来账处理]
    B --&gt;|总务科人员| D[🏢 R602_总务科特殊往来账处理]
    B --&gt;|收费室人员| E[💳 R603_收费室特殊往来账处理]
    B --&gt;|血浆站人员| F[🩸 R604_单采血浆站特殊处理]
    B --&gt;|特定员工| G[👤 R605_吴军兵特殊实发处理]
    B --&gt;|普通员工| H[✅ 正常处理流程]

    C --&gt; C1[📄 生成特殊凭证分录]
    D --&gt; D1[📄 生成特殊凭证分录]
    E --&gt; E1[📄 生成特殊凭证分录]
    F --&gt; F1[📄 生成特殊凭证分录]
    G --&gt; G1[📄 生成特殊凭证分录]

    C1 --&gt; I[💰 借方：121803&lt;br/&gt;其他应收款-其他]
    D1 --&gt; I
    E1 --&gt; I
    F1 --&gt; J[💰 借方：121803&lt;br/&gt;其他应收款-其他]
    G1 --&gt; K[💳 贷方：121803&lt;br/&gt;其他应收款-其他]

    I --&gt; L[🏷️ 辅助项：往来单位4414&lt;br/&gt;员工编号]
    J --&gt; M[🏷️ 辅助项：往来单位7004&lt;br/&gt;员工编号]
    K --&gt; N[🏷️ 辅助项：往来单位3077&lt;br/&gt;员工编号]

    L --&gt; O[✅ 特殊处理完成]
    M --&gt; O
    N --&gt; O
    H --&gt; P[✅ 正常流程继续]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style H fill:#fff8e1
    style I fill:#ffebee
    style J fill:#ffebee
    style K fill:#e8f5e8
    style O fill:#e8f5e8
    style P fill:#e8f5e8
</div><p><strong>👥 特殊人员规则映射表</strong></p>
<table>
<thead>
<tr>
<th>规则编号</th>
<th>特殊人员类型</th>
<th>人员名单</th>
<th>科目代码</th>
<th>往来单位</th>
<th>处理方式</th>
</tr>
</thead>
<tbody>
<tr>
<td>R601</td>
<td>维修班人员</td>
<td>邹毅、田晓辉、刘黎等11人</td>
<td>121803</td>
<td>4414</td>
<td>借方特殊往来账</td>
</tr>
<tr>
<td>R602</td>
<td>总务科人员</td>
<td>吴思琪</td>
<td>121803</td>
<td>4414</td>
<td>借方特殊往来账</td>
</tr>
<tr>
<td>R603</td>
<td>收费室人员</td>
<td>罗亚婕</td>
<td>121803</td>
<td>4414</td>
<td>借方特殊往来账</td>
</tr>
<tr>
<td>R604</td>
<td>血浆站人员</td>
<td>血浆站全体人员</td>
<td>121803</td>
<td>7004</td>
<td>借方特殊往来账</td>
</tr>
<tr>
<td>R605</td>
<td>特定员工</td>
<td>吴军兵</td>
<td>121803</td>
<td>3077</td>
<td>贷方特殊实发</td>
</tr>
</tbody>
</table>
<p><strong>📝 Drools规则实现</strong></p>
<p>将特殊人员和特殊情况处理细分为具体规则：</p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>// 规则组：特殊人员处理规则
rule "R601_维修班特殊往来账处理"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(
        empCode in ("邹毅", "田晓辉", "刘黎", "刘亨强", "陈洪浩",
                   "吕勇", "苟鹏", "古代勇", "林贤培", "张丽", "邓钟")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("121803"); // 其他应收款-其他
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("维修班特殊往来账-" + $detail.getEmpName());
    debitEntry.setAuxiliaryItems(buildAuxItems("往来单位", "4414", "员工", $detail.getEmpCode()));

    $task.addVoucherEntry(debitEntry);
    $task.setRuleApplied("R601");
    $task.setSpecialProcessing(true);
end

rule "R602_总务科特殊往来账处理"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(empCode == "吴思琪") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("121803");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("总务科特殊往来账-" + $detail.getEmpName());
    debitEntry.setAuxiliaryItems(buildAuxItems("往来单位", "4414", "员工", $detail.getEmpCode()));

    $task.addVoucherEntry(debitEntry);
    $task.setRuleApplied("R602");
    $task.setSpecialProcessing(true);
end

rule "R603_收费室特殊往来账处理"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(empCode == "罗亚婕") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("121803");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("收费室特殊往来账-" + $detail.getEmpName());
    debitEntry.setAuxiliaryItems(buildAuxItems("往来单位", "4414", "员工", $detail.getEmpCode()));

    $task.addVoucherEntry(debitEntry);
    $task.setRuleApplied("R603");
    $task.setSpecialProcessing(true);
end

rule "R604_单采血浆站特殊处理"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(orgId == "单采血浆站") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("121803");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("血浆站特殊处理-" + $detail.getEmpName());
    debitEntry.setAuxiliaryItems(buildAuxItems("往来单位", "7004", "员工", $detail.getEmpCode()));

    $task.addVoucherEntry(debitEntry);
    $task.setRuleApplied("R604");
    $task.setSpecialProcessing(true);
end

rule "R605_吴军兵特殊实发处理"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(empCode == "吴军兵") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("121803"); // 其他应收款
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("吴军兵特殊实发处理");
    creditEntry.setAuxiliaryItems(buildAuxItems("往来单位", "3077", "员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R605");
    $task.setSpecialProcessing(true);
end
</code></pre><h4 id="337-条件判断和验证规则">3.3.7 条件判断和验证规则 </h4>
<p><strong>✅ 数据验证规则流程图</strong></p>
<div class="mermaid">flowchart TD
    A[📋 数据验证开始] --&gt; B[🔍 R701_工资任务基础数据验证]
    B --&gt; C[💰 R702_工资金额数据验证]
    C --&gt; D[📄 R703_工资明细数据验证]
    D --&gt; E[👤 R704_个人扣款配置检查]
    E --&gt; F[🔧 R705_特殊人员身份验证]
    F --&gt; G[🏢 R706_科室类型验证]
    G --&gt; H[👥 R707_人员类型验证]

    B --&gt;|验证失败| I[❌ 基础数据错误]
    C --&gt;|验证失败| J[❌ 金额数据错误]
    D --&gt;|验证失败| K[❌ 明细数据错误]
    E --&gt;|验证失败| L[❌ 配置缺失错误]
    F --&gt;|验证失败| M[❌ 特殊人员配置错误]
    G --&gt;|验证失败| N[❌ 科室类型错误]
    H --&gt;|验证失败| O[❌ 人员类型错误]

    H --&gt;|全部验证通过| P[✅ 数据验证通过]

    I --&gt; Q[🛑 终止处理]
    J --&gt; Q
    K --&gt; Q
    L --&gt; Q
    M --&gt; Q
    N --&gt; Q
    O --&gt; Q

    P --&gt; R[🔄 继续业务规则处理]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style H fill:#fff8e1
    style P fill:#e8f5e8
    style Q fill:#ffebee
    style R fill:#e8f5e8
</div><p><strong>📊 数据验证规则详细表</strong></p>
<table>
<thead>
<tr>
<th>规则编号</th>
<th>验证项目</th>
<th>验证条件</th>
<th>错误信息</th>
<th>优先级</th>
</tr>
</thead>
<tbody>
<tr>
<td>R701</td>
<td>基础数据验证</td>
<td>salaryId、ffMth、num不能为空</td>
<td>工资任务基础数据不完整</td>
<td>100</td>
</tr>
<tr>
<td>R702</td>
<td>金额数据验证</td>
<td>shouldPay、reducePay、realPay不能为负</td>
<td>工资金额数据异常</td>
<td>100</td>
</tr>
<tr>
<td>R703</td>
<td>明细数据验证</td>
<td>orgId、reimType、reimAmt不能为空</td>
<td>工资明细数据不完整</td>
<td>100</td>
</tr>
<tr>
<td>R704</td>
<td>个人扣款配置检查</td>
<td>个人扣款项目必须有配置</td>
<td>个人扣款未配置</td>
<td>95</td>
</tr>
<tr>
<td>R705</td>
<td>特殊人员身份验证</td>
<td>特殊人员必须有配置记录</td>
<td>特殊人员配置缺失</td>
<td>95</td>
</tr>
<tr>
<td>R706</td>
<td>科室类型验证</td>
<td>deptType必须为1或2</td>
<td>科室类型错误</td>
<td>95</td>
</tr>
<tr>
<td>R707</td>
<td>人员类型验证</td>
<td>empType必须在允许范围内</td>
<td>人员类型错误</td>
<td>95</td>
</tr>
</tbody>
</table>
<p><strong>📝 Drools规则实现</strong></p>
<p>将条件判断和验证逻辑细分为具体的检查规则：</p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>// 规则组：数据验证规则
rule "R701_工资任务基础数据验证"
    salience 100
when
    $task: SalaryTaskFact(
        salaryId == null ||
        ffMth == null || ffMth == "" ||
        num == null || num &lt;= 0
    )
then
    $task.addValidationError("工资任务基础数据不完整：工资ID、发放月份、工资条数不能为空");
    $task.setRuleApplied("R701");
end

rule "R702_工资金额数据验证"
    salience 100
when
    $task: SalaryTaskFact(
        shouldPay == null || shouldPay.compareTo(BigDecimal.ZERO) &lt; 0 ||
        reducePay == null || reducePay.compareTo(BigDecimal.ZERO) &lt; 0 ||
        realPay == null || realPay.compareTo(BigDecimal.ZERO) &lt; 0
    )
then
    $task.addValidationError("工资金额数据异常：应发、扣款、实发金额不能为负数");
    $task.setRuleApplied("R702");
end

rule "R703_工资明细数据验证"
    salience 100
when
    $task: SalaryTaskFact()
    $detail: SalaryDetailFact(
        orgId == null || orgId == "" ||
        reimType == null || reimType == "" ||
        reimAmt == null || reimAmt.compareTo(BigDecimal.ZERO) &lt;= 0
    ) from $task.details
then
    $task.addValidationError("工资明细数据不完整：科室ID、报销类型、报销金额不能为空或负数");
    $task.setRuleApplied("R703");
end

rule "R704_个人扣款配置检查"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(
        empCode != null &amp;&amp; empCode != "",
        reimType in ("养老保险", "医疗保险", "失业保险", "住房公积金", "职业年金",
                    "个人所得税", "房租费", "水费", "人力临时扣款", "财务临时扣款")
    ) from $task.details
    not PersonalDeductionConfig(empCode == $detail.empCode, deductionType == $detail.reimType)
then
    $task.addValidationError("个人扣款未配置: 员工[" + $detail.getEmpCode() + "]的[" + $detail.getReimType() + "]");
    $task.setRuleApplied("R704");
end

rule "R705_特殊人员身份验证"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(
        empCode in ("邹毅", "田晓辉", "刘黎", "刘亨强", "陈洪浩", "吕勇", "苟鹏",
                   "古代勇", "林贤培", "张丽", "邓钟", "吴思琪", "罗亚婕", "吴军兵")
    ) from $task.details
    not SpecialEmployeeConfig(empCode == $detail.empCode, activeFlag == "1")
then
    $task.addValidationError("特殊人员配置缺失: 员工[" + $detail.getEmpCode() + "]需要特殊处理配置");
    $task.setRuleApplied("R705");
end

rule "R706_科室类型验证"
    salience 95
when
    $task: SalaryTaskFact()
    $detail: SalaryDetailFact(
        deptType not in ("1", "2")
    ) from $task.details
then
    $task.addValidationError("科室类型错误: 科室[" + $detail.getOrgName() + "]类型必须为1(业务科室)或2(管理科室)");
    $task.setRuleApplied("R706");
end

rule "R707_人员类型验证"
    salience 95
when
    $task: SalaryTaskFact()
    $detail: SalaryDetailFact(
        empType not in ("在编", "血防占编", "编外-医技", "编外-护理", "编外-辅助岗位",
                       "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习",
                       "编外-其他专技", "编外-后勤", "编外-其他专技-见习", "编外-后勤-见习",
                       "借调", "返聘")
    ) from $task.details
then
    $task.addValidationError("人员类型错误: 员工[" + $detail.getEmpCode() + "]类型[" + $detail.getEmpType() + "]不在允许范围内");
    $task.setRuleApplied("R707");
end
</code></pre><h4 id="338-预算科目配置规则">3.3.8 预算科目配置规则 </h4>
<p><strong>💰 预算科目配置规则流程图</strong></p>
<div class="mermaid">flowchart TD
    A[💰 预算科目配置] --&gt; B{🏷️ 工资类型判断}

    B --&gt;|salaryType=1| C[📊 R801_工资计提预算科目配置]
    B --&gt;|salaryType=2| D[💳 R802_工资发放预算科目配置]
    B --&gt;|salaryType=3| E[🛡️ R803_企业四险两金预算科目配置]
    B --&gt;|salaryType=4| F[🏛️ R804_工会经费预算科目配置]

    C --&gt; G[📄 生成预算分录]
    D --&gt; G
    E --&gt; G
    F --&gt; G

    G --&gt; H[💰 预算科目：7201010301&lt;br/&gt;事业支出-医院支出其他资金支出-基本支出]

    H --&gt; I[📋 预算分录配置]
    I --&gt; J[✅ 预算配置完成]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style H fill:#ffebee
    style I fill:#fff3e0
    style J fill:#e8f5e8
</div><p><strong>📊 预算科目配置规则映射表</strong></p>
<table>
<thead>
<tr>
<th>规则编号</th>
<th>工资类型</th>
<th>预算科目</th>
<th>预算科目名称</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>R801</td>
<td>工资计提</td>
<td>7201010301</td>
<td>事业支出-基本支出-人员经费</td>
<td>工资计提预算</td>
</tr>
<tr>
<td>R802</td>
<td>工资发放</td>
<td>7201010301</td>
<td>事业支出-基本支出-人员经费</td>
<td>工资发放预算</td>
</tr>
<tr>
<td>R803</td>
<td>企业四险两金</td>
<td>7201010301</td>
<td>事业支出-基本支出-社会保障缴费</td>
<td>企业保险预算</td>
</tr>
<tr>
<td>R804</td>
<td>工会经费</td>
<td>7201010301</td>
<td>事业支出-基本支出-工会经费</td>
<td>工会经费预算</td>
</tr>
</tbody>
</table>
<h4 id="339-业务流程控制规则">3.3.9 业务流程控制规则 </h4>
<p><strong>🔄 业务流程控制规则流程图</strong></p>
<div class="mermaid">flowchart TD
    A[🔄 业务流程控制] --&gt; B[🔍 R901_工资发放流程前置检查]

    B --&gt; C{❌ 验证错误存在?}
    C --&gt;|是| D[❌ 流程终止&lt;br/&gt;VALIDATION_FAILED]
    C --&gt;|否| E[✅ R902_个人扣款配置完整性检查]

    E --&gt; F{⚙️ 配置完整?}
    F --&gt;|否| G[⚙️ 需要配置&lt;br/&gt;CONFIG_REQUIRED]
    F --&gt;|是| H[✅ R903_凭证生成条件检查]

    H --&gt; I{📄 条件满足?}
    I --&gt;|是| J[✅ 可以生成凭证&lt;br/&gt;readyForVoucher = true]
    I --&gt;|否| K[❌ 条件不满足]

    D --&gt; L[🛑 处理终止]
    G --&gt; M[⚙️ 等待配置]
    J --&gt; N[📄 继续凭证生成]
    K --&gt; L

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#fff8e1
    style D fill:#ffebee
    style E fill:#e8f5e8
    style F fill:#fff8e1
    style G fill:#fff3e0
    style H fill:#e8f5e8
    style I fill:#fff8e1
    style J fill:#e8f5e8
    style K fill:#ffebee
    style L fill:#ffebee
    style M fill:#fff3e0
    style N fill:#e8f5e8
</div><p><strong>📊 业务流程控制规则映射表</strong></p>
<table>
<thead>
<tr>
<th>规则编号</th>
<th>规则名称</th>
<th>检查内容</th>
<th>成功结果</th>
<th>失败结果</th>
</tr>
</thead>
<tbody>
<tr>
<td>R901</td>
<td>工资发放流程前置检查</td>
<td>验证错误列表</td>
<td>继续流程</td>
<td>VALIDATION_FAILED</td>
</tr>
<tr>
<td>R902</td>
<td>个人扣款配置完整性检查</td>
<td>员工配置完整性</td>
<td>继续流程</td>
<td>CONFIG_REQUIRED</td>
</tr>
<tr>
<td>R903</td>
<td>凭证生成条件检查</td>
<td>所有条件满足</td>
<td>readyForVoucher=true</td>
<td>条件不满足</td>
</tr>
</tbody>
</table>
<p><strong>📝 Drools规则实现</strong></p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>// 规则组：预算科目配置规则
rule "R801_工资计提预算科目配置"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact() from $task.details
then
    BudgetEntry budgetEntry = new BudgetEntry();
    budgetEntry.setBudgetSubject("7201010301"); // 事业支出-医院支出其他资金支出-基本支出
    budgetEntry.setBudgetSubjectName("事业支出-基本支出-人员经费");
    budgetEntry.setAmount($detail.getReimAmt());
    budgetEntry.setDescription("工资计提预算-" + $detail.getReimType() + "-" + $detail.getOrgName());

    $task.addBudgetEntry(budgetEntry);
    $task.setRuleApplied("R801");
end

rule "R802_工资发放预算科目配置"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact() from $task.details
then
    BudgetEntry budgetEntry = new BudgetEntry();
    budgetEntry.setBudgetSubject("7201010301");
    budgetEntry.setBudgetSubjectName("事业支出-基本支出-人员经费");
    budgetEntry.setAmount($detail.getReimAmt());
    budgetEntry.setDescription("工资发放预算-" + $detail.getReimType() + "-" + $detail.getOrgName());

    $task.addBudgetEntry(budgetEntry);
    $task.setRuleApplied("R802");
end

rule "R803_企业四险两金预算科目配置"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact() from $task.details
then
    BudgetEntry budgetEntry = new BudgetEntry();
    budgetEntry.setBudgetSubject("7201010301");
    budgetEntry.setBudgetSubjectName("事业支出-基本支出-社会保障缴费");
    budgetEntry.setAmount($detail.getReimAmt());
    budgetEntry.setDescription("企业保险预算-" + $detail.getReimType() + "-" + $detail.getOrgName());

    $task.addBudgetEntry(budgetEntry);
    $task.setRuleApplied("R803");
end

rule "R804_工会经费预算科目配置"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "4")
    $detail: SalaryDetailFact() from $task.details
then
    BudgetEntry budgetEntry = new BudgetEntry();
    budgetEntry.setBudgetSubject("7201010301");
    budgetEntry.setBudgetSubjectName("事业支出-基本支出-工会经费");
    budgetEntry.setAmount($detail.getReimAmt());
    budgetEntry.setDescription("工会经费预算-" + $detail.getOrgName());

    $task.addBudgetEntry(budgetEntry);
    $task.setRuleApplied("R804");
end

// 规则组：业务流程控制规则
rule "R901_工资发放流程前置检查"
    salience 85
when
    $task: SalaryTaskFact(salaryType == "2")
    $validationErrors: List(size &gt; 0) from $task.validationErrors
then
    $task.setProcessType(ProcessType.VALIDATION_FAILED);
    $task.addValidationError("工资发放流程前置检查失败，请先解决数据验证问题");
    $task.setRuleApplied("R901");
end

rule "R902_个人扣款配置完整性检查"
    salience 85
when
    $task: SalaryTaskFact(salaryType == "2", processType != ProcessType.VALIDATION_FAILED)
    $empCodes: List() from collect(SalaryDetailFact(empCode != null &amp;&amp; empCode != "") from $task.details)
    $configuredEmpCodes: List() from collect(PersonalDeductionConfig(activeFlag == "1").empCode)
    eval($empCodes.size() &gt; $configuredEmpCodes.size())
then
    $task.addValidationError("存在未配置个人扣款的员工，请先完成个人扣款配置");
    $task.setProcessType(ProcessType.CONFIG_REQUIRED);
    $task.setRuleApplied("R902");
end

rule "R903_凭证生成条件检查"
    salience 75
when
    $task: SalaryTaskFact(
        processType != ProcessType.VALIDATION_FAILED &amp;&amp;
        processType != ProcessType.CONFIG_REQUIRED
    )
    $validationErrors: List(size == 0) from $task.validationErrors
    $voucherEntries: List(size &gt; 0) from $task.voucherEntries
then
    $task.setReadyForVoucher(true);
    $task.setRuleApplied("R903");
    System.out.println("凭证生成条件检查通过，可以生成凭证");
end
</code></pre><h3 id="34-规则优先级和执行顺序">3.4 规则优先级和执行顺序 </h3>
<h4 id="341-规则优先级设计">3.4.1 规则优先级设计 </h4>
<p>基于业务逻辑的重要性和依赖关系，规则优先级设计如下：</p>
<table>
<thead>
<tr>
<th>优先级</th>
<th>规则类型</th>
<th>规则编号范围</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>100</td>
<td>数据验证规则</td>
<td>R701-R707</td>
<td>最高优先级，确保数据完整性</td>
</tr>
<tr>
<td>95</td>
<td>特殊业务处理规则</td>
<td>R601-R605, R704-R707</td>
<td>特殊人员和特殊情况处理</td>
</tr>
<tr>
<td>90</td>
<td>科目映射规则</td>
<td>R101-R109, R201-R205, R401-R406, R501</td>
<td>核心业务规则，科目映射</td>
</tr>
<tr>
<td>85</td>
<td>流程控制规则</td>
<td>R901-R902</td>
<td>业务流程前置检查</td>
</tr>
<tr>
<td>80</td>
<td>预算科目配置规则</td>
<td>R801-R804</td>
<td>预算科目配置</td>
</tr>
<tr>
<td>75</td>
<td>条件检查规则</td>
<td>R903</td>
<td>最终条件检查</td>
</tr>
<tr>
<td>70</td>
<td>汇总计算规则</td>
<td>R330</td>
<td>金额汇总和计算</td>
</tr>
<tr>
<td>50</td>
<td>异常处理规则</td>
<td>R005</td>
<td>异常情况处理</td>
</tr>
</tbody>
</table>
<h4 id="342-规则执行流程">3.4.2 规则执行流程 </h4>
<div class="mermaid">graph TD
    A[开始执行规则] --&gt; B[数据验证规则 100]
    B --&gt; C{验证通过?}
    C --&gt;|否| D[返回验证错误]
    C --&gt;|是| E[特殊业务处理规则 95]
    E --&gt; F[科目映射规则 90]
    F --&gt; G[流程控制规则 85]
    G --&gt; H{需要配置?}
    H --&gt;|是| I[返回配置要求]
    H --&gt;|否| J[预算科目配置规则 80]
    J --&gt; K[条件检查规则 75]
    K --&gt; L[汇总计算规则 70]
    L --&gt; M[返回执行结果]
</div><h4 id="343-规则依赖关系">3.4.3 规则依赖关系 </h4>
<ul>
<li><strong>数据验证规则</strong>：无依赖，最先执行</li>
<li><strong>特殊业务处理规则</strong>：依赖数据验证通过</li>
<li><strong>科目映射规则</strong>：依赖特殊业务处理完成</li>
<li><strong>流程控制规则</strong>：依赖科目映射完成</li>
<li><strong>预算科目配置规则</strong>：依赖流程控制检查通过</li>
<li><strong>条件检查规则</strong>：依赖所有业务规则执行完成</li>
<li><strong>汇总计算规则</strong>：依赖条件检查通过</li>
</ul>
<h3 id="35-代码迁移对照表">3.5 代码迁移对照表 </h3>
<h4 id="351-硬编码业务逻辑迁移对照">3.5.1 硬编码业务逻辑迁移对照 </h4>
<table>
<thead>
<tr>
<th>原代码位置</th>
<th>硬编码逻辑</th>
<th>对应Drools规则</th>
<th>迁移说明</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>salaryVcr.vue:330-337</code></td>
<td>工资类型判断switch语句</td>
<td>R001-R005</td>
<td>工资类型处理规则</td>
</tr>
<tr>
<td><code>salaryVcr.vue:427-435</code></td>
<td>个人扣款配置检查</td>
<td>R704, R902</td>
<td>个人扣款验证规则</td>
</tr>
<tr>
<td><code>EcsConst.java:271-284</code></td>
<td>人员类型常量数组</td>
<td>R707</td>
<td>人员类型验证规则</td>
</tr>
<tr>
<td><code>EcsConst.java:184-267</code></td>
<td>工资项目常量定义</td>
<td>R101-R501</td>
<td>工资项目科目映射规则</td>
</tr>
<tr>
<td><code>salaryConfig/index.vue:205-214</code></td>
<td>工资类别选项</td>
<td>R001-R004</td>
<td>工资类型配置规则</td>
</tr>
<tr>
<td><code>salaryConfig/index.vue:216-237</code></td>
<td>员工类别选项</td>
<td>R707</td>
<td>人员类型验证规则</td>
</tr>
<tr>
<td><code>salaryConfig/index.vue:238-247</code></td>
<td>科室类别选项</td>
<td>R706</td>
<td>科室类型验证规则</td>
</tr>
</tbody>
</table>
<h4 id="352-业务逻辑重构对照">3.5.2 业务逻辑重构对照 </h4>
<p><strong>原始硬编码逻辑</strong>：</p>
<pre data-role="codeBlock" data-info="javascript" class="language-javascript javascript"><code><span class="token comment">// 原代码：src/views/modules/ecs/reimNew/salaryReim/index.vue</span>
<span class="token keyword control-flow keyword-switch">switch</span> <span class="token punctuation">(</span>row<span class="token punctuation">.</span><span class="token property-access">salaryType</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-case">case</span> <span class="token string">'1'</span><span class="token operator">:</span> <span class="token keyword control-flow keyword-return">return</span> <span class="token string">'工资计提'</span>
    <span class="token keyword keyword-case">case</span> <span class="token string">'2'</span><span class="token operator">:</span> <span class="token keyword control-flow keyword-return">return</span> <span class="token string">'工资发放+三险两金'</span>
    <span class="token keyword keyword-case">case</span> <span class="token string">'3'</span><span class="token operator">:</span> <span class="token keyword control-flow keyword-return">return</span> <span class="token string">'企业四险两金'</span>
    <span class="token keyword keyword-case">case</span> <span class="token string">'4'</span><span class="token operator">:</span> <span class="token keyword control-flow keyword-return">return</span> <span class="token string">'工会经费'</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>重构后Drools规则</strong>：</p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>rule "R001_工资计提类型判断"
    salience 100
when
    $task: SalaryTaskFact(salaryType == "1")
then
    $task.setProcessType(ProcessType.DIRECT_VOUCHER);
    $task.setVoucherTemplate("SALARY_ACCRUAL");
end
</code></pre><p><strong>原始硬编码逻辑</strong>：</p>
<pre data-role="codeBlock" data-info="javascript" class="language-javascript javascript"><code><span class="token comment">// 原代码：src/views/modules/erp/vcrGen/vcrPane/components/salaryVcr.vue</span>
<span class="token keyword control-flow keyword-if">if</span> <span class="token punctuation">(</span>row<span class="token punctuation">.</span><span class="token property-access">salaryType</span> <span class="token operator">==</span> <span class="token string">'2'</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-const">const</span> indiToConfig <span class="token operator">=</span> <span class="token keyword control-flow keyword-await">await</span> <span class="token function">queryToConfigSalary</span><span class="token punctuation">(</span><span class="token punctuation">{</span><span class="token literal-property property">id</span><span class="token operator">:</span> checkedRowKeys<span class="token punctuation">.</span><span class="token property-access">value</span><span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">}</span><span class="token punctuation">)</span>
    <span class="token keyword control-flow keyword-if">if</span> <span class="token punctuation">(</span>indiToConfig<span class="token punctuation">.</span><span class="token property-access">code</span> <span class="token operator">==</span> <span class="token number">200</span> <span class="token operator">&amp;&amp;</span> indiToConfig<span class="token punctuation">.</span><span class="token property-access">data</span><span class="token punctuation">.</span><span class="token property-access">length</span> <span class="token operator">&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        indiToConfigModal<span class="token punctuation">.</span><span class="token property-access">value</span> <span class="token operator">=</span> <span class="token boolean">true</span>
        <span class="token keyword control-flow keyword-return">return</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>重构后Drools规则</strong>：</p>
<pre data-role="codeBlock" data-info="drools" class="language-drools drools"><code>rule "R704_个人扣款配置检查"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(empCode != null) from $task.details
    not PersonalDeductionConfig(empCode == $detail.empCode)
then
    $task.addValidationError("个人扣款未配置: " + $detail.getEmpCode());
end
</code></pre><h4 id="353-常量定义迁移对照">3.5.3 常量定义迁移对照 </h4>
<p><strong>原始常量定义</strong>：</p>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token comment">// 原代码：med-ecs/src/main/java/com/jp/med/ecs/modules/reimMgt/constant/EcsConst.java</span>
<span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token constant">ESTAB_STR_ARR</span> <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">{</span><span class="token string">"在编"</span><span class="token punctuation">,</span><span class="token string">"血防占编"</span><span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token constant">HIRE_STR_ARR</span> <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">{</span><span class="token string">"编外-医技"</span><span class="token punctuation">,</span><span class="token string">"编外-护理"</span><span class="token punctuation">,</span><span class="token string">"编外-辅助岗位"</span><span class="token punctuation">,</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><p><strong>重构后规则配置</strong>：</p>
<pre data-role="codeBlock" data-info="sql" class="language-sql sql"><code><span class="token comment">-- 规则配置表数据</span>
<span class="token keyword keyword-INSERT">INSERT</span> <span class="token keyword keyword-INTO">INTO</span> salary_rule_config <span class="token punctuation">(</span>rule_name<span class="token punctuation">,</span> rule_type<span class="token punctuation">,</span> rule_content<span class="token punctuation">)</span> <span class="token keyword keyword-VALUES">VALUES</span>
<span class="token punctuation">(</span><span class="token string">'人员类型验证'</span><span class="token punctuation">,</span> <span class="token string">'VALIDATION'</span><span class="token punctuation">,</span> <span class="token string">'empType in ("在编", "血防占编", "编外-医技", ...)'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h4 id="354-接口调用迁移对照">3.5.4 接口调用迁移对照 </h4>
<p><strong>原始接口调用</strong>：</p>
<pre data-role="codeBlock" data-info="javascript" class="language-javascript javascript"><code><span class="token comment">// 原代码：src/api/erp/vcr/vcrGen/vcrGen.ts</span>
<span class="token keyword module keyword-export">export</span> <span class="token keyword keyword-function">function</span> <span class="token function">queryToConfigSalary</span><span class="token punctuation">(</span><span class="token parameter"><span class="token literal-property property">param</span><span class="token operator">:</span> <span class="token known-class-name class-name">Object</span></span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword control-flow keyword-return">return</span> <span class="token function">request</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
        <span class="token literal-property property">url</span><span class="token operator">:</span> <span class="token string">'erp/erpVcrDetail/queryToCfgTempReduce'</span><span class="token punctuation">,</span>
        <span class="token literal-property property">method</span><span class="token operator">:</span> <span class="token maybe-class-name">RequestType</span><span class="token punctuation">.</span><span class="token constant">POST</span><span class="token punctuation">,</span>
        <span class="token literal-property property">data</span><span class="token operator">:</span> param<span class="token punctuation">,</span>
    <span class="token punctuation">}</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>重构后规则引擎调用</strong>：</p>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token comment">// 新代码：SalaryVoucherRuleService.java</span>
<span class="token keyword keyword-public">public</span> <span class="token class-name">SalaryVoucherDecision</span> <span class="token function">executeRules</span><span class="token punctuation">(</span><span class="token class-name">SalaryTaskRequest</span> request<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token class-name">SalaryTaskFact</span> fact <span class="token operator">=</span> <span class="token function">buildSalaryTaskFact</span><span class="token punctuation">(</span>request<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token class-name">KieSession</span> kieSession <span class="token operator">=</span> kieContainer<span class="token punctuation">.</span><span class="token function">newKieSession</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    kieSession<span class="token punctuation">.</span><span class="token function">insert</span><span class="token punctuation">(</span>fact<span class="token punctuation">)</span><span class="token punctuation">;</span>
    kieSession<span class="token punctuation">.</span><span class="token function">fireAllRules</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> <span class="token function">buildDecision</span><span class="token punctuation">(</span>fact<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="4-功能需求-">4. 功能需求 📝 </h2>
<h3 id="41-规则引擎核心功能">4.1 规则引擎核心功能 </h3>
<h4 id="411-规则执行引擎">4.1.1 规则执行引擎 </h4>
<ul>
<li><strong>功能描述</strong>：基于Drools的规则执行引擎</li>
<li><strong>输入</strong>：工资任务数据、明细数据</li>
<li><strong>输出</strong>：凭证生成决策、验证结果</li>
<li><strong>性能要求</strong>：单次规则执行时间 &lt; 500ms</li>
</ul>
<h4 id="412-规则热加载">4.1.2 规则热加载 </h4>
<ul>
<li><strong>功能描述</strong>：支持规则动态加载，无需重启服务</li>
<li><strong>触发方式</strong>：规则配置变更后自动触发</li>
<li><strong>回滚机制</strong>：规则加载失败时自动回滚到上一版本</li>
</ul>
<h4 id="413-规则版本管理">4.1.3 规则版本管理 </h4>
<ul>
<li><strong>功能描述</strong>：维护规则版本历史，支持版本回退</li>
<li><strong>版本策略</strong>：语义化版本号(major.minor.patch)</li>
<li><strong>存储方式</strong>：数据库+文件系统双重存储</li>
</ul>
<h3 id="42-规则配置管理">4.2 规则配置管理 </h3>
<h4 id="421-工资类型规则配置">4.2.1 工资类型规则配置 </h4>
<ul>
<li><strong>配置项</strong>：工资类型、处理方式、模板选择</li>
<li><strong>界面要求</strong>：表格形式，支持增删改查</li>
<li><strong>验证规则</strong>：工资类型唯一性、处理方式有效性</li>
</ul>
<h4 id="422-科目映射规则配置">4.2.2 科目映射规则配置 </h4>
<ul>
<li><strong>配置项</strong>：报销类型、会计科目、借贷方向、金额计算公式</li>
<li><strong>界面要求</strong>：树形结构展示科目层级</li>
<li><strong>公式支持</strong>：支持简单的数学表达式</li>
</ul>
<h4 id="423-辅助项规则配置">4.2.3 辅助项规则配置 </h4>
<ul>
<li><strong>配置项</strong>：辅助项类型、取值规则、默认值</li>
<li><strong>支持类型</strong>：部门、项目、资金性质、现金流量</li>
<li><strong>取值规则</strong>：固定值、动态取值、计算取值</li>
</ul>
<h3 id="43-规则管理界面">4.3 规则管理界面 </h3>
<h4 id="431-规则列表页面">4.3.1 规则列表页面 </h4>
<ul>
<li><strong>功能</strong>：展示所有规则，支持搜索、筛选、排序</li>
<li><strong>操作</strong>：新增、编辑、删除、启用/禁用、测试</li>
<li><strong>状态显示</strong>：规则状态、最后修改时间、修改人</li>
</ul>
<h4 id="432-规则编辑器">4.3.2 规则编辑器 </h4>
<ul>
<li><strong>功能</strong>：可视化规则编辑器，支持拖拽操作</li>
<li><strong>语法检查</strong>：实时语法验证和错误提示</li>
<li><strong>预览功能</strong>：规则预览和测试执行</li>
</ul>
<h4 id="433-规则测试工具">4.3.3 规则测试工具 </h4>
<ul>
<li><strong>功能</strong>：规则单元测试和集成测试</li>
<li><strong>测试数据</strong>：支持导入测试数据或手动输入</li>
<li><strong>结果对比</strong>：期望结果与实际结果对比</li>
</ul>
<h2 id="5-技术实现方案-️">5. 技术实现方案 🛠️ </h2>
<h3 id="51-技术选型">5.1 技术选型 </h3>
<p>基于当前系统架构，技术选型如下：</p>
<ul>
<li><strong>规则引擎</strong>：Drools 7.74.1.Final</li>
<li><strong>后端框架</strong>：Spring Boot 2.7.x</li>
<li><strong>数据库</strong>：PostgreSQL 12+</li>
<li><strong>前端框架</strong>：Vue 3 + Naive UI</li>
<li><strong>构建工具</strong>：Maven 3.8+</li>
<li><strong>JDK版本</strong>：JDK 8+</li>
<li><strong>缓存</strong>：Redis（规则缓存）</li>
<li><strong>工作流</strong>：BPM流程引擎（报销审批）</li>
</ul>
<h3 id="52-数据库设计">5.2 数据库设计 </h3>
<pre data-role="codeBlock" data-info="sql" class="language-sql sql"><code><span class="token comment">-- 规则配置表</span>
<span class="token keyword keyword-CREATE">CREATE</span> <span class="token keyword keyword-TABLE">TABLE</span> salary_rule_config <span class="token punctuation">(</span>
    id <span class="token keyword keyword-SERIAL">SERIAL</span> <span class="token keyword keyword-PRIMARY">PRIMARY</span> <span class="token keyword keyword-KEY">KEY</span><span class="token punctuation">,</span>
    rule_name <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">100</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    rule_type <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    rule_content <span class="token keyword keyword-TEXT">TEXT</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    version <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">20</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    <span class="token keyword keyword-status">status</span> <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">20</span><span class="token punctuation">)</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token string">'ACTIVE'</span><span class="token punctuation">,</span>
    priority <span class="token keyword keyword-INTEGER">INTEGER</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token number">0</span><span class="token punctuation">,</span>
    create_time <span class="token keyword keyword-TIMESTAMP">TIMESTAMP</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token keyword keyword-CURRENT_TIMESTAMP">CURRENT_TIMESTAMP</span><span class="token punctuation">,</span>
    update_time <span class="token keyword keyword-TIMESTAMP">TIMESTAMP</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token keyword keyword-CURRENT_TIMESTAMP">CURRENT_TIMESTAMP</span><span class="token punctuation">,</span>
    creator <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    updater <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span>
<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">-- 规则版本历史表</span>
<span class="token keyword keyword-CREATE">CREATE</span> <span class="token keyword keyword-TABLE">TABLE</span> salary_rule_version <span class="token punctuation">(</span>
    id <span class="token keyword keyword-SERIAL">SERIAL</span> <span class="token keyword keyword-PRIMARY">PRIMARY</span> <span class="token keyword keyword-KEY">KEY</span><span class="token punctuation">,</span>
    rule_id <span class="token keyword keyword-INTEGER">INTEGER</span> <span class="token keyword keyword-REFERENCES">REFERENCES</span> salary_rule_config<span class="token punctuation">(</span>id<span class="token punctuation">)</span><span class="token punctuation">,</span>
    version <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">20</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    rule_content <span class="token keyword keyword-TEXT">TEXT</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    change_log <span class="token keyword keyword-TEXT">TEXT</span><span class="token punctuation">,</span>
    create_time <span class="token keyword keyword-TIMESTAMP">TIMESTAMP</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token keyword keyword-CURRENT_TIMESTAMP">CURRENT_TIMESTAMP</span><span class="token punctuation">,</span>
    creator <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span>
<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">-- 工资类型规则配置表</span>
<span class="token keyword keyword-CREATE">CREATE</span> <span class="token keyword keyword-TABLE">TABLE</span> salary_type_rule <span class="token punctuation">(</span>
    id <span class="token keyword keyword-SERIAL">SERIAL</span> <span class="token keyword keyword-PRIMARY">PRIMARY</span> <span class="token keyword keyword-KEY">KEY</span><span class="token punctuation">,</span>
    salary_type <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">10</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    process_type <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    template_code <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    description <span class="token keyword keyword-TEXT">TEXT</span><span class="token punctuation">,</span>
    active_flag <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token string">'1'</span><span class="token punctuation">,</span>
    create_time <span class="token keyword keyword-TIMESTAMP">TIMESTAMP</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token keyword keyword-CURRENT_TIMESTAMP">CURRENT_TIMESTAMP</span>
<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">-- 科目映射规则表</span>
<span class="token keyword keyword-CREATE">CREATE</span> <span class="token keyword keyword-TABLE">TABLE</span> salary_subject_mapping <span class="token punctuation">(</span>
    id <span class="token keyword keyword-SERIAL">SERIAL</span> <span class="token keyword keyword-PRIMARY">PRIMARY</span> <span class="token keyword keyword-KEY">KEY</span><span class="token punctuation">,</span>
    salary_type <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">10</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    dept_type <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">20</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    emp_type <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">20</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    reim_type <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    debit_subject <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">20</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    credit_subject <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">20</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    budget_subject <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">20</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    auxiliary_config <span class="token keyword keyword-TEXT">TEXT</span><span class="token punctuation">,</span>
    priority <span class="token keyword keyword-INTEGER">INTEGER</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token number">0</span><span class="token punctuation">,</span>
    active_flag <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token string">'1'</span><span class="token punctuation">,</span>
    create_time <span class="token keyword keyword-TIMESTAMP">TIMESTAMP</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token keyword keyword-CURRENT_TIMESTAMP">CURRENT_TIMESTAMP</span>
<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">-- 特殊人员配置表</span>
<span class="token keyword keyword-CREATE">CREATE</span> <span class="token keyword keyword-TABLE">TABLE</span> salary_special_employee <span class="token punctuation">(</span>
    id <span class="token keyword keyword-SERIAL">SERIAL</span> <span class="token keyword keyword-PRIMARY">PRIMARY</span> <span class="token keyword keyword-KEY">KEY</span><span class="token punctuation">,</span>
    emp_code <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    emp_name <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    special_type <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    subject_code <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">20</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    auxiliary_code <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    description <span class="token keyword keyword-TEXT">TEXT</span><span class="token punctuation">,</span>
    active_flag <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token string">'1'</span>
<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">-- 辅助项配置表</span>
<span class="token keyword keyword-CREATE">CREATE</span> <span class="token keyword keyword-TABLE">TABLE</span> salary_auxiliary_config <span class="token punctuation">(</span>
    id <span class="token keyword keyword-SERIAL">SERIAL</span> <span class="token keyword keyword-PRIMARY">PRIMARY</span> <span class="token keyword keyword-KEY">KEY</span><span class="token punctuation">,</span>
    aux_type <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    aux_code <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">50</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    aux_name <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">100</span><span class="token punctuation">)</span> <span class="token operator">NOT</span> <span class="token boolean">NULL</span><span class="token punctuation">,</span>
    mapping_rule <span class="token keyword keyword-TEXT">TEXT</span><span class="token punctuation">,</span>
    active_flag <span class="token keyword keyword-VARCHAR">VARCHAR</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token keyword keyword-DEFAULT">DEFAULT</span> <span class="token string">'1'</span>
<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h3 id="53-科目映射规则详细配置">5.3 科目映射规则详细配置 </h3>
<h4 id="531-工资计提科目映射表">5.3.1 工资计提科目映射表 </h4>
<p>基于系统实际配置，工资计提科目映射规则如下：</p>
<table>
<thead>
<tr>
<th>部门类型</th>
<th>人员类型</th>
<th>工资项目</th>
<th>借方科目</th>
<th>贷方科目</th>
<th>辅助项配置</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>业务科室(1)</td>
<td>在编/招聘</td>
<td>岗位工资</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
<td>业务活动费用-其他经费-人员经费</td>
</tr>
<tr>
<td>业务科室(1)</td>
<td>在编/招聘</td>
<td>薪级工资</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
<td>基本工资科目</td>
</tr>
<tr>
<td>业务科室(1)</td>
<td>在编/招聘</td>
<td>护士10%</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
<td>护士津贴</td>
</tr>
<tr>
<td>业务科室(1)</td>
<td>在编/招聘</td>
<td>地区附加津贴</td>
<td>**********</td>
<td>221102</td>
<td>部门+项目</td>
<td>国家统一规定津贴补贴</td>
</tr>
<tr>
<td>业务科室(1)</td>
<td>在编/招聘</td>
<td>护龄补贴</td>
<td>**********</td>
<td>221102</td>
<td>部门+项目</td>
<td>国家统一规定津贴补贴</td>
</tr>
<tr>
<td>业务科室(1)</td>
<td>在编/招聘</td>
<td>基础绩效工资</td>
<td>**********</td>
<td>221103</td>
<td>部门+项目</td>
<td>规范津贴补贴</td>
</tr>
<tr>
<td>业务科室(1)</td>
<td>在编/招聘</td>
<td>人力临时增加</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
<td>临时工资调整</td>
</tr>
<tr>
<td>业务科室(1)</td>
<td>在编/招聘</td>
<td>财务临时增加</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
<td>财务调整</td>
</tr>
<tr>
<td>业务科室(1)</td>
<td>临聘</td>
<td>岗位工资</td>
<td>**********</td>
<td>221101</td>
<td>部门+项目</td>
<td>临聘人员基本工资</td>
</tr>
<tr>
<td>管理科室(2)</td>
<td>在编</td>
<td>岗位工资</td>
<td>5201030199</td>
<td>221101</td>
<td>部门+项目</td>
<td>单位管理费用-其他经费-人员经费</td>
</tr>
<tr>
<td>管理科室(2)</td>
<td>在编</td>
<td>薪级工资</td>
<td>5201030199</td>
<td>221101</td>
<td>部门+项目</td>
<td>管理科室基本工资</td>
</tr>
<tr>
<td>管理科室(2)</td>
<td>招聘</td>
<td>岗位工资</td>
<td>5201030199</td>
<td>221101</td>
<td>部门+项目</td>
<td>招聘人员工资</td>
</tr>
<tr>
<td>管理科室(2)</td>
<td>临聘</td>
<td>岗位工资</td>
<td>5201030199</td>
<td>221101</td>
<td>部门+项目</td>
<td>临聘人员工资</td>
</tr>
</tbody>
</table>
<h4 id="532-工资发放科目映射表">5.3.2 工资发放科目映射表 </h4>
<table>
<thead>
<tr>
<th>业务类型</th>
<th>借方科目</th>
<th>贷方科目</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>基本工资冲销</td>
<td>221101</td>
<td>**********(负数)</td>
<td>冲销计提的基本工资</td>
</tr>
<tr>
<td>津贴补贴冲销</td>
<td>221102</td>
<td>**********(负数)</td>
<td>冲销计提的津贴补贴</td>
</tr>
<tr>
<td>绩效工资冲销</td>
<td>221103</td>
<td>**********(负数)</td>
<td>冲销计提的绩效工资</td>
</tr>
<tr>
<td>个人养老保险</td>
<td>-</td>
<td>********</td>
<td>个人缴纳养老保险</td>
</tr>
<tr>
<td>个人医疗保险</td>
<td>-</td>
<td>********</td>
<td>个人缴纳医疗保险</td>
</tr>
<tr>
<td>个人失业保险</td>
<td>-</td>
<td>********</td>
<td>个人缴纳失业保险</td>
</tr>
<tr>
<td>个人住房公积金</td>
<td>-</td>
<td>221104</td>
<td>个人缴纳住房公积金</td>
</tr>
<tr>
<td>个人职业年金</td>
<td>-</td>
<td>********</td>
<td>个人缴纳职业年金</td>
</tr>
<tr>
<td>工会会费</td>
<td>-</td>
<td>其他应付款</td>
<td>工会会费代扣</td>
</tr>
<tr>
<td>个人所得税</td>
<td>-</td>
<td>210207</td>
<td>个人所得税代扣</td>
</tr>
<tr>
<td>实发工资</td>
<td>-</td>
<td>100201</td>
<td>银行存款实发</td>
</tr>
</tbody>
</table>
<h4 id="533-特殊人员处理配置">5.3.3 特殊人员处理配置 </h4>
<table>
<thead>
<tr>
<th>特殊类型</th>
<th>人员列表</th>
<th>科目代码</th>
<th>辅助项</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>维修班</td>
<td>邹毅,田晓辉,刘黎等11人</td>
<td>121803</td>
<td>往来单位4414</td>
<td>特殊往来账处理</td>
</tr>
<tr>
<td>总务科特殊</td>
<td>吴思琪</td>
<td>121803</td>
<td>往来单位4414</td>
<td>特殊往来账处理</td>
</tr>
<tr>
<td>收费室特殊</td>
<td>罗亚婕</td>
<td>121803</td>
<td>往来单位4414</td>
<td>特殊往来账处理</td>
</tr>
<tr>
<td>单采血浆站</td>
<td>血浆站全体人员</td>
<td>121803</td>
<td>往来单位7004</td>
<td>血浆站特殊处理</td>
</tr>
<tr>
<td>吴军兵特殊</td>
<td>吴军兵</td>
<td>-</td>
<td>往来单位3077</td>
<td>特殊实发处理</td>
</tr>
</tbody>
</table>
<h3 id="54-核心代码结构">5.4 核心代码结构 </h3>
<p>基于现有ECS模块结构，规则引擎代码组织如下：</p>
<pre data-role="codeBlock" data-info="" class="language-text"><code>med-ecs/src/main/java/com/jp/med/ecs/modules/
├── salary/                              # 新增工资规则模块
│   ├── rule/
│   │   ├── engine/
│   │   │   ├── SalaryRuleEngine.java           # 规则引擎核心
│   │   │   ├── RuleExecutor.java               # 规则执行器
│   │   │   └── RuleLoader.java                 # 规则加载器
│   │   ├── fact/
│   │   │   ├── SalaryTaskFact.java             # 工资任务事实
│   │   │   ├── SalaryDetailFact.java           # 工资明细事实
│   │   │   └── SalaryContextFact.java          # 工资上下文事实
│   │   ├── decision/
│   │   │   ├── SalaryVoucherDecision.java      # 凭证决策结果
│   │   │   ├── ProcessType.java                # 处理类型枚举
│   │   │   └── VoucherEntry.java               # 凭证分录
│   │   └── config/
│   │       ├── RuleConfig.java                 # 规则配置
│   │       └── DroolsConfig.java               # Drools配置
│   ├── service/
│   │   ├── SalaryVoucherRuleService.java       # 工资凭证规则服务
│   │   ├── RuleConfigService.java              # 规则配置服务
│   │   └── RuleManagementService.java          # 规则管理服务
│   └── controller/
│       ├── SalaryRuleController.java           # 规则执行控制器
│       └── RuleManagementController.java       # 规则管理控制器
└── reimMgt/                             # 现有报销管理模块
    ├── service/
    │   └── write/impl/
    │       └── EcsReimSalaryTaskWriteServiceImpl.java  # 集成规则引擎
    └── controller/
        └── EcsReimSalaryTaskController.java            # 增加规则调用

# 前端代码结构
src/views/modules/ecs/
├── salary/                              # 新增工资规则管理页面
│   ├── rule-management/
│   │   ├── index.vue                           # 规则管理主页面
│   │   ├── components/
│   │   │   ├── RuleEditor.vue                  # 规则编辑器
│   │   │   ├── RuleTestTool.vue                # 规则测试工具
│   │   │   └── RuleVersionHistory.vue          # 规则版本历史
│   │   └── hooks/
│   │       └── useRuleManagement.ts            # 规则管理逻辑
│   └── config/
│       ├── salary-type-config.vue              # 工资类型配置
│       └── subject-mapping-config.vue          # 科目映射配置
└── reimMgt/                             # 现有报销管理模块
    └── salaryReim/
        └── index.vue                           # 集成规则引擎调用
</code></pre><h3 id="54-drools配置">5.4 Drools配置 </h3>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token annotation punctuation">@Configuration</span>
<span class="token annotation punctuation">@EnableConfigurationProperties</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">DroolsConfig</span> <span class="token punctuation">{</span>

    <span class="token annotation punctuation">@Bean</span>
    <span class="token keyword keyword-public">public</span> <span class="token class-name">KieContainer</span> <span class="token function">kieContainer</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token class-name">KieServices</span> kieServices <span class="token operator">=</span> <span class="token class-name">KieServices<span class="token punctuation">.</span>Factory</span><span class="token punctuation">.</span><span class="token function">get</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token class-name">KieFileSystem</span> kieFileSystem <span class="token operator">=</span> kieServices<span class="token punctuation">.</span><span class="token function">newKieFileSystem</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token comment">// 加载规则文件</span>
        <span class="token function">loadRulesFromDatabase</span><span class="token punctuation">(</span>kieFileSystem<span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token class-name">KieBuilder</span> kieBuilder <span class="token operator">=</span> kieServices<span class="token punctuation">.</span><span class="token function">newKieBuilder</span><span class="token punctuation">(</span>kieFileSystem<span class="token punctuation">)</span><span class="token punctuation">;</span>
        kieBuilder<span class="token punctuation">.</span><span class="token function">buildAll</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token class-name">KieModule</span> kieModule <span class="token operator">=</span> kieBuilder<span class="token punctuation">.</span><span class="token function">getKieModule</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> kieServices<span class="token punctuation">.</span><span class="token function">newKieContainer</span><span class="token punctuation">(</span>kieModule<span class="token punctuation">.</span><span class="token function">getReleaseId</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token keyword keyword-private">private</span> <span class="token keyword keyword-void">void</span> <span class="token function">loadRulesFromDatabase</span><span class="token punctuation">(</span><span class="token class-name">KieFileSystem</span> kieFileSystem<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// 从数据库加载规则配置</span>
        <span class="token class-name">List</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">SalaryRuleConfig</span><span class="token punctuation">&gt;</span></span> rules <span class="token operator">=</span> ruleConfigService<span class="token punctuation">.</span><span class="token function">getActiveRules</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token class-name">SalaryRuleConfig</span> rule <span class="token operator">:</span> rules<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token class-name">String</span> rulePath <span class="token operator">=</span> <span class="token string">"src/main/resources/rules/"</span> <span class="token operator">+</span> rule<span class="token punctuation">.</span><span class="token function">getRuleName</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">+</span> <span class="token string">".drl"</span><span class="token punctuation">;</span>
            kieFileSystem<span class="token punctuation">.</span><span class="token function">write</span><span class="token punctuation">(</span>rulePath<span class="token punctuation">,</span> rule<span class="token punctuation">.</span><span class="token function">getRuleContent</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="6-业务流程设计-">6. 业务流程设计 🔄 </h2>
<h3 id="61-工资凭证生成流程">6.1 工资凭证生成流程 </h3>
<div class="mermaid">sequenceDiagram
    participant U as 用户
    participant C as Controller
    participant S as SalaryVoucherService
    participant R as RuleEngine
    participant E as ERPService
    participant B as BPMService

    U-&gt;&gt;C: 提交工资凭证生成请求
    C-&gt;&gt;S: 调用工资凭证服务
    S-&gt;&gt;S: 构建事实对象
    S-&gt;&gt;R: 执行规则引擎
    R-&gt;&gt;R: 规则匹配和执行
    R-&gt;&gt;S: 返回决策结果

    alt 直接生成凭证
        S-&gt;&gt;E: 调用ERP生成凭证
        E-&gt;&gt;S: 返回凭证信息
    else 报销审批流程
        S-&gt;&gt;B: 启动BPM流程
        B-&gt;&gt;S: 返回流程实例ID
    end

    S-&gt;&gt;C: 返回处理结果
    C-&gt;&gt;U: 返回响应
</div><h3 id="62-规则配置管理流程">6.2 规则配置管理流程 </h3>
<div class="mermaid">graph TD
    A[规则配置请求] --&gt; B[规则验证]
    B --&gt; C{验证通过?}
    C --&gt;|否| D[返回错误信息]
    C --&gt;|是| E[保存规则配置]
    E --&gt; F[生成规则文件]
    F --&gt; G[规则热加载]
    G --&gt; H{加载成功?}
    H --&gt;|否| I[回滚到上一版本]
    H --&gt;|是| J[更新规则状态]
    J --&gt; K[返回成功结果]
    I --&gt; L[返回回滚信息]
</div><h2 id="7-接口设计-">7. 接口设计 🔌 </h2>
<h3 id="71-规则执行接口">7.1 规则执行接口 </h3>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token annotation punctuation">@RestController</span>
<span class="token annotation punctuation">@RequestMapping</span><span class="token punctuation">(</span><span class="token string">"/api/salary/rule"</span><span class="token punctuation">)</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">SalaryRuleController</span> <span class="token punctuation">{</span>

    <span class="token doc-comment comment">/**
     * 执行工资凭证规则
     */</span>
    <span class="token annotation punctuation">@PostMapping</span><span class="token punctuation">(</span><span class="token string">"/execute"</span><span class="token punctuation">)</span>
    <span class="token keyword keyword-public">public</span> <span class="token class-name">CommonResult</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">SalaryVoucherDecision</span><span class="token punctuation">&gt;</span></span> <span class="token function">executeRules</span><span class="token punctuation">(</span>
            <span class="token annotation punctuation">@RequestBody</span> <span class="token class-name">SalaryTaskRequest</span> request<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token class-name">SalaryVoucherDecision</span> decision <span class="token operator">=</span> salaryVoucherRuleService<span class="token punctuation">.</span><span class="token function">executeRules</span><span class="token punctuation">(</span>request<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token class-name">CommonResult</span><span class="token punctuation">.</span><span class="token function">success</span><span class="token punctuation">(</span>decision<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token doc-comment comment">/**
     * 验证规则
     */</span>
    <span class="token annotation punctuation">@PostMapping</span><span class="token punctuation">(</span><span class="token string">"/validate"</span><span class="token punctuation">)</span>
    <span class="token keyword keyword-public">public</span> <span class="token class-name">CommonResult</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">ValidationResult</span><span class="token punctuation">&gt;</span></span> <span class="token function">validateRules</span><span class="token punctuation">(</span>
            <span class="token annotation punctuation">@RequestBody</span> <span class="token class-name">SalaryTaskRequest</span> request<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token class-name">ValidationResult</span> result <span class="token operator">=</span> salaryVoucherRuleService<span class="token punctuation">.</span><span class="token function">validateRules</span><span class="token punctuation">(</span>request<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token class-name">CommonResult</span><span class="token punctuation">.</span><span class="token function">success</span><span class="token punctuation">(</span>result<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="72-规则管理接口">7.2 规则管理接口 </h3>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token annotation punctuation">@RestController</span>
<span class="token annotation punctuation">@RequestMapping</span><span class="token punctuation">(</span><span class="token string">"/api/salary/rule-config"</span><span class="token punctuation">)</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">RuleManagementController</span> <span class="token punctuation">{</span>

    <span class="token doc-comment comment">/**
     * 查询规则列表
     */</span>
    <span class="token annotation punctuation">@PostMapping</span><span class="token punctuation">(</span><span class="token string">"/list"</span><span class="token punctuation">)</span>
    <span class="token keyword keyword-public">public</span> <span class="token class-name">CommonResult</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">PageResult</span><span class="token punctuation">&lt;</span><span class="token class-name">SalaryRuleConfig</span><span class="token punctuation">&gt;</span><span class="token punctuation">&gt;</span></span> <span class="token function">getRuleList</span><span class="token punctuation">(</span>
            <span class="token annotation punctuation">@RequestBody</span> <span class="token class-name">RuleQueryRequest</span> request<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token class-name">PageResult</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">SalaryRuleConfig</span><span class="token punctuation">&gt;</span></span> result <span class="token operator">=</span> ruleConfigService<span class="token punctuation">.</span><span class="token function">queryRules</span><span class="token punctuation">(</span>request<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token class-name">CommonResult</span><span class="token punctuation">.</span><span class="token function">success</span><span class="token punctuation">(</span>result<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token doc-comment comment">/**
     * 保存规则
     */</span>
    <span class="token annotation punctuation">@PostMapping</span><span class="token punctuation">(</span><span class="token string">"/save"</span><span class="token punctuation">)</span>
    <span class="token keyword keyword-public">public</span> <span class="token class-name">CommonResult</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">Void</span><span class="token punctuation">&gt;</span></span> <span class="token function">saveRule</span><span class="token punctuation">(</span><span class="token annotation punctuation">@RequestBody</span> <span class="token class-name">SalaryRuleConfig</span> rule<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        ruleConfigService<span class="token punctuation">.</span><span class="token function">saveRule</span><span class="token punctuation">(</span>rule<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token class-name">CommonResult</span><span class="token punctuation">.</span><span class="token function">success</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token doc-comment comment">/**
     * 测试规则
     */</span>
    <span class="token annotation punctuation">@PostMapping</span><span class="token punctuation">(</span><span class="token string">"/test"</span><span class="token punctuation">)</span>
    <span class="token keyword keyword-public">public</span> <span class="token class-name">CommonResult</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">RuleTestResult</span><span class="token punctuation">&gt;</span></span> <span class="token function">testRule</span><span class="token punctuation">(</span><span class="token annotation punctuation">@RequestBody</span> <span class="token class-name">RuleTestRequest</span> request<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token class-name">RuleTestResult</span> result <span class="token operator">=</span> ruleConfigService<span class="token punctuation">.</span><span class="token function">testRule</span><span class="token punctuation">(</span>request<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token class-name">CommonResult</span><span class="token punctuation">.</span><span class="token function">success</span><span class="token punctuation">(</span>result<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="8-前端界面设计-">8. 前端界面设计 🎨 </h2>
<h3 id="81-规则管理主页面">8.1 规则管理主页面 </h3>
<ul>
<li><strong>页面路径</strong>：<code>/ecs/salary/rule-management</code></li>
<li><strong>页面组件</strong>：基于<code>j-crud</code>组件</li>
<li><strong>主要功能</strong>：
<ul>
<li>规则列表展示</li>
<li>规则搜索和筛选</li>
<li>规则新增、编辑、删除</li>
<li>规则启用/禁用</li>
<li>规则测试</li>
</ul>
</li>
</ul>
<h3 id="82-规则编辑器">8.2 规则编辑器 </h3>
<ul>
<li><strong>组件名称</strong>：<code>SalaryRuleEditor.vue</code></li>
<li><strong>功能特性</strong>：
<ul>
<li>代码高亮显示</li>
<li>语法错误提示</li>
<li>自动补全功能</li>
<li>规则模板选择</li>
<li>实时预览</li>
</ul>
</li>
</ul>
<h3 id="83-规则测试工具">8.3 规则测试工具 </h3>
<ul>
<li><strong>组件名称</strong>：<code>RuleTestTool.vue</code></li>
<li><strong>测试功能</strong>：
<ul>
<li>测试数据输入</li>
<li>规则执行结果展示</li>
<li>执行日志查看</li>
<li>性能指标统计</li>
</ul>
</li>
</ul>
<h2 id="9-部署方案-">9. 部署方案 🚀 </h2>
<h3 id="91-环境要求">9.1 环境要求 </h3>
<ul>
<li><strong>JDK版本</strong>：JDK 8+</li>
<li><strong>Spring Boot版本</strong>：2.7.x</li>
<li><strong>Drools版本</strong>：7.74.1.Final</li>
<li><strong>数据库</strong>：PostgreSQL 12+</li>
</ul>
<h3 id="92-配置文件">9.2 配置文件 </h3>
<pre data-role="codeBlock" data-info="yaml" class="language-yaml yaml"><code><span class="token comment"># application.yml</span>
<span class="token key atrule">drools</span><span class="token punctuation">:</span>
  <span class="token key atrule">rules</span><span class="token punctuation">:</span>
    <span class="token key atrule">path</span><span class="token punctuation">:</span> classpath<span class="token punctuation">:</span>rules/
    <span class="token key atrule">auto-reload</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
    <span class="token key atrule">reload-interval</span><span class="token punctuation">:</span> <span class="token number">30000</span>
  <span class="token key atrule">cache</span><span class="token punctuation">:</span>
    <span class="token key atrule">enabled</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
    <span class="token key atrule">max-size</span><span class="token punctuation">:</span> <span class="token number">1000</span>
    <span class="token key atrule">expire-after-write</span><span class="token punctuation">:</span> <span class="token number">3600</span>
</code></pre><h3 id="93-部署步骤">9.3 部署步骤 </h3>
<ol>
<li><strong>数据库初始化</strong>：执行DDL脚本创建规则相关表</li>
<li><strong>规则文件部署</strong>：将初始规则文件部署到指定目录</li>
<li><strong>应用启动</strong>：启动Spring Boot应用</li>
<li><strong>规则加载验证</strong>：验证规则引擎正常工作</li>
<li><strong>功能测试</strong>：执行端到端功能测试</li>
</ol>
<h2 id="10-测试方案-">10. 测试方案 🧪 </h2>
<h3 id="101-单元测试">10.1 单元测试 </h3>
<ul>
<li><strong>规则引擎测试</strong>：测试各种规则场景</li>
<li><strong>服务层测试</strong>：测试业务逻辑正确性</li>
<li><strong>数据访问测试</strong>：测试数据库操作</li>
</ul>
<h3 id="102-集成测试">10.2 集成测试 </h3>
<ul>
<li><strong>端到端测试</strong>：完整业务流程测试</li>
<li><strong>性能测试</strong>：规则执行性能测试</li>
<li><strong>并发测试</strong>：多用户并发场景测试</li>
</ul>
<h3 id="103-测试用例">10.3 测试用例 </h3>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token annotation punctuation">@Test</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-void">void</span> <span class="token function">testSalaryType1Rule</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// 测试工资计提规则</span>
    <span class="token class-name">SalaryTaskFact</span> fact <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">SalaryTaskFact</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    fact<span class="token punctuation">.</span><span class="token function">setSalaryType</span><span class="token punctuation">(</span><span class="token string">"1"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token class-name">SalaryVoucherDecision</span> decision <span class="token operator">=</span> ruleEngine<span class="token punctuation">.</span><span class="token function">execute</span><span class="token punctuation">(</span>fact<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token function">assertEquals</span><span class="token punctuation">(</span><span class="token class-name">ProcessType</span><span class="token punctuation">.</span><span class="token constant">DIRECT_VOUCHER</span><span class="token punctuation">,</span> decision<span class="token punctuation">.</span><span class="token function">getProcessType</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token function">assertEquals</span><span class="token punctuation">(</span><span class="token string">"SALARY_ACCRUAL"</span><span class="token punctuation">,</span> decision<span class="token punctuation">.</span><span class="token function">getVoucherTemplate</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="11-风险评估与应对-️">11. 风险评估与应对 ⚠️ </h2>
<h3 id="111-技术风险">11.1 技术风险 </h3>
<ul>
<li><strong>规则复杂度风险</strong>：规则过于复杂可能影响性能
<ul>
<li><strong>应对措施</strong>：规则优化和性能监控</li>
</ul>
</li>
<li><strong>规则冲突风险</strong>：多个规则可能产生冲突
<ul>
<li><strong>应对措施</strong>：规则优先级管理和冲突检测</li>
</ul>
</li>
</ul>
<h3 id="112-业务风险">11.2 业务风险 </h3>
<ul>
<li><strong>规则理解偏差</strong>：业务规则理解不准确
<ul>
<li><strong>应对措施</strong>：充分的业务调研和用户确认</li>
</ul>
</li>
<li><strong>数据迁移风险</strong>：现有数据迁移可能出现问题
<ul>
<li><strong>应对措施</strong>：详细的迁移方案和回滚计划</li>
</ul>
</li>
</ul>
<h2 id="12-项目计划-">12. 项目计划 📅 </h2>
<h3 id="121-开发阶段">12.1 开发阶段 </h3>
<table>
<thead>
<tr>
<th>阶段</th>
<th>任务</th>
<th>工期</th>
<th>负责人</th>
</tr>
</thead>
<tbody>
<tr>
<td>需求分析</td>
<td>业务调研、需求确认</td>
<td>1周</td>
<td>产品经理</td>
</tr>
<tr>
<td>技术设计</td>
<td>架构设计、接口设计</td>
<td>1周</td>
<td>架构师</td>
</tr>
<tr>
<td>后端开发</td>
<td>规则引擎、服务开发</td>
<td>3周</td>
<td>后端开发</td>
</tr>
<tr>
<td>前端开发</td>
<td>界面开发、集成测试</td>
<td>2周</td>
<td>前端开发</td>
</tr>
<tr>
<td>测试验收</td>
<td>功能测试、性能测试</td>
<td>1周</td>
<td>测试工程师</td>
</tr>
</tbody>
</table>
<h3 id="122-里程碑">12.2 里程碑 </h3>
<ul>
<li><strong>M1</strong>：需求确认完成</li>
<li><strong>M2</strong>：技术方案评审通过</li>
<li><strong>M3</strong>：核心功能开发完成</li>
<li><strong>M4</strong>：系统测试通过</li>
<li><strong>M5</strong>：生产环境部署完成</li>
</ul>
<h2 id="13-实施建议-">13. 实施建议 💡 </h2>
<h3 id="131-分阶段实施策略">13.1 分阶段实施策略 </h3>
<p><strong>第一阶段：基础框架搭建</strong></p>
<ul>
<li>搭建Drools规则引擎基础框架</li>
<li>实现核心事实对象和决策对象</li>
<li>完成规则配置数据库设计</li>
</ul>
<p><strong>第二阶段：核心规则迁移</strong></p>
<ul>
<li>迁移工资类型判断规则</li>
<li>实现基础科目映射规则</li>
<li>完成规则热加载功能</li>
</ul>
<p><strong>第三阶段：复杂规则实现</strong></p>
<ul>
<li>实现特殊人员处理规则</li>
<li>完成个人扣款配置检查</li>
<li>实现规则冲突检测</li>
</ul>
<p><strong>第四阶段：管理界面开发</strong></p>
<ul>
<li>开发规则管理界面</li>
<li>实现规则测试工具</li>
<li>完成规则版本管理</li>
</ul>
<h3 id="132-数据迁移策略">13.2 数据迁移策略 </h3>
<ul>
<li>现有硬编码规则提取为配置数据</li>
<li>建立规则配置与代码的映射关系</li>
<li>制定规则验证和回滚机制</li>
</ul>
<h3 id="133-培训计划">13.3 培训计划 </h3>
<ul>
<li>开发人员Drools技术培训</li>
<li>业务人员规则配置培训</li>
<li>系统管理员运维培训</li>
</ul>
<h2 id="14-附录-">14. 附录 📚 </h2>
<h3 id="141-相关技术文档">14.1 相关技术文档 </h3>
<ul>
<li>[Drools官方文档]</li>
<li>[Spring Boot集成Drools指南]</li>
<li>[ECS系统架构文档]</li>
</ul>
<h3 id="142-业务规则清单">14.2 业务规则清单 </h3>
<p>基于系统实际分析，完整的业务规则清单：</p>
<p><strong>工资类型规则</strong>：</p>
<ul>
<li>salaryType=1：工资计提 → 直接生成凭证</li>
<li>salaryType=2：工资发放+三险两金 → 报销审批流程</li>
<li>salaryType=3：企业四险两金 → 直接生成凭证</li>
<li>salaryType=4：工会经费 → 直接生成凭证</li>
</ul>
<p><strong>人员类型规则</strong>：</p>
<ul>
<li>在编人员：<code>["在编","血防占编"]</code></li>
<li>招聘人员：<code>["编外-医技","编外-护理","编外-辅助岗位","编外-医技-见习","编外-护理-见习","编外-辅助岗位-见习"]</code></li>
<li>临聘人员：<code>["编外-其他专技","编外-后勤","编外-其他专技-见习","编外-后勤-见习"]</code></li>
<li>借调人员：<code>["借调"]</code></li>
<li>返聘人员：<code>["返聘"]</code></li>
</ul>
<p><strong>科室类型规则</strong>：</p>
<ul>
<li>业务科室(deptType=1)：临床医技医辅</li>
<li>管理科室(deptType=2)：行政</li>
</ul>
<h3 id="143-系统集成点">14.3 系统集成点 </h3>
<ul>
<li><strong>HRM系统</strong>：获取员工信息和工资数据</li>
<li><strong>ERP系统</strong>：生成财务凭证</li>
<li><strong>BPM系统</strong>：工资报销审批流程</li>
<li><strong>OSS系统</strong>：文件存储和管理</li>
</ul>
<h2 id="15-总结-">15. 总结 📊 </h2>
<p>本PRD文档基于中江县人民医院智慧财务系统(ECS)的实际业务需求和技术架构，详细描述了工资凭证模块的Drools重构方案。通过深入分析现有系统代码和业务规则，制定了完整的重构计划。</p>
<p><strong>重构价值</strong>：</p>
<ol>
<li><strong>业务规则外化</strong>：将硬编码的业务逻辑转换为可配置的规则</li>
<li><strong>提升可维护性</strong>：业务人员可直接维护规则，减少开发依赖</li>
<li><strong>增强扩展性</strong>：支持灵活的规则组合和动态调整</li>
<li><strong>优化性能</strong>：通过规则引擎优化决策执行效率</li>
<li><strong>保证准确性</strong>：统一的规则管理确保业务逻辑一致性</li>
</ol>
<p><strong>技术优势</strong>：</p>
<ul>
<li>基于成熟的Drools规则引擎</li>
<li>与现有Spring Boot架构无缝集成</li>
<li>支持规则热加载和版本管理</li>
<li>提供完整的规则测试和验证机制</li>
</ul>
<p>重构后的系统将为医院财务管理提供更加灵活、高效、可靠的技术支撑，显著提升工资凭证处理的自动化水平和业务响应能力。</p>

      </div>
      
      
    
    
    <script type="module">
// TODO: If ZenUML gets integrated into mermaid in the future,
//      we can remove the following lines.


var MERMAID_CONFIG = ({"startOnLoad":false});
if (typeof MERMAID_CONFIG !== 'undefined') {
  MERMAID_CONFIG.startOnLoad = false
  MERMAID_CONFIG.cloneCssStyles = false
  MERMAID_CONFIG.theme = "default"
}

mermaid.initialize(MERMAID_CONFIG || {})
if (typeof(window['Reveal']) !== 'undefined') {
  function mermaidRevealHelper(event) {
    var currentSlide = event.currentSlide
    var diagrams = currentSlide.querySelectorAll('.mermaid')
    for (var i = 0; i < diagrams.length; i++) {
      var diagram = diagrams[i]
      if (!diagram.hasAttribute('data-processed')) {
        mermaid.init(null, diagram, ()=> {
          Reveal.slide(event.indexh, event.indexv)
        })
      }
    }
  }
  Reveal.addEventListener('slidetransitionend', mermaidRevealHelper)
  Reveal.addEventListener('ready', mermaidRevealHelper)
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
} else {
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
}
</script>
    
    
    
  
    </body></html>