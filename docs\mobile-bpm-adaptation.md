# BPM移动端适配文档 📱

## 概述

为BPM系统的4个主要页面创建了移动端适配版本，使用NaiveUI框架，采用卡片式瀑布流布局设计。

## 🏗️ 架构设计

### 通用模板组件

**文件位置**: `src/components/mobile/BpmListTemplate.vue`

这是一个高度可复用的移动端列表模板组件，提供以下功能：

- ✅ **固定顶部筛选区域** - 不随内容滚动
- ✅ **插槽化筛选条件** - 支持自定义筛选表单
- ✅ **卡片式列表展示** - 固定高度，美观易用
- ✅ **触底自动加载** - 使用Intersection Observer API
- ✅ **搜索和筛选功能** - 快速搜索和高级筛选
- ✅ **加载状态管理** - 完整的loading和empty状态

### 插槽系统

```vue
<!-- 筛选条件插槽 -->
<template #filters="{ queryParams }">
  <!-- 自定义筛选表单 -->
</template>

<!-- 顶部操作按钮插槽 -->
<template #actions>
  <!-- 自定义操作按钮 -->
</template>

<!-- 卡片内容插槽 -->
<template #card="{ item, index }">
  <!-- 自定义卡片内容 -->
</template>
```

## 📱 移动端页面

### 1. 我的流程 (processInstance/index-mob.vue)

**功能特点**:
- 流程状态标签显示
- 业务标识和分类信息
- 当前审批任务展示
- 支持取消和重新发起操作

**筛选条件**:
- 流程分类选择
- 流程状态选择  
- 发起时间范围

### 2. 待办任务 (task/todo/index-mob.vue)

**功能特点**:
- 突出显示待办状态
- 发起人和发起原因信息
- 一键办理按钮
- 任务时间信息

**筛选条件**:
- 流程分类选择
- 业务标识输入
- 发起人选择
- 创建时间范围

### 3. 已办任务 (task/done/index-mob.vue)

**功能特点**:
- 任务状态标签
- 处理耗时显示
- 审批建议展示
- 支持重置和删除操作

**筛选条件**:
- 流程分类选择
- 任务名称输入
- 业务标识输入
- 创建时间范围

### 4. 抄送我的 (task/copy/index-mob.vue)

**功能特点**:
- 抄送标识突出显示
- 抄送人和抄送时间
- 流程发起信息
- 简洁的查看详情操作

**筛选条件**:
- 抄送时间范围

## 🎨 设计规范

### 卡片设计
- **背景**: 白色卡片，圆角边框
- **间距**: 卡片间距12px，内边距16px
- **阴影**: 轻微阴影效果
- **交互**: 点击时背景变灰

### 标签系统
- **业务标识**: 蓝色背景标签
- **当前任务**: 橙色背景标签  
- **已办任务**: 绿色背景标签
- **抄送标识**: 紫色背景标签

### 响应式设计
- **小屏设备**: 减少内边距，调整字体大小
- **触摸优化**: 增大点击区域，优化手势操作

## 🔧 技术实现

### 时间筛选修复
使用Ant Design Vue的`RangePicker`组件，针对移动端优化：
```vue
<RangePicker
  v-model:value="queryParams.createTime"
  :placeholder="['开始日期', '结束日期']"
  format="YYYY-MM-DD HH:mm:ss"
  value-format="YYYY-MM-DD HH:mm:ss"
  class="w-full"
  :allowClear="true"
  show-time
/>
```

**移动端优化特性**：
- `show-time`: 支持时间选择，适合移动端分别选择开始和结束时间
- `format="YYYY-MM-DD HH:mm:ss"`: 显示完整的日期时间格式
- 解决了手机屏幕宽度限制导致的显示问题

**组件导入**：
```javascript
import { DatePicker } from 'ant-design-vue'
const { RangePicker } = DatePicker

defineOptions({
  name: 'ComponentName'
})
```

**中文支持配置**：
在`src/App.vue`中已配置Ant Design Vue的中文支持：
```javascript
import { ConfigProvider } from 'ant-design-vue'
import zhCN_antd from 'ant-design-vue/es/locale/zh_CN'

// 在模板中包装
<a-config-provider :locale="antdLocale">
  <!-- 其他内容 -->
</a-config-provider>

// 在setup中返回
return {
  antdLocale: zhCN_antd, // Ant Design Vue 中文
  // ...其他配置
}
```

### 下拉框数据加载
确保在`onMounted`中正确加载选项数据：
```javascript
onMounted(async () => {
  await getList()
  categoryList.value = await CategoryApi.getCategorySimpleList()
  // 其他数据加载...
})
```

### 触底加载实现
使用Intersection Observer API实现高性能的无限滚动：
```javascript
const setupInfiniteScroll = () => {
  observer = new IntersectionObserver(
    (entries) => {
      const entry = entries[0]
      if (entry.isIntersecting && hasMore.value && !loading.value) {
        loadMore()
      }
    },
    { rootMargin: '100px' }
  )
  observer.observe(loadTrigger.value)
}
```

## 📋 使用指南

### 1. 路由配置
确保移动端路由能正确加载`index-mob.vue`文件：
```javascript
// 路由配置中优先加载移动端组件
const getComp = (path) => {
  if (isMobileDevice.value) {
    try {
      return () => import(`@/views/${path}/index-mob.vue`)
    } catch {
      return () => import(`@/views/${path}/index.vue`)
    }
  }
  return () => import(`@/views/${path}/index.vue`)
}
```

### 2. 移动端检测
使用全局注入的移动端检测：
```javascript
const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')
```

### 3. 页面跳转
移动端统一使用页面跳转而非弹窗：
```javascript
const handleDetail = (row) => {
  router.push({
    path: '/bpm/processInstance/detail/index',
    query: { id: row.id }
  })
}
```

## 🚀 扩展指南

### 创建新的移动端页面
1. 复制现有的`index-mob.vue`文件
2. 修改筛选条件插槽内容
3. 自定义卡片内容插槽
4. 调整API调用和数据处理逻辑

### 自定义卡片样式
在`<style scoped>`中添加自定义样式：
```css
.custom-card {
  @apply bg-white rounded-lg border border-gray-200 p-4 shadow-sm;
}
```

## 🔍 问题解决

### 时间筛选问题
- ✅ 使用Ant Design Vue的`RangePicker`组件
- ✅ 添加`show-time`属性支持时间选择
- ✅ 正确设置`format`和`value-format`为`YYYY-MM-DD HH:mm:ss`
- ✅ 处理数组类型的时间参数
- ✅ 直接导入和使用RangePicker组件
- ✅ 针对移动端屏幕宽度优化

### 下拉框加载问题  
- ✅ 在`onMounted`中加载数据
- ✅ 添加错误处理机制
- ✅ 使用loading状态管理

### 触底加载问题
- ✅ 使用Intersection Observer
- ✅ 正确管理loading状态
- ✅ 避免重复请求

## 📊 性能优化

- **虚拟滚动**: 大数据量时考虑使用虚拟滚动
- **图片懒加载**: 卡片中的图片使用懒加载
- **防抖搜索**: 搜索输入添加防抖处理
- **缓存策略**: 合理使用数据缓存

## 🎯 未来规划

- [ ] 添加下拉刷新功能
- [ ] 支持卡片拖拽排序
- [ ] 增加更多筛选条件
- [ ] 优化动画效果
- [ ] 支持主题切换
