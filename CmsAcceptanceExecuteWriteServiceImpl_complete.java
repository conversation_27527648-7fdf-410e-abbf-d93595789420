package com.jp.med.cms.modules.accept.service.write.impl;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.util.OSSUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceExecuteWriteMapper;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceAttachmentWriteMapper;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceElementWriteMapper;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceApplyWriteMapper;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceExecuteDto;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceAttachmentDto;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceElementDto;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceApplyDto;
import com.jp.med.cms.modules.accept.service.write.CmsAcceptanceExecuteWriteService;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.ULIDUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;
import java.util.ArrayList;

/**
 * 验收执行记录表
 * <AUTHOR>
 * @email -
 * @date 2025-06-13 11:34:09
 */
@Service
@Transactional(readOnly = false)
public class CmsAcceptanceExecuteWriteServiceImpl extends ServiceImpl<CmsAcceptanceExecuteWriteMapper, CmsAcceptanceExecuteDto> implements CmsAcceptanceExecuteWriteService {

    @Autowired
    private CmsAcceptanceAttachmentWriteMapper attachmentWriteMapper;
    
    @Autowired
    private CmsAcceptanceElementWriteMapper elementWriteMapper;
    
    @Autowired
    private CmsAcceptanceApplyWriteMapper applyWriteMapper;

    @Override
    public void executeAcceptance(CmsAcceptanceExecuteDto dto) {
        // 获取创建人信息
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String creator = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        String currentTime = DateUtil.getCurrentTime(null);
        String executeNo = "EXE" + ULIDUtil.generate();
        
        // 验证必要字段
        if (dto.getApplyId() == null) {
            throw new RuntimeException("验收申请ID不能为空");
        }
        
        // 设置基本信息
        dto.setExecuteNo(executeNo);
        dto.setExecuteDate(currentTime);
        dto.setCreator(creator);
        dto.setCreateTime(currentTime);
        dto.setIsDeleted(MedConst.NOT_DELETED);
        
        // 保存验收执行记录
        this.save(dto);
        
        // 处理验收附件上传 - 使用原有的OSS上传方式
        if (CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            List<List<String>> ossPaths = getOSSPaths(dto.getAttFiles(), "acceptFile/");
            saveAttachmentRecords(ossPaths, dto.getApplyId(), dto.getHospitalId(), creator, currentTime);
        }
        
        // 处理验收要素结果更新
        if (dto.getElementsResultData() != null && !dto.getElementsResultData().isEmpty()) {
            updateAcceptanceElementsResult(dto.getElementsResultData(), dto.getApplyId(), creator);
        }
        
        // 更新验收申请状态
        updateApplyStatus(dto.getApplyId(), dto.getResult(), currentTime, creator);
    }

    /**
     * 批量上传文件并获取文件地址list
     * @param files
     * @param basePath
     * @return
     */
    public List<List<String>> getOSSPaths(List<MultipartFile> files, String basePath) {
        List<String> paths = new ArrayList<>();
        List<String> pathNames = new ArrayList<>();
        for (MultipartFile file : files) {
            String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_CMS, basePath, file);
            paths.add(filePath);
            pathNames.add(file.getOriginalFilename());
        }
        List<List<String>> res = new ArrayList<>();
        res.add(paths);
        res.add(pathNames);
        return res;
    }

    /**
     * 保存验收执行附件记录到数据库
     */
    private void saveAttachmentRecords(List<List<String>> ossPaths, Integer applyId, String hospitalId, String creator, String currentTime) {
        List<String> filePaths = ossPaths.get(0);
        List<String> fileNames = ossPaths.get(1);
        
        for (int i = 0; i < filePaths.size(); i++) {
            CmsAcceptanceAttachmentDto attachment = new CmsAcceptanceAttachmentDto();
            attachment.setApplyId(applyId);
            attachment.setFileType("ACCEPTANCE"); // 验收附件
            attachment.setFileName(fileNames.get(i));
            attachment.setFilePath(filePaths.get(i));
            attachment.setFileExt(getFileExtension(fileNames.get(i)));
            attachment.setBucket(OSSConst.BUCKET_CMS);
            attachment.setUploadPerson(creator);
            attachment.setUploadTime(currentTime);
            attachment.setHospitalId(hospitalId);
            attachment.setCreateTime(currentTime);
            attachment.setCreator(creator);
            attachment.setIsDeleted(MedConst.NOT_DELETED);
            
            attachmentWriteMapper.insert(attachment);
        }
    }

    /**
     * 更新验收要素结果
     */
    private void updateAcceptanceElementsResult(String elementsResultJson, Integer applyId, String updater) {
        String currentTime = DateUtil.getCurrentTime(null);
        
        try {
            JSONArray elementsArray = JSONArray.parseArray(elementsResultJson);
            for (int i = 0; i < elementsArray.size(); i++) {
                JSONObject elementObj = elementsArray.getJSONObject(i);
                
                Integer elementId = elementObj.getInteger("id");
                if (elementId != null) {
                    CmsAcceptanceElementDto element = new CmsAcceptanceElementDto();
                    element.setId(elementId);
                    element.setElementStatus(elementObj.getString("elementStatus"));
                    element.setAcceptanceResult(elementObj.getString("acceptanceResult"));
                    element.setAcceptancePerson(updater);
                    element.setAcceptanceDate(currentTime);
                    element.setUpdateTime(currentTime);
                    element.setUpdater(updater);
                    
                    elementWriteMapper.updateById(element);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("更新验收要素结果失败", e);
        }
    }

    /**
     * 更新验收申请状态
     */
    private void updateApplyStatus(Integer applyId, String result, String currentTime, String updater) {
        CmsAcceptanceApplyDto apply = new CmsAcceptanceApplyDto();
        apply.setId(applyId);
        apply.setAcceptanceResult(result);
        apply.setActualAcceptanceDate(currentTime);
        apply.setStatus("COMPLETED");
        apply.setUpdateTime(currentTime);
        apply.setUpdater(updater);
        
        applyWriteMapper.updateById(apply);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
}
