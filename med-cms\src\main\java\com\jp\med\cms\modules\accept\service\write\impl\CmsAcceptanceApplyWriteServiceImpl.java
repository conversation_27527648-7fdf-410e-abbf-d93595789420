package com.jp.med.cms.modules.accept.service.write.impl;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.util.OSSUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceApplyWriteMapper;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceAttachmentWriteMapper;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceElementWriteMapper;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceApplyDto;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceAttachmentDto;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceElementDto;
import com.jp.med.cms.modules.accept.service.write.CmsAcceptanceApplyWriteService;
import com.jp.med.cms.modules.accept.util.AcceptanceFileUtil;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.ULIDUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同验收申请主表
 * <AUTHOR>
 * @email -
 * @date 2025-06-13 11:34:09
 */
@Service
@Transactional(readOnly = false)
public class CmsAcceptanceApplyWriteServiceImpl extends ServiceImpl<CmsAcceptanceApplyWriteMapper, CmsAcceptanceApplyDto> implements CmsAcceptanceApplyWriteService {

    @Autowired
    private CmsAcceptanceAttachmentWriteMapper attachmentWriteMapper;

    @Autowired
    private CmsAcceptanceElementWriteMapper elementWriteMapper;

    @Override
    public void submitApply(CmsAcceptanceApplyDto dto) {
        // 获取创建人信息
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String creator = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        String currentTime = DateUtil.getCurrentTime(null);
        String applyNo = "ACC" + ULIDUtil.generate();

        // 设置基本信息
        dto.setApplyNo(applyNo);
        dto.setCreator(creator);
        dto.setCreateTime(currentTime);
        dto.setIsDeleted(MedConst.NOT_DELETED);
        dto.setStatus("SUBMITTED");

        // 设置默认的验收类型（如果前端没有传递）
        if (dto.getAcceptanceType() == null || dto.getAcceptanceType().isEmpty()) {
            dto.setAcceptanceType("FINAL"); // 默认为最终验收
        }

        // 设置默认的申请机构（如果前端没有传递）
        if (dto.getApplicantOrg() == null || dto.getApplicantOrg().isEmpty()) {
            dto.setApplicantOrg("DEPT001"); // 默认申请科室ID
        }

        // 设置默认的申请机构名称
        if (dto.getApplicantOrgName() == null || dto.getApplicantOrgName().isEmpty()) {
            dto.setApplicantOrgName("合同管理科"); // 默认申请科室名称
        }

        // 设置默认的申请人（如果前端没有传递）
        if (dto.getApplicant() == null || dto.getApplicant().isEmpty()) {
            dto.setApplicant(creator); // 默认为创建人
        }

        // 设置默认的申请人姓名
        if (dto.getApplicantName() == null || dto.getApplicantName().isEmpty()) {
            dto.setApplicantName("系统用户"); // 默认申请人姓名
        }

        // 确保申请日期不为空
        if (dto.getApplyDate() == null || dto.getApplyDate().isEmpty()) {
            dto.setApplyDate(currentTime.split(" ")[0]); // 使用当前日期
        }

        // 保存验收申请主记录
        this.save(dto);
        Integer applyId = dto.getId();

        // 处理附件上传 - 使用原有的OSS上传方式
        if (CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            List<List<String>> ossPaths = getOSSPaths(dto.getAttFiles(), "acceptFile/");
            saveAttachments(ossPaths, applyId, dto.getHospitalId(), creator, currentTime, "APPLY");
        }

        // 处理验收要素数据
        if (dto.getElementsData() != null && !dto.getElementsData().isEmpty()) {
            saveAcceptanceElements(dto.getElementsData(), applyId, dto.getHospitalId(), creator);
        }
    }

    /**
     * 保存验收附件
     */
    private void saveAttachments(List<MultipartFile> files, Integer applyId, String hospitalId, String creator) {
        // 使用工具类上传文件并获取附件记录
        List<CmsAcceptanceAttachmentDto> attachments = AcceptanceFileUtil.uploadApplyFiles(
            files, applyId, hospitalId, creator);

        // 批量保存附件记录
        for (CmsAcceptanceAttachmentDto attachment : attachments) {
            attachmentWriteMapper.insert(attachment);
        }
    }

    /**
     * 批量上传文件并获取文件地址list
     *
     * @param files
     * @param basePath
     * @return
     */
    public List<List<String>> getOSSPaths(List<MultipartFile> files, String basePath) {
        List<String> paths = new ArrayList<>();
        List<String> pathNames = new ArrayList<>();
        for (MultipartFile file : files) {
            String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_CMS, basePath, file);
            paths.add(filePath);
            pathNames.add(file.getOriginalFilename());
        }
        List<List<String>> res = new ArrayList<>();
        res.add(paths);
        res.add(pathNames);
        return res;
    }
    /**
     * 保存验收要素
     */
    private void saveAcceptanceElements(String elementsDataJson, Integer applyId, String hospitalId, String creator) {
        String currentTime = DateUtil.getCurrentTime(null);

        try {
            JSONArray elementsArray = JSONArray.parseArray(elementsDataJson);
            for (int i = 0; i < elementsArray.size(); i++) {
                JSONObject elementObj = elementsArray.getJSONObject(i);

                CmsAcceptanceElementDto element = new CmsAcceptanceElementDto();
                element.setApplyId(applyId);
                element.setSeq(i + 1);
                element.setKeyElement(elementObj.getString("keyElement"));
                element.setNodeRequire(elementObj.getString("nodeRequire"));
                element.setTimeNode(elementObj.getString("timeNode"));
                element.setElementStatus("PENDING");
                element.setHospitalId(hospitalId);
                element.setCreateTime(currentTime);
                element.setCreator(creator);
                element.setIsDeleted(MedConst.NOT_DELETED);

                elementWriteMapper.insert(element);
            }
        } catch (Exception e) {
            throw new RuntimeException("解析验收要素数据失败", e);
        }
    }


}
