# 🔧 CRUD类型定义使用说明

## 📋 概述

本文档详细说明了 `src/types/comps/crud.ts` 中定义的CRUD相关类型接口的使用方法，包括 `CRUDColumnInterface`、`TreeNodeInterface` 和 `JTab` 等核心接口。

## 🏗️ 核心接口

### 1. CRUDColumnInterface - CRUD列配置接口

这是CRUD组件最重要的接口，用于定义表格列和表单项的配置。

#### 基础属性

```typescript
interface CRUDColumnInterface<T = any> extends JFeedback {
  // 基础显示属性
  title: string | Function | VNode    // 列标题
  key: string                         // 列键名（必需）
  realKey?: string                    // 实际键名
  realTitle?: string                  // 实际标题
  className?: TitleClassName          // 列类名

  // 显示控制
  show?: boolean                      // 是否在表单中显示
  showInForm?: boolean               // 是否在表单中显示
  tableColumnShow?: boolean          // 是否在表格中显示
  tableColumnHidden?: boolean        // 是否在默认表格中隐藏
  dynamicShow?: boolean              // 是否动态显示表单项
  hide?: boolean                     // 是否隐藏
  defaultHidden?: boolean            // 默认隐藏
}
```

#### 表单控件属性

```typescript
interface CRUDColumnInterface {
  // 控件类型和配置
  type?: ContainerValueType          // 控件类型
  inputType?: 'text' | 'password' | 'textarea'  // 输入类型
  placeholder?: string               // 占位符文本
  defaultValue?: any                 // 默认值

  // 验证相关
  required?: boolean                 // 是否必填
  showRequireMark?: boolean         // 是否显示必填标记
  disabled?: boolean | Function     // 是否禁用

  // 选择器相关
  multiple?: boolean                 // 是否多选
  selection?: Array<any> | Ref      // 选项数组
  dictType?: string                 // 字典类型
  selectType?: string               // 选择类型

  // 功能配置
  clearable?: boolean               // 是否可清除
  filterable?: boolean              // 是否可过滤
  checkable?: boolean               // 是否可选
  cascade?: boolean                 // 是否级联
}
```

#### 表格显示属性

```typescript
interface CRUDColumnInterface {
  // 表格列配置
  width?: number                    // 列宽
  align?: string                    // 对齐方式
  fixed?: 'left' | 'right'         // 固定列
  ellipsis?: boolean | EllipsisProps // 省略配置
  resizable?: boolean              // 是否可调整大小

  // 排序相关
  sorter?: Function | string | Boolean  // 排序函数
  defaultSortOrder?: string        // 默认排序顺序

  // 渲染相关
  render?: (row: T, index?: number) => VNode | string  // 渲染函数
  renderCell?: (value: any, rowData: object, column: DataTableBaseColumn) => VNodeChild
  realRender?: Function | Boolean  // 实际渲染函数
  formItemRender?: Function        // 表单项渲染函数
  tagRender?: boolean              // 是否渲染标签
  tagRenderHandler?: Function      // 标签渲染处理函数
}
```

#### 高级功能属性

```typescript
interface CRUDColumnInterface {
  // 回调函数
  callBack?: Function              // 回调函数
  selectChange?: Function          // 选择变化回调

  // 树形结构
  treeProp?: {                     // 树形属性配置
    key: string
    label: string
  }

  // 键值对配置
  keyProp?: {                      // 键值对属性配置
    value: string
    label: string
  }

  // 数据类型
  dataType?: DataType              // 数据类型
  checkStrategy?: string | any     // 选择策略

  // 子列配置
  children?: Array<CRUDColumnInterface>  // 子列配置

  // 汇总相关
  summaryType?: 'sum' | 'avg' | 'max' | 'min'  // 汇总计算方式
  summaryLib?: 'mathjs' | 'javascript'         // 汇总使用的库
  summaryFractionDigits?: number               // 汇总小数位数
  rowDotSummaryFn?: Function                   // 行点汇总函数
}
```

#### 日期时间相关属性

```typescript
interface CRUDColumnInterface {
  // 日期时间配置
  defaultTime?: string | Array<string | undefined>  // 默认时间
  dateDisabled?: Function          // 日期禁用函数
  timeDisabled?: Function          // 时间禁用函数
  dateFooter?: Function            // 日期页脚渲染函数
}
```

#### 输入框增强属性

```typescript
interface CRUDColumnInterface {
  // 输入框增强
  prefix?: string                  // 前缀
  suffix?: string | Component      // 后缀
  inputSuffixDelBtn?: boolean      // 是否显示输入框后缀删除按钮
  inputSuffixClick?: Function      // 输入框后缀点击回调
}
```

#### 布局和其他属性

```typescript
interface CRUDColumnInterface {
  // 布局相关
  span?: number                    // 表单栅格占位（默认48）

  // 元数据
  meta?: any                       // 元数据

  // 导出相关
  extraExportColumnIndex?: number  // 额外导出列索引

  // 扩展属性
  [key: string]: any              // 允许任意扩展属性
}
```

## 🌳 TreeNodeInterface - 树状结构接口

用于配置树形数据的节点关系。

```typescript
interface TreeNodeInterface {
  id: string        // 子节点ID对应字段
  parentId: string  // 父级节点ID对应字段
}
```

### 使用示例

```typescript
const treeConfig: TreeNodeInterface = {
  id: 'id',
  parentId: 'parentId'
}

// 在CRUD组件中使用
<j-crud
  :treeNode="treeConfig"
  // ... 其他配置
/>
```

## 📑 JTab - 标签页接口

用于配置CRUD组件的标签页功能。

```typescript
interface JTab {
  // 基础属性
  name: string                     // 标签名称（必需）
  tab: string | VNode | (() => VNodeChild)  // 标签内容
  disabled?: boolean               // 是否禁用

  // 数据相关
  columns?: CRUDColumnInterface[]  // 列配置
  data?: any                       // 数据

  // 汇总相关
  summary?: DataTableCreateSummary // 汇总函数
  summaryFractionDigits?: number   // 汇总小数位数

  // 徽章相关
  useBadge?: boolean              // 是否使用徽章
  badge?: number                  // 徽章数值

  // 选项相关
  options?: Option[]              // 选项列表
  optionSelectVal?: string | number  // 选项选中值

  // 回调函数
  tabChange?: Function            // 标签切换回调
  tabContentRender?: Function     // 标签内容渲染函数

  // 嵌套标签
  tabs?: JTab[]                   // 子标签列表

  // 扩展属性
  [key: string]: any             // 允许任意扩展属性
}
```

### 使用示例

```typescript
const tabs: JTab[] = [
  {
    name: '1',
    tab: '全部数据',
    useBadge: true,
    badge: 0,
    columns: [
      // 列配置...
    ]
  },
  {
    name: '2',
    tab: '待审核',
    useBadge: true,
    badge: 5,
    tabChange: (tabName: string) => {
      console.log('切换到标签:', tabName)
    }
  }
]

// 在CRUD组件中使用
<j-crud
  :tabs="tabs"
  defaultCheckTab="1"
  // ... 其他配置
/>
```

## 🎯 ContainerValueType - 控件类型枚举

定义了CRUD组件支持的所有控件类型。

```typescript
enum ContainerValueType {
  INPUT = 'input',              // 输入框
  DATE_RANGE = 'date_range',    // 日期范围
  DATE_YEAR = 'year',           // 年份选择
  DATE_MONTH = 'date_month',    // 月份选择
  DATE = 'date',                // 日期选择
  TIME = 'time',                // 时间选择
  TIMESTAMP = 'timestamp',      // 时间戳
  DATE_TIME_RANGE = 'date_time_range',  // 日期时间范围
  SELECT = 'select',            // 下拉选择
  TREE_SELECT = 'tree_select',  // 树形选择
  CASCADER = 'cascader',        // 级联选择
  HOSPITAL = 'hospital',        // 医院选择
  SELECTION = 'selection',      // 选择器
  NUMBER = 'number',            // 数字输入
  ORG = 'org',                  // 组织选择
  EMP = 'emp',                  // 员工选择
  ADDR = 'addr',                // 地址选择
}
```

## 📊 DataType - 数据类型枚举

定义了数据验证和处理时使用的数据类型。

```typescript
enum DataType {
  NUMBER = 'number',    // 数字类型
  ARRAY = 'array',      // 数组类型
  STRING = 'string',    // 字符串类型
  OBJECT = 'object',    // 对象类型
  BOOLEAN = 'boolean',  // 布尔类型
  DATE = 'date',        // 日期类型
  YEAR = 'year',        // 年份类型
}
```

## 💡 实际使用示例

### 1. 基础表格列配置

```typescript
import { ContainerValueType, DataType } from '@/types/enums/enums'
import type { CRUDColumnInterface } from '@/types/comps/crud'

const columns: CRUDColumnInterface[] = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center',
    tableColumnShow: true,
    show: false  // 不在表单中显示
  },
  {
    title: '用户名',
    key: 'username',
    width: 150,
    show: true,
    required: true,
    type: ContainerValueType.INPUT,
    placeholder: '请输入用户名',
    dataType: DataType.STRING
  },
  {
    title: '邮箱',
    key: 'email',
    width: 200,
    show: true,
    required: true,
    type: ContainerValueType.INPUT,
    placeholder: '请输入邮箱地址',
    dataType: DataType.STRING
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    show: true,
    type: ContainerValueType.SELECT,
    selection: [
      { label: '启用', value: '1' },
      { label: '禁用', value: '0' }
    ],
    tagRender: true,
    defaultValue: '1'
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180,
    align: 'center',
    show: true,
    type: ContainerValueType.DATE,
    dataType: DataType.DATE
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center',
    fixed: 'right',
    show: false  // 操作列不在表单中显示
  }
]
```

### 2. 高级表单控件配置

```typescript
const advancedColumns: CRUDColumnInterface[] = [
  // 日期范围选择
  {
    title: '时间范围',
    key: 'timeRange',
    show: true,
    type: ContainerValueType.DATE_RANGE,
    placeholder: '请选择时间范围',
    tableColumnShow: false,
    span: 24  // 占满一行
  },

  // 树形选择
  {
    title: '部门',
    key: 'deptId',
    show: true,
    required: true,
    type: ContainerValueType.TREE_SELECT,
    placeholder: '请选择部门',
    treeProp: {
      key: 'id',
      label: 'name'
    },
    checkable: true,
    cascade: true
  },

  // 级联选择
  {
    title: '地区',
    key: 'region',
    show: true,
    type: ContainerValueType.CASCADER,
    placeholder: '请选择地区',
    selection: [
      {
        label: '北京市',
        value: 'beijing',
        children: [
          { label: '朝阳区', value: 'chaoyang' },
          { label: '海淀区', value: 'haidian' }
        ]
      }
    ]
  },

  // 数字输入
  {
    title: '年龄',
    key: 'age',
    show: true,
    type: ContainerValueType.NUMBER,
    placeholder: '请输入年龄',
    dataType: DataType.NUMBER,
    callBack: (value: number) => {
      // 年龄变化时的回调
      if (value < 18) {
        return { disabled: true, message: '年龄不能小于18岁' }
      }
    }
  },

  // 自定义渲染
  {
    title: '头像',
    key: 'avatar',
    width: 80,
    align: 'center',
    show: false,
    render: (row: any) => {
      return h('img', {
        src: row.avatar,
        style: { width: '40px', height: '40px', borderRadius: '50%' }
      })
    }
  }
]
```

### 3. 带汇总功能的列配置

```typescript
const summaryColumns: CRUDColumnInterface[] = [
  {
    title: '商品名称',
    key: 'productName',
    width: 200,
    show: true,
    required: true,
    type: ContainerValueType.INPUT
  },
  {
    title: '单价',
    key: 'price',
    width: 120,
    align: 'right',
    show: true,
    type: ContainerValueType.NUMBER,
    dataType: DataType.NUMBER,
    summaryType: 'avg',  // 平均值汇总
    summaryFractionDigits: 2  // 保留2位小数
  },
  {
    title: '数量',
    key: 'quantity',
    width: 100,
    align: 'right',
    show: true,
    type: ContainerValueType.NUMBER,
    dataType: DataType.NUMBER,
    summaryType: 'sum'  // 求和汇总
  },
  {
    title: '总金额',
    key: 'totalAmount',
    width: 120,
    align: 'right',
    show: false,  // 计算字段，不在表单中显示
    summaryType: 'sum',
    summaryFractionDigits: 2,
    render: (row: any) => {
      return (row.price * row.quantity).toFixed(2)
    }
  }
]
```

### 4. Tab页签配置示例

```typescript
const tabsConfig: JTab[] = [
  {
    name: 'all',
    tab: '全部订单',
    useBadge: true,
    badge: 0,
    columns: [
      {
        title: '订单号',
        key: 'orderNo',
        width: 150,
        show: true,
        required: true,
        type: ContainerValueType.INPUT
      },
      {
        title: '订单状态',
        key: 'status',
        width: 100,
        show: true,
        type: ContainerValueType.SELECT,
        selection: [
          { label: '全部', value: '' },
          { label: '待付款', value: '1' },
          { label: '已付款', value: '2' },
          { label: '已发货', value: '3' },
          { label: '已完成', value: '4' }
        ]
      }
    ]
  },
  {
    name: 'pending',
    tab: '待处理',
    useBadge: true,
    badge: 5,
    tabChange: (tabName: string) => {
      console.log('切换到待处理标签页')
      // 可以在这里执行特定的查询逻辑
    }
  },
  {
    name: 'completed',
    tab: '已完成',
    useBadge: true,
    badge: 20,
    // 自定义标签内容渲染
    tabContentRender: () => {
      return h('div', [
        h('span', '已完成'),
        h('n-tag', { type: 'success', size: 'small' }, '20')
      ])
    }
  }
]
```

## 🎯 最佳实践

### 1. 列配置命名规范

```typescript
// ✅ 推荐的命名方式
const columns: CRUDColumnInterface[] = [
  {
    title: '用户ID',
    key: 'userId',        // 使用驼峰命名
    realKey: 'user_id'    // 如果后端字段是下划线命名
  },
  {
    title: '创建时间',
    key: 'createTime',
    realKey: 'created_at'
  }
]
```

### 2. 表单验证配置

```typescript
const validationColumns: CRUDColumnInterface[] = [
  {
    title: '手机号',
    key: 'phone',
    show: true,
    required: true,
    type: ContainerValueType.INPUT,
    placeholder: '请输入手机号',
    validator: (value: string) => {
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(value)) {
        return Promise.reject('请输入正确的手机号格式')
      }
      return Promise.resolve()
    }
  },
  {
    title: '邮箱',
    key: 'email',
    show: true,
    required: true,
    type: ContainerValueType.INPUT,
    feedback: (value: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (value && !emailRegex.test(value)) {
        return {
          validationStatus: 'error',
          feedbackText: '邮箱格式不正确'
        }
      }
      return { validationStatus: 'success' }
    }
  }
]
```

### 3. 动态表单项控制

```typescript
const dynamicColumns: CRUDColumnInterface[] = [
  {
    title: '用户类型',
    key: 'userType',
    show: true,
    required: true,
    type: ContainerValueType.SELECT,
    selection: [
      { label: '个人用户', value: '1' },
      { label: '企业用户', value: '2' }
    ],
    selectChange: (value: string, formData: any) => {
      // 根据用户类型动态显示不同的表单项
      return {
        companyName: {
          dynamicShow: value === '2'  // 只有企业用户才显示公司名称
        },
        idCard: {
          dynamicShow: value === '1'  // 只有个人用户才显示身份证
        }
      }
    }
  },
  {
    title: '公司名称',
    key: 'companyName',
    show: true,
    dynamicShow: false,  // 默认隐藏
    type: ContainerValueType.INPUT,
    placeholder: '请输入公司名称'
  },
  {
    title: '身份证号',
    key: 'idCard',
    show: true,
    dynamicShow: false,  // 默认隐藏
    type: ContainerValueType.INPUT,
    placeholder: '请输入身份证号'
  }
]
```

### 4. 性能优化建议

```typescript
// ✅ 对于大数据量表格，使用虚拟滚动
const performanceColumns: CRUDColumnInterface[] = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    fixed: 'left'  // 固定重要列
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: true,  // 长文本省略显示
    render: (row: any) => {
      // 避免复杂的渲染逻辑，影响性能
      return row.description?.substring(0, 50) + '...'
    }
  }
]

// 在CRUD组件中启用虚拟滚动
<j-crud
  :columns="performanceColumns"
  :virtualScroll="true"
  :defaultPageSize="100"
/>
```

## ⚠️ 注意事项

1. **必需属性**：`title` 和 `key` 是必需的
2. **类型安全**：使用 TypeScript 时，建议为泛型参数指定具体类型
3. **性能考虑**：避免在 `render` 函数中进行复杂计算
4. **命名规范**：保持一致的命名风格
5. **验证逻辑**：复杂验证建议使用 `validator` 而不是 `feedback`

## 🔗 相关文档

- [AI开发规则-主要组件使用指南](../AI开发规则-主要组件使用指南.md)
- [CRUD组件更新日志](../组件更新文档/crud.md)
- [容器组件使用说明](../components/Container/页面高度计算.md)
```
