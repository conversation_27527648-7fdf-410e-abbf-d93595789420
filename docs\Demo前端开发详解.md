# 🎨 实习生Demo前端开发详解

## 🔄 工作流管理界面开发

### 📋 BPM流程模型管理页面

参考现有的BPM模型管理页面，创建请假流程管理界面：

```vue
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="流程标识" prop="key">
        <el-input v-model="queryParams.key" placeholder="请输入流程标识" clearable />
      </el-form-item>
      <el-form-item label="流程名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入流程名称" clearable />
      </el-form-item>
      <el-form-item label="流程分类" prop="category">
        <el-select v-model="queryParams.category" placeholder="请选择流程分类" clearable>
          <el-option
            v-for="category in categoryList"
            :key="category.code"
            :label="category.name"
            :value="category.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary">
          <Search />搜索
        </el-button>
        <el-button @click="resetQuery">
          <Refresh />重置
        </el-button>
        <el-button type="primary" plain @click="openForm('create')">
          <Plus />新建流程
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 流程列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="流程标识" align="center" prop="key" width="200" />
      <el-table-column label="流程名称" align="center" prop="name" width="200">
        <template #default="scope">
          <el-button type="primary" link @click="handleBpmnDetail(scope.row)">
            <span>{{ scope.row.name }}</span>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="流程分类" align="center" prop="categoryName" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
      <el-table-column label="流程版本" align="center" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.processDefinition">
            v{{ scope.row.processDefinition.version }}
          </el-tag>
          <el-tag v-else type="warning">未部署</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="激活状态" align="center" width="85">
        <template #default="scope">
          <el-switch
            v-if="scope.row.processDefinition"
            v-model="scope.row.processDefinition.suspensionState"
            :active-value="1"
            :inactive-value="2"
            @change="handleChangeState(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="240" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            修改流程
          </el-button>
          <el-button link type="primary" @click="handleDesign(scope.row)">
            设计流程
          </el-button>
          <el-button link type="primary" @click="handleDeploy(scope.row)">
            发布流程
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 流程图预览弹窗 -->
  <Dialog title="流程图" v-model="bpmnDetailVisible" width="800">
    <MyProcessViewer
      key="designer"
      v-model="bpmnXML"
      :value="bpmnXML"
      v-bind="bpmnControlForm"
      :prefix="bpmnControlForm.prefix"
    />
  </Dialog>
</template>

<script setup lang="ts">
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { Dialog } from '@/components/common/bpm/Dialog'
import Pagination from '@/components/common/bpm/Pagination/index.vue'
import { MyProcessViewer } from '@/components/common/bpm/bpmnProcessDesigner/package'
import * as ModelApi from '@/api/bpm/model'
import { CategoryApi } from '@/api/bpm/category'
import { useMessage } from '@/components/common/bpm/bpmAdapter/useMessage'
import { useRouter } from 'vue-router'

const message = useMessage()
const { push } = useRouter()

// 响应式数据
const loading = ref(true)
const total = ref(0)
const list = ref([])
const categoryList = ref([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  key: undefined,
  name: undefined,
  category: undefined,
})

// 流程图预览相关
const bpmnDetailVisible = ref(false)
const bpmnXML = ref('')
const bpmnControlForm = ref({
  prefix: 'flowable',
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ModelApi.getModelPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 设计流程 - 跳转到BPMN设计器 */
const handleDesign = (row: any) => {
  push({
    path: '/bpm/model/editor/index',
    query: { modelId: row.id }
  })
}

/** 发布流程 */
const handleDeploy = async (row: any) => {
  try {
    await message.confirm('是否部署该流程？')
    await ModelApi.deployModel(row.id)
    message.success('部署成功')
    getList()
  } catch {}
}

/** 流程图详情预览 */
const handleBpmnDetail = async (row: any) => {
  const data = await ModelApi.getModel(row.id)
  bpmnXML.value = data.bpmnXml || ''
  bpmnDetailVisible.value = true
}

/** 初始化 */
onMounted(async () => {
  await getList()
  categoryList.value = await CategoryApi.getCategorySimpleList()
})
</script>
```

### 🎯 请假申请管理页面

```vue
<template>
  <j-crud
    ref="crudRef"
    :queryMethod="queryLeaveApplications"
    :addMethod="addLeaveApplication"
    :updateMethod="updateLeaveApplication"
    :delMethod="deleteLeaveApplication"
    :columns="columns"
    :queryForm="queryForm"
    name="请假申请"
    showAddButton
  >
    <!-- 扩展查询条件 -->
    <template #extendFormItems>
      <n-form-item label="申请日期">
        <n-date-picker
          v-model:formatted-value="queryForm.dateRange"
          type="daterange"
          clearable
          placeholder="选择日期范围"
        />
      </n-form-item>
      <n-form-item label="请假类型">
        <n-select
          v-model:value="queryForm.leaveType"
          :options="leaveTypeOptions"
          clearable
          placeholder="选择请假类型"
        />
      </n-form-item>
      <n-form-item label="审批状态">
        <n-select
          v-model:value="queryForm.status"
          :options="statusOptions"
          clearable
          placeholder="选择审批状态"
        />
      </n-form-item>
    </template>

    <!-- 扩展操作按钮 -->
    <template #extendButtons>
      <n-button type="primary" @click="showStatistics">
        <template #icon>
          <n-icon><BarChart /></n-icon>
        </template>
        统计报表
      </n-button>
      <n-button type="info" @click="exportData">
        <template #icon>
          <n-icon><Download /></n-icon>
        </template>
        导出数据
      </n-button>
    </template>

    <!-- 自定义行操作 -->
    <template #rowActions="{ row }">
      <n-button
        v-if="row.status === 'DRAFT'"
        size="small"
        type="primary"
        @click="startProcess(row)"
      >
        提交审批
      </n-button>
      <n-button
        v-if="row.status === 'PENDING'"
        size="small"
        type="warning"
        @click="viewProcess(row)"
      >
        查看流程
      </n-button>
      <n-button
        v-if="row.processInstanceId"
        size="small"
        type="info"
        @click="viewApprovalHistory(row)"
      >
        审批历史
      </n-button>
    </template>
  </j-crud>

  <!-- 审批历史弹窗 -->
  <n-modal v-model:show="historyVisible" preset="dialog" title="审批历史">
    <n-timeline>
      <n-timeline-item
        v-for="record in approvalHistory"
        :key="record.id"
        :type="getTimelineType(record.action)"
        :title="record.approverName"
      >
        <template #header>
          <div class="approval-header">
            <span class="approver-name">{{ record.approverName }}</span>
            <span class="approval-time">{{ record.approvalTime }}</span>
          </div>
        </template>
        <div class="approval-content">
          <n-tag :type="getTagType(record.action)">
            {{ getActionText(record.action) }}
          </n-tag>
          <p v-if="record.comment" class="approval-comment">
            {{ record.comment }}
          </p>
        </div>
      </n-timeline-item>
    </n-timeline>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue'
import { BarChart, Download } from '@vicons/ionicons5'
import type { CRUDColumn } from '@/types/comps/crud'
import { ContainerValueType, DataType } from '@/types/enums/enums'
import * as leaveApi from '@/api/leave/application'

const crudRef = ref()
const historyVisible = ref(false)
const approvalHistory = ref([])

// 查询表单
const queryForm = reactive({
  applicantName: '',
  leaveType: '',
  status: '',
  dateRange: [],
  pageNum: 1,
  pageSize: 20
})

// 请假类型选项
const leaveTypeOptions = [
  { label: '事假', value: 'PERSONAL' },
  { label: '病假', value: 'SICK' },
  { label: '年假', value: 'ANNUAL' },
  { label: '调休', value: 'COMPENSATORY' }
]

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'DRAFT' },
  { label: '审批中', value: 'PENDING' },
  { label: '已通过', value: 'APPROVED' },
  { label: '已拒绝', value: 'REJECTED' }
]

// 表格列配置
const columns = ref<CRUDColumn[]>([
  {
    title: 'ID',
    key: 'id',
    width: 80,
    show: false
  },
  {
    title: '申请人',
    key: 'applicantName',
    width: 120,
    show: true,
    required: true,
    type: ContainerValueType.INPUT,
    placeholder: '请输入申请人姓名'
  },
  {
    title: '部门',
    key: 'deptName',
    width: 150,
    show: true,
    type: ContainerValueType.ORG,
    placeholder: '请选择部门'
  },
  {
    title: '请假类型',
    key: 'leaveType',
    width: 100,
    show: true,
    required: true,
    type: ContainerValueType.SELECT,
    selection: leaveTypeOptions,
    tagRender: true
  },
  {
    title: '开始日期',
    key: 'startDate',
    width: 120,
    show: true,
    required: true,
    type: ContainerValueType.DATE,
    dataType: DataType.DATE
  },
  {
    title: '结束日期',
    key: 'endDate',
    width: 120,
    show: true,
    required: true,
    type: ContainerValueType.DATE,
    dataType: DataType.DATE
  },
  {
    title: '请假天数',
    key: 'days',
    width: 100,
    align: 'center',
    show: true,
    required: true,
    type: ContainerValueType.NUMBER,
    dataType: DataType.NUMBER
  },
  {
    title: '请假原因',
    key: 'reason',
    width: 200,
    show: true,
    type: ContainerValueType.TEXTAREA,
    placeholder: '请输入请假原因'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    show: false,
    render: (row: any) => {
      const statusMap = {
        'DRAFT': { label: '草稿', type: 'default' },
        'PENDING': { label: '审批中', type: 'warning' },
        'APPROVED': { label: '已通过', type: 'success' },
        'REJECTED': { label: '已拒绝', type: 'error' }
      }
      const status = statusMap[row.status] || statusMap['DRAFT']
      return h('n-tag', { type: status.type }, status.label)
    }
  }
])

// API方法
const queryLeaveApplications = async (params: any) => {
  return await leaveApi.queryApplicationList(params)
}

const addLeaveApplication = async (data: any) => {
  return await leaveApi.addApplication(data)
}

const updateLeaveApplication = async (data: any) => {
  return await leaveApi.updateApplication(data)
}

const deleteLeaveApplication = async (id: string) => {
  return await leaveApi.deleteApplication(id)
}

// 业务方法
const startProcess = async (row: any) => {
  try {
    await leaveApi.startProcess(row.id)
    window.$message.success('流程启动成功')
    crudRef.value.refresh()
  } catch (error) {
    window.$message.error('流程启动失败')
  }
}

const viewProcess = (row: any) => {
  // 跳转到流程跟踪页面
  window.open(`/bpm/process/track?processInstanceId=${row.processInstanceId}`)
}

const viewApprovalHistory = async (row: any) => {
  try {
    approvalHistory.value = await leaveApi.getApprovalHistory(row.id)
    historyVisible.value = true
  } catch (error) {
    window.$message.error('查询审批历史失败')
  }
}

const showStatistics = () => {
  // 跳转到统计报表页面
  window.open('/leave/statistics')
}

const exportData = async () => {
  try {
    await leaveApi.exportData(queryForm)
    window.$message.success('导出成功')
  } catch (error) {
    window.$message.error('导出失败')
  }
}

// 辅助方法
const getTimelineType = (action: string) => {
  const typeMap = {
    'APPROVE': 'success',
    'REJECT': 'error',
    'SUBMIT': 'info'
  }
  return typeMap[action] || 'default'
}

const getTagType = (action: string) => {
  const typeMap = {
    'APPROVE': 'success',
    'REJECT': 'error',
    'SUBMIT': 'info'
  }
  return typeMap[action] || 'default'
}

const getActionText = (action: string) => {
  const textMap = {
    'APPROVE': '同意',
    'REJECT': '拒绝',
    'SUBMIT': '提交'
  }
  return textMap[action] || action
}
</script>

<style scoped>
.approval-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.approver-name {
  font-weight: bold;
}

.approval-time {
  color: #666;
  font-size: 12px;
}

.approval-content {
  margin-top: 8px;
}

.approval-comment {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
}
</style>
```

### 📊 统计报表页面

```vue
<template>
  <j-container :showHeader="false" @contentHeight="val => (contentHeight = val)">
    <template #content>
      <div class="leave-dashboard" :style="{ height: contentHeight, padding: '16px' }">
        <!-- 统计卡片 -->
        <n-grid :cols="4" :x-gap="16" :y-gap="16" class="stats-section">
          <n-grid-item v-for="stat in statsData" :key="stat.key">
            <n-card class="stat-card" hoverable>
              <div class="stat-content">
                <div class="stat-icon" :style="{ color: stat.iconColor }">
                  <n-icon :size="32">
                    <component :is="stat.icon" />
                  </n-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-title">{{ stat.title }}</div>
                  <div class="stat-value">{{ stat.value }}</div>
                  <div class="stat-trend" :style="{ color: stat.trendColor }">
                    {{ stat.trend }}
                  </div>
                </div>
              </div>
            </n-card>
          </n-grid-item>
        </n-grid>

        <!-- 图表展示区域 -->
        <n-grid :cols="24" :x-gap="16" :y-gap="16" class="charts-section" style="margin-top: 16px;">
          <n-grid-item :span="12">
            <n-card title="月度请假趋势" hoverable>
              <div ref="trendChartRef" style="height: 300px;"></div>
            </n-card>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-card title="请假类型分布" hoverable>
              <div ref="typeChartRef" style="height: 300px;"></div>
            </n-card>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-card title="部门请假统计" hoverable>
              <div ref="deptChartRef" style="height: 300px;"></div>
            </n-card>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-card title="审批效率统计" hoverable>
              <div ref="efficiencyChartRef" style="height: 300px;"></div>
            </n-card>
          </n-grid-item>
        </n-grid>

        <!-- 数据列表 -->
        <n-card title="最近申请记录" style="margin-top: 16px;" hoverable>
          <n-data-table
            :columns="recentColumns"
            :data="recentData"
            :pagination="{ pageSize: 10 }"
            size="small"
          />
        </n-card>
      </div>
    </template>
  </j-container>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { DocumentText, Clock, CheckmarkCircle, CloseCircle, TrendingUp } from '@vicons/ionicons5'

const contentHeight = ref<string>('')
const trendChartRef = ref()
const typeChartRef = ref()
const deptChartRef = ref()
const efficiencyChartRef = ref()

// 统计数据
const statsData = ref([
  {
    key: 'totalApplications',
    title: '本月申请',
    value: '25',
    trend: '↑ 12%',
    icon: DocumentText,
    iconColor: '#409EFF',
    trendColor: '#67C23A'
  },
  {
    key: 'pendingApprovals',
    title: '待审批',
    value: '8',
    trend: '↓ 5%',
    icon: Clock,
    iconColor: '#E6A23C',
    trendColor: '#F56C6C'
  },
  {
    key: 'approvedCount',
    title: '已通过',
    value: '15',
    trend: '↑ 8%',
    icon: CheckmarkCircle,
    iconColor: '#67C23A',
    trendColor: '#67C23A'
  },
  {
    key: 'rejectedCount',
    title: '已拒绝',
    value: '2',
    trend: '→ 0%',
    icon: CloseCircle,
    iconColor: '#F56C6C',
    trendColor: '#909399'
  }
])

// 最近记录表格列
const recentColumns = [
  { title: '申请人', key: 'applicantName' },
  { title: '请假类型', key: 'leaveType' },
  { title: '请假天数', key: 'days' },
  { title: '状态', key: 'status' },
  { title: '申请时间', key: 'createdTime' }
]

const recentData = ref([])

onMounted(async () => {
  await nextTick()
  initCharts()
  loadDashboardData()
})

const initCharts = () => {
  // 月度趋势图
  const trendChart = echarts.init(trendChartRef.value)
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['申请数量', '通过数量']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '申请数量',
        data: [12, 18, 25, 20, 28, 25],
        type: 'line',
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '通过数量',
        data: [10, 16, 22, 18, 25, 23],
        type: 'line',
        smooth: true,
        itemStyle: { color: '#67C23A' }
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 类型分布饼图
  const typeChart = echarts.init(typeChartRef.value)
  const typeOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      bottom: '5%',
      left: 'center'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        data: [
          { value: 10, name: '事假' },
          { value: 8, name: '病假' },
          { value: 5, name: '年假' },
          { value: 2, name: '调休' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  typeChart.setOption(typeOption)

  // 部门统计柱状图
  const deptChart = echarts.init(deptChartRef.value)
  const deptOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['技术部', '市场部', '财务部', '人事部', '运营部']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: [8, 12, 5, 3, 7],
        type: 'bar',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  deptChart.setOption(deptOption)

  // 审批效率图
  const efficiencyChart = echarts.init(efficiencyChartRef.value)
  const efficiencyOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1天内', '2天内', '3天内', '4-7天', '7天以上']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: [15, 8, 2, 1, 0],
        type: 'bar',
        itemStyle: {
          color: (params: any) => {
            const colors = ['#67C23A', '#E6A23C', '#F56C6C', '#909399', '#F56C6C']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  efficiencyChart.setOption(efficiencyOption)

  // 响应式处理
  window.addEventListener('resize', () => {
    trendChart.resize()
    typeChart.resize()
    deptChart.resize()
    efficiencyChart.resize()
  })
}

const loadDashboardData = async () => {
  try {
    // 加载最近记录数据
    // const data = await leaveApi.getRecentApplications()
    // recentData.value = data
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}
</script>

<style scoped>
.leave-dashboard {
  background-color: #f5f5f5;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 12px;
}

.charts-section {
  margin-top: 16px;
}
</style>
```

这样实习生就能快速构建出功能完整、界面美观的前端管理系统！🎨 