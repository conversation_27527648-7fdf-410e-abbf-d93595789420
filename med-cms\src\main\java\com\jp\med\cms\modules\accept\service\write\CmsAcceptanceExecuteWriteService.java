package com.jp.med.cms.modules.accept.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceExecuteDto;

/**
 * 验收执行记录表
 * <AUTHOR>
 * @email -
 * @date 2025-06-13 11:34:09
 */
public interface CmsAcceptanceExecuteWriteService extends IService<CmsAcceptanceExecuteDto> {

    /**
     * 执行验收（带附件上传）
     * @param dto 验收执行数据
     */
    void executeAcceptance(CmsAcceptanceExecuteDto dto);
}

