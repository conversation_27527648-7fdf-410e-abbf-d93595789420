package com.jp.med.cms.modules.accept.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.jp.med.cms.modules.accept.dto.CmsAcceptanceApplyDto;
import com.jp.med.cms.modules.accept.service.read.CmsAcceptanceApplyReadService;
import com.jp.med.cms.modules.accept.service.write.CmsAcceptanceApplyWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 合同验收申请主表
 * <AUTHOR>
 * @email -
 * @date 2025-06-13 11:34:09
 */
@Api(value = "合同验收申请主表", tags = "合同验收申请主表")
@RestController
@RequestMapping("cmsAcceptanceApply")
public class CmsAcceptanceApplyController {

    @Autowired
    private CmsAcceptanceApplyReadService cmsAcceptanceApplyReadService;

    @Autowired
    private CmsAcceptanceApplyWriteService cmsAcceptanceApplyWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询合同验收申请主表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody CmsAcceptanceApplyDto dto){
        return CommonResult.paging(cmsAcceptanceApplyReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询合同验收申请主表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CmsAcceptanceApplyDto dto){
        return CommonResult.success(cmsAcceptanceApplyReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增合同验收申请主表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody CmsAcceptanceApplyDto dto){
        cmsAcceptanceApplyWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 提交验收申请（带附件）
     */
    @ApiOperation("提交合同验收申请")
    @PostMapping("/submit")
    public CommonResult<?> submitApply(CmsAcceptanceApplyDto dto){
        cmsAcceptanceApplyWriteService.submitApply(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改合同验收申请主表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CmsAcceptanceApplyDto dto){
        cmsAcceptanceApplyWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除合同验收申请主表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CmsAcceptanceApplyDto dto){
        cmsAcceptanceApplyWriteService.removeById(dto);
        return CommonResult.success();
    }

}
