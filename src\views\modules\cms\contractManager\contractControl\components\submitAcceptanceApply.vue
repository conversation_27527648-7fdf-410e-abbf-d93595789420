<template>
  <n-modal v-model:show="showModal" :mask-closable="false" preset="dialog" title="提交验收申请">
    <template #header>
      <div class="flex items-center">
        提交验收申请
      </div>
    </template>
    
    <div class="max-h-[70vh] overflow-y-auto">
      <n-form ref="formRef" :model="formData" :rules="rules" label-placement="left" label-width="120px">
        <!-- 基本信息 -->
        <n-form-item label="合同名称" path="contractName">
          <n-input v-model:value="formData.contractName" readonly />
        </n-form-item>
        
        <n-form-item label="合同编号" path="contractNo">
          <n-input v-model:value="formData.contractNo" readonly />
        </n-form-item>

        <n-form-item label="验收类型" path="acceptanceType">
          <n-select v-model:value="formData.acceptanceType" placeholder="请选择验收类型">
            <n-option value="MILESTONE" label="里程碑验收" />
            <n-option value="STAGE" label="阶段验收" />
            <n-option value="FINAL" label="最终验收" />
          </n-select>
        </n-form-item>

        <n-form-item label="申请验收日期" path="applyDate">
          <n-date-picker
            v-model:value="formData.applyDate"
            type="date"
            placeholder="请选择申请验收日期"
          />
        </n-form-item>

        <n-form-item label="计划验收日期" path="acceptanceDate">
          <n-date-picker
            v-model:value="formData.acceptanceDate"
            type="date"
            placeholder="请选择计划验收日期"
          />
        </n-form-item>
        
        <n-form-item label="验收申请说明" path="applyReason">
          <n-input 
            v-model:value="formData.applyReason" 
            type="textarea" 
            :rows="3"
            placeholder="请输入验收申请说明"
          />
        </n-form-item>
        
        <!-- 验收要素 -->
        <n-form-item label="验收要素">
          <div class="w-full">
            <div class="flex justify-between items-center mb-3">
              <span class="text-sm text-gray-600">配置验收要素</span>
              <n-button size="small" @click="addElement">
                添加要素
              </n-button>
            </div>
            
            <div v-if="elements.length === 0" class="text-center text-gray-400 py-4">
              暂无验收要素，请点击"添加要素"按钮添加
            </div>
            
            <div v-for="(element, index) in elements" :key="index" class="border rounded p-3 mb-3">
              <div class="flex justify-between items-start mb-2">
                <span class="text-sm font-medium">要素 {{ index + 1 }}</span>
                <n-button size="tiny" type="error" @click="removeElement(index)">
                  删除
                </n-button>
              </div>
              
              <n-grid :cols="2" :x-gap="12">
                <n-grid-item>
                  <n-form-item label="关键要素">
                    <n-input v-model:value="element.keyElement" placeholder="请输入关键要素" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="节点要求">
                    <n-input v-model:value="element.nodeRequire" placeholder="请输入节点要求" />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              
              <n-form-item label="时间节点">
                <n-input v-model:value="element.timeNode" placeholder="请输入时间节点" />
              </n-form-item>
            </div>
          </div>
        </n-form-item>
        
        <!-- 附件上传 -->
        <n-form-item label="申请附件">
          <n-upload
            ref="uploadRef"
            v-model:file-list="fileList"
            multiple
            :max="10"
            :on-before-upload="beforeUpload"
            :on-remove="handleRemove"
          >
            <n-upload-dragger>
              <div style="margin-bottom: 12px">
                📁
              </div>
              <n-text style="font-size: 16px">
                点击或者拖动文件到该区域来上传
              </n-text>
              <n-p depth="3" style="margin: 8px 0 0 0">
                支持 PDF、Word、Excel、图片等格式，单个文件不超过 10MB
              </n-p>
            </n-upload-dragger>
          </n-upload>
        </n-form-item>
      </n-form>
    </div>
    
    <template #action>
      <div class="flex justify-end space-x-2">
        <n-button @click="closeModal">取消</n-button>
        <n-button type="primary" :loading="submitting" @click="handleSubmit">提交申请</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { submitAcceptanceApply } from '@/api/cms/contractAcceptance/index'

interface Props {
  show: boolean
  contractData?: any
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success', data: any): void
}

const props = defineProps<Props>()
const emits = defineEmits<Emits>()

const showModal = ref(false)
const submitting = ref(false)
const formRef = ref()
const uploadRef = ref()

// 表单数据
const formData = reactive({
  contractId: '',
  contractName: '',
  contractNo: '',
  acceptanceType: 'FINAL', // 默认最终验收
  applyDate: null as any,
  acceptanceDate: null as any,
  applyReason: '',
  hospitalId: ''
})

// 验收要素
const elements = ref<Array<{
  keyElement: string
  nodeRequire: string
  timeNode: string
}>>([])

// 文件列表
const fileList = ref([])

// 表单验证规则
const rules = {
  acceptanceType: {
    required: true,
    message: '请选择验收类型',
    trigger: 'change'
  },
  applyDate: {
    required: true,
    message: '请选择申请验收日期',
    trigger: 'blur'
  },
  acceptanceDate: {
    required: true,
    message: '请选择计划验收日期',
    trigger: 'blur'
  },
  applyReason: {
    required: true,
    message: '请输入验收申请说明',
    trigger: 'blur'
  }
}

// 监听显示状态
watch(() => props.show, (newVal) => {
  showModal.value = newVal
  if (newVal && props.contractData) {
    initFormData()
  }
})

watch(showModal, (newVal) => {
  emits('update:show', newVal)
  if (!newVal) {
    resetForm()
  }
})

// 初始化表单数据
const initFormData = () => {
  const contract = props.contractData
  formData.contractId = contract.id
  formData.contractName = contract.ctName || contract.contractName
  formData.contractNo = contract.ctCode || contract.contractNo
  formData.hospitalId = contract.hospitalId || 'zjxrmyy'
}

// 添加验收要素
const addElement = () => {
  elements.value.push({
    keyElement: '',
    nodeRequire: '',
    timeNode: ''
  })
}

// 删除验收要素
const removeElement = (index: number) => {
  elements.value.splice(index, 1)
}

// 文件上传前检查
const beforeUpload = (data: any) => {
  const file = data.file.file || data.file
  const allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'bmp', 'gif']
  const fileExt = file.name.split('.').pop()?.toLowerCase()

  if (!allowedTypes.includes(fileExt || '')) {
    window.$message.error('不支持的文件格式')
    return false
  }

  if (file.size > 10 * 1024 * 1024) {
    window.$message.error('文件大小不能超过 10MB')
    return false
  }

  return true
}

// 删除文件
const handleRemove = () => {
  return true
}

// 提交申请
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    // 构建FormData
    const formDataToSubmit = new FormData()

    // 添加基本信息
    Object.keys(formData).forEach(key => {
      let value = formData[key]
      // 处理日期格式
      if ((key === 'applyDate' || key === 'acceptanceDate') && value) {
        value = new Date(value).toISOString().split('T')[0]
      }
      formDataToSubmit.append(key, value || '')
    })
    
    // 添加验收要素数据
    if (elements.value.length > 0) {
      formDataToSubmit.append('elementsData', JSON.stringify(elements.value))
    }
    
    // 添加文件
    fileList.value.forEach((fileItem: any) => {
      if (fileItem.file) {
        formDataToSubmit.append('attFiles', fileItem.file)
      }
    })
    
    // 调用API
    const res = await submitAcceptanceApply(formDataToSubmit)
    
    if (res.code === '200') {
      window.$message.success('验收申请提交成功')
      emits('success', res.data)
      closeModal()
    } else {
      window.$message.error('提交失败：' + res.message)
    }
    
  } catch (error) {
    console.error('提交验收申请失败:', error)
    window.$message.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.contractId = ''
  formData.contractName = ''
  formData.contractNo = ''
  formData.acceptanceType = 'FINAL'
  formData.applyDate = null
  formData.acceptanceDate = null
  formData.applyReason = ''
  formData.hospitalId = ''
  elements.value = []
  fileList.value = []
}

// 关闭弹窗
const closeModal = () => {
  showModal.value = false
}
</script>

