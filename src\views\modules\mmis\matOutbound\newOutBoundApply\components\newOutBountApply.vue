<template>
  <n-card style="height: 100%; width: 100%" :content-style="{ height: '90%' }">
    <n-form label-placement="left" label-width="80" ref="formRef">
      <template v-for="(item, idx) in formItems" :key="idx">
        <j-title-line :title="item.title" />
        <n-row :gutter="20">
          <template v-for="fi in item.formItems" :key="fi.key">
            <n-col :span="fi.span ?? 6" v-if="fi.show == undefined ? true : fi.show">
              <n-form-item :label="fi.title" :path="fi.key">
                <j-form-item :action-form="form" :item="fi" />
              </n-form-item>
            </n-col>
          </template>
        </n-row>
      </template>
      <n-row :gutter="24">
        <n-col :span="24">
          <j-title-line title="预处理出库申请详情" class="exp-space">
            <template #default>
              <div style="display: flex; flex-direction: row; align-items: center">
                <!-- 添加当前仓库名称显示 -->
                <n-tag type="success" style="margin-right: 15px; font-size: 14px">
                  当前仓库: {{ getCurrentWrhsName() }}
                </n-tag>

                <div style="display: flex; flex-direction: row">
                  <n-popselect
                    v-if="isAdd ? true : chkState == '0'"
                    :options="
                      form.wrhsManager.map(item => ({
                        value: item.id,
                        label: item.empName + '[' + allWrhsInfo.find(w => w.value === item.wrhsCode)?.label + ']',
                        style: {
                          fontWeight: 'bold',
                          fontSize: '16px',
                        },
                      }))
                    "
                    @update:value="assignOutbound"
                    style="width: 120%"
                  >
                    <n-popover placement="top">
                      <template #trigger>
                        <j-icon name="jiaosefenpei" style="cursor: pointer" :width="25" :height="25" />
                      </template>
                      <span>分配物资出库任务</span>
                    </n-popover>
                  </n-popselect>
                  <div style="width: 40px"></div>
                  <n-popover placement="top" v-if="isAdd ? true : chkState == '0'">
                    <template #trigger>
                      <j-icon
                        name="huifuxitongmoren"
                        style="cursor: pointer"
                        :width="25"
                        :height="25"
                        @click="showSelectTableData2()"
                      />
                    </template>
                    重新分配
                  </n-popover>

                  <div style="width: 40px"></div>
                  <n-popconfirm
                    placement="bottom"
                    v-if="isAdd ? true : chkState == '0'"
                    @positive-click="deleteMmisDetail()"
                  >
                    <template #trigger>
                      <j-icon name="delete" style="cursor: pointer" :width="25" :height="25" />
                    </template>
                    <span>移除全部</span>
                  </n-popconfirm>
                  <div style="width: 40px"></div>
                  <n-popover placement="top" v-if="isAdd ? true : chkState == '0'">
                    <template #trigger>
                      <j-icon
                        name="detail"
                        style="cursor: pointer"
                        :width="25"
                        :height="25"
                        @click="showSelectTableData2()"
                      />
                    </template>
                    导入申请
                  </n-popover>
                  <div style="width: 40px"></div>
                  <n-popover placement="top" v-if="isAdd ? true : chkState == '0'">
                    <template #trigger>
                      <j-icon
                        name="spmx"
                        style="cursor: pointer"
                        :width="25"
                        :height="25"
                        @click="showPurcShippingData()"
                      />
                    </template>
                    采购发货
                  </n-popover>
                  <div style="width: 40px"></div>
                  <n-popover placement="top" v-if="isAdd ? true : chkState == '0'">
                    <template #trigger>
                      <j-icon
                        name="add2"
                        style="cursor: pointer"
                        :width="25"
                        :height="25"
                        @click="showSelectTableData()"
                      />
                    </template>
                    自定义物资出库申请
                  </n-popover>

                  <div style="width: 40px"></div>
                  <n-popover placement="top">
                    <template #trigger>
                      <j-icon name="rukudan" style="cursor: pointer" :width="25" :height="25" @click="exportExcel()" />
                    </template>
                    最近入库单
                  </n-popover>
                  <div style="width: 40px"></div>
                  <n-popover placement="top">
                    <template #trigger>
                      <j-icon name="excel2" style="cursor: pointer" :width="25" :height="25" @click="exportExcel()" />
                    </template>
                    导出出库单
                  </n-popover>
                </div>
              </div>
            </template>
          </j-title-line>
          <!--     tp1 按键换成图标并靠右     -->
          <!--          <j-export :export-config="exportConfig" :show-exportExcel-template="false">导出入库单</j-export>-->
          <j-n-data-table
            :columns="materialsColumns as any"
            :data="form.mmisOutboundApplyDetailsDtos"
            ref="tableDataRef"
            :row-key="row => row.key"
            @update:checked-row-keys="selectOutboundTask"
            :row-props="rowProps"
            :row-class-name="rowClassName"
          />
          <n-dropdown
            placement="bottom-start"
            trigger="manual"
            :x="x"
            :y="y"
            :options="options"
            :show="showDropdown"
            :on-clickoutside="onClickoutside"
            @select="handleSelect"
          />
        </n-col>
      </n-row>
    </n-form>
    <n-divider />

    <div
      class="task-distribution-header"
      style="background-color: #f9fcff; border-radius: 8px; padding: 15px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05)"
    >
      <!-- 上方：分配物资出库任务标题和进度条 -->
      <div style="width: 100%; margin-bottom: 15px">
        <n-h3
          style="text-align: center; margin: 0; padding: 12px 0"
          :style="{
            backgroundColor: form.waitOutboundApplyDetailsDtos.some(arr => arr && arr.length > 0)
              ? '#e8f5e9'
              : '#f5f5f5',
            borderRadius: '8px',
            transition: 'all 0.3s ease',
            boxShadow: form.waitOutboundApplyDetailsDtos.some(arr => arr && arr.length > 0)
              ? '0 2px 8px rgba(0, 120, 0, 0.1)'
              : 'none',
          }"
        >
          <div style="display: flex; align-items: center; justify-content: center">
            <j-icon name="jiaosefenpei" :width="24" :height="24" style="margin-right: 10px" />
            分配物资出库任务

            <!-- 添加分配状态指示器 -->
            <div class="distribution-status" style="margin-left: 20px; display: flex; align-items: center">
              <n-tooltip trigger="hover" placement="top">
                <template #trigger>
                  <n-progress
                    type="line"
                    :percentage="getDistributionPercentage()"
                    :processing="getDistributionPercentage() > 0 && getDistributionPercentage() < 100"
                    :status="getDistributionPercentage() === 100 ? 'success' : 'processing'"
                    :show-indicator="false"
                    style="width: 120px; margin: 0 10px"
                  />
                </template>
                <span>已分配: {{ getDistributedCount() }} / 总数: {{ getTotalItemsCount() }}</span>
              </n-tooltip>

              <span style="font-size: 14px; color: #666">
                {{
                  getDistributionPercentage() === 100
                    ? '全部已分配'
                    : getDistributionPercentage() > 0
                    ? `已分配 ${getDistributionPercentage()}%`
                    : '未分配'
                }}
              </span>
            </div>
          </div>
        </n-h3>
      </div>

      <!-- 下方：分支动画效果 (竖向分支布局) -->
      <div style="width: 100%" v-if="getTotalItemsCount() > 0">
        <div
          class="distribution-flow-animation"
          style="transform: scale(0.95); margin: 0; box-shadow: none; background-color: transparent; min-height: 200px"
        >
          <div class="flow-container">
            <div class="flow-source">
              <div class="flow-title">待分配物资</div>
              <div class="flow-count">{{ form.mmisOutboundApplyDetailsDtos.length }}</div>
              <div class="flow-icon">
                <j-icon name="wuzi" :width="20" :height="20" />
              </div>
            </div>

            <div class="flow-branches">
              <div
                v-for="(item, index) in form.wrhsManager"
                :key="item.empCode"
                class="flow-branch"
                :class="{ 'flow-branch-active': form.waitOutboundApplyDetailsDtos[index]?.length > 0 }"
              >
                <div class="flow-line">
                  <div
                    class="flow-dot"
                    :class="{ 'flow-dot-pulse': form.waitOutboundApplyDetailsDtos[index]?.length > 0 }"
                  ></div>
                  <div class="flow-arrow"></div>
                </div>
                <div class="flow-target">
                  <div class="flow-target-name">{{ item.empName }}</div>
                  <div class="flow-target-count">{{ form.waitOutboundApplyDetailsDtos[index]?.length || 0 }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <n-tabs
      type="segment"
      animated
      trigger="click"
      :default-value="form.wrhsManager[0]?.empName"
      @update:value="handleNTabsChange"
      style="margin-top: 16px"
    >
      <n-tab-pane
        v-for="(item, index) in form.wrhsManager"
        :key="item.empCode"
        :name="item.empName"
        :tab="
          () => {
            const itemCount = form.waitOutboundApplyDetailsDtos[index]?.length || 0
            return h(
              NBadge,
              {
                value: itemCount,
                max: 99,
                offset: [38, 8],
                color: itemCount > 0 ? '#18a058' : '#d9d9d9',
                processing: itemCount > 0,
              },
              {
                default: () =>
                  h(
                    'div',
                    {
                      style: {
                        display: 'flex',
                        alignItems: 'center',
                        padding: '4px 8px',
                        borderRadius: '4px',
                        transition: 'all 0.3s ease',
                        background: itemCount > 0 ? 'rgba(24, 160, 88, 0.1)' : 'transparent',
                        fontWeight: 'bold',
                        fontSize: '16px',
                        letterSpacing: '1px',
                      },
                    },
                    [
                      h(JIcon, {
                        name: 'user',
                        width: 18,
                        height: 18,
                        style: {
                          marginRight: '8px',
                          color: itemCount > 0 ? '#18a058' : '#666',
                        },
                      }),
                      item.empName + ' [' + allWrhsInfo.find(w => w.value === item.wrhsCode)?.label + ']',
                    ]
                  ),
              }
            )
          }
        "
      >
        <!-- 添加库管信息卡片样式 -->
        <div
          class="manager-card"
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            margin: 0 auto 16px;
            padding: 16px;
            background-color: #f9fcff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
          "
          :style="{
            borderLeft:
              form.waitOutboundApplyDetailsDtos[index]?.length > 0 ? '4px solid #18a058' : '4px solid #d9d9d9',
          }"
        >
          <div style="display: flex; flex-direction: column; width: 40%; border-radius: 8px">
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px">
              <div style="font-size: 18px; display: flex; align-items: center">
                <j-icon name="user" :width="20" :height="20" style="margin-right: 8px" />
                <span style="font-weight: bold">库管: </span>
                <span style="margin-left: 8px; color: #18a058">{{ item.empName }}</span>
              </div>
              <div style="font-size: 18px; display: flex; align-items: center">
                <j-icon name="phone" :width="20" :height="20" style="margin-right: 8px" />
                <span style="font-weight: bold">联系电话: </span>
                <span style="margin-left: 8px; color: #2080f0">{{ item.hrmPhone }}</span>
              </div>
            </div>
            <div style="font-size: 18px; display: flex; align-items: center">
              <j-icon name="org" :width="20" :height="20" style="margin-right: 8px" />
              <span style="font-weight: bold">科室: </span>
              <span style="margin-left: 8px; color: #2080f0">{{ item.orgName }}</span>
            </div>
          </div>

          <!-- 添加任务统计信息 -->
          <div
            style="display: flex; align-items: center; background-color: #f0f7ff; padding: 8px 16px; border-radius: 8px"
          >
            <n-statistic
              label="待处理物资"
              :value="form.waitOutboundApplyDetailsDtos[index]?.length || 0"
              :style="{
                color: form.waitOutboundApplyDetailsDtos[index]?.length > 0 ? '#18a058' : '#999',
              }"
            >
              <template #suffix>
                <span style="font-size: 14px; margin-left: 4px">项</span>
              </template>
            </n-statistic>
          </div>
        </div>

        <!-- 数据表格 -->
        <j-n-data-table
          :columns="assignedMaterialsColumns as any"
          :data="form.waitOutboundApplyDetailsDtos[index]"
          :ref="'tableDataRef' + item.empCode"
          :empty-text="form.waitOutboundApplyDetailsDtos[index]?.length > 0 ? '加载中...' : '暂无分配给该库管的物资'"
          :row-class-name="row => 'animated-row'"
        ></j-n-data-table>
      </n-tab-pane>
    </n-tabs>

    <!--  选择申请通过的申请记录，还需要加上详情   -->
    <j-modal v-model:show="showAppliedMat" title="导入申请单" width="95%" height="80%" @confirm="addSelectApplyMat()">
      <j-crud
        :queryMethod="queryWaitOutApplyList"
        :columns="matSelectColumns1"
        :queryForm="queryApMatForm"
        rowKey="key"
        :checkedRowKeys="checkRows1"
        @update:checkedRowKeys="handOrgApplyMatRowKeys"
        ref="matApplySelectCrudRef"
        :show-operation-button="false"
      >
        <template #extendFormItems>
          <n-form-item label="申请科室">
            <j-bus-hos-org v-model:value="queryApMatForm.appyOrgId" clearable></j-bus-hos-org>
          </n-form-item>
        </template>
      </j-crud>

      <!-- <n-form>
		  <j-bus-hos-org v-model:value="queryApMatForm.appyOrgId" clearable></j-bus-hos-org>
		  <n-button type="success" @click="searchAppliedMat(paginationReactive.page)">搜索</n-button>
		</n-form>
		<j-n-data-table
		  ref="table"
		  remote
		  min-height="100px"
		  :max-height="300"
		  :data="matSelectData1"
		  :columns="matSelectColumns1 as any"
		  :pagination="pagination1"
		  @update:page="onChange1"
		  @update:page-size="onUpdatePageSize1"
		  v-model:checked-row-keys="checkRows1"
		  
		></j-n-data-table> -->

      <j-preview
        v-model:show="showAttPreview"
        :file="attPreviewInfo.file!"
        :type="attPreviewInfo.type!"
        style="z-index: 2100"
      />
      <!-- 预览 -->
      <j-preview
        v-model:show="showPreview"
        :oss-path="previewOssPath"
        :oss-path-name="previewOssPathName"
        bucket="mmis"
      />
    </j-modal>
    <!--  选择库存物资 不是在汇总表中选：这里就需要精确一点到入库的货号   -->
    <j-modal v-model:show="showMatSelect" title="选择物资" width="90%" height="80%" @confirm="addSelections2Form()">
      <j-crud
        :queryMethod="hasStroagedMatsInApplyByUniqueCode"
        :columns="matSelectColumns"
        :queryForm="queryForm"
        rowKey="key"
        ref="matSelectCrudRef"
        :show-operation-button="false"
        :checkedRowKeys="checkRows"
        @update:checkedRowKeys="handleCheck"
      >
        <template #extendFormItems>
          <n-form-item label="物资名称">
            <n-input v-model:value="queryForm.name" placeholder="物资名称搜索" clearable></n-input>
          </n-form-item>
          <n-form-item label="物资唯一编码">
            <n-input v-model:value="queryForm.matUniqueCode" placeholder="物资唯一编码搜索" clearable></n-input>
          </n-form-item>
          <n-form-item label="仓库">
            <n-select
              v-model:value="queryForm.wrhsAddr"
              :options="allWrhsInfo"
              value-field="value"
              label-field="label"
              :consistent-menu-width="true"
              :update-value-on-close="true"
              style="width: 150px"
              clearable
            ></n-select>
          </n-form-item>
        </template>
      </j-crud>
    </j-modal>

    <!--  采购发货   -->
    <OrgRestockList
      v-model:show="showPurcShipping"
      :restockStatus="'6'"
      title="采购发货"
      @confirm="addOrgRestockListToTable"
    ></OrgRestockList>
    <!--    <j-modal v-model:show="showPurcShipping" title="采购发货" width="95%" :show-footer="false">-->
    <!--      <n-row :gutter="24">-->
    <!--        <n-col :span="24">-->
    <!--          <j-crud-->
    <!--            :queryMethod="queryPurcShipping"-->
    <!--            :columns="purcShippingColumns as any"-->
    <!--            :queryForm="queryPurcShippingForm"-->
    <!--            ref="purcShippingCrudRef"-->
    <!--            :show-operation-button="false"-->
    <!--          ></j-crud>-->
    <!--        </n-col>-->
    <!--      </n-row>-->
    <!--    </j-modal>-->
    <!--  这里的弹框是查看物资申请详情的   -->
    <j-modal v-model:show="showMatApplyDetails" title="申请详情" width="80%" :show-footer="false">
      <n-row :gutter="24">
        <n-col :span="24">
          <j-title-line title="出库申请物资明细" class="exp-space"> </j-title-line>
          <j-n-data-table
            :columns="matApplyDetailsColumns as any"
            :data="details.form.mmisOutboundApplyDetailsDtos"
            :max-height="400"
          ></j-n-data-table>
        </n-col>
      </n-row>
    </j-modal>
  </n-card>
  <!--  附件展示-->
  <j-preview
    v-model:show="showAttPreview"
    :file="attPreviewInfo.file!"
    :type="attPreviewInfo.type!"
    style="z-index: 2100"
  />
  <!-- 预览 -->
  <j-preview v-model:show="showPreview" :oss-path="previewOssPath" :oss-path-name="previewOssPathName" bucket="mmis" />
</template>
<script setup lang="tsx">
  import { CRUDColumnInterface } from '@/types/comps/crud'
  import { AuditDetail } from '@/types/comps/audit'
  import AuditSelect from '@/components/common/auditFlow/auditSelect.vue'
  import { h, onMounted, PropType, reactive, ref, toRaw } from 'vue'
  import {
    DataTableRowKey,
    NDataTable,
    NInput,
    NInputNumber,
    NPopover,
    NPopselect,
    NSelect,
    NTag,
    NBadge,
    NProgress,
    NTooltip,
    NStatistic,
  } from 'naive-ui'
  import { Icon, Icon as JIcon } from '@/types/common/jcomponents'
  import { ContainerValueType } from '@/types/enums/enums'
  import { ExcelType, IPageRes } from '@/types/common/jtypes'
  import JPGlobal, { exportExcelNow } from '@/types/common/jutil'
  import { Option } from '@/types/comps/common'
  import { useUserStore } from '@/store'
  import { queryWaitOutApplyList } from '@/api/mmis/matApply/MaterialApplyWeb'
  import { queryAllMmisWrhsInfo } from '@/api/mmis/wrhsInfo/wrhsInfoWeb'
  import { queryMmisInvTypeCfgList } from '@/api/mmis/useCodeCfg/InvTypeCfgWeb'
  import { hasStroagedMatsInApply, hasStroagedMatsInApplyByUniqueCode } from '@/api/mmis/matReceipt/AsetStorageWeb'
  import {
    addMmisOutboundApply,
    queryMmisDocNumCk,
    updateMmisOutboundApply,
    newOutBoundApply,
  } from '@/api/mmis/outBound/OutboundApplyWeb'
  import { queryMmisApplyDetailList, queryMmisApplyDetailListNewKey } from '@/api/mmis/matApply/MaterialApplyDetailWeb'
  import { queryMmisMeteringModeCfg } from '@/api/mmis/useCodeCfg/MeteringModeCfgWeb'
  import { queryMmisMaterialUsageCfgList } from '@/api/mmis/useCodeCfg/MaterialUsageCfgWeb'
  import { queryMmisOutboundApplyReqDetails } from '@/api/mmis/outBound/OutboundApplyDetailsWeb'
  import { queryMmisAsetType } from '@/api/mmis/asetType/asetTypeWeb'
  import OrgRestockList from '@/views/modules/mmis/matApply/myRestockProgress/components/orgRestockList.vue'
  import { queryAuditCfg } from '@/api/sys/configMgt/auditFlowConfig.ts'
  import { queryMmisWrhsManager } from '@/api/mmis/wrhsInfo/WrhsManagerWeb.ts'
  import { index } from 'mathjs'
  import { Key } from '@vicons/ionicons5'
  import { useSysStore } from '@/store'
  import { queryMmisMaterialSumList } from '@/api/mmis/matSum/MaterialSumWeb.ts'
  import { queryOrg } from '@/api/hrm/hrmOrg.ts'
  import { querySelection } from '@/api/hrm/hrmEmp.ts'
  import { queryMmisWarehouseManagerMapping } from '@/api/mmis/wrhsManagersMapping/WarehouseManagerMappingWeb'

  const props = defineProps({
    //view
    view: {
      type: Boolean,
      default: true,
    },
    // 是否编辑
    isAdd: {
      type: Boolean,
      default: true,
    },
    details: {
      type: Object as PropType<any>,
    },
    chkState: {
      type: String,
      default: '0',
    },
  })
  const emits = defineEmits(['close'])
  //关闭模态框
  // const close = () => {
  //   emits('close')
  // }

  //tp1 采购发货
  let showPurcShipping = ref(false)
  const showPurcShippingData = () => {
    // 检查是否已选择仓库
    if (!form.value.wrhsCode) {
      window.$message.warning('请先选择仓库')
      return
    }

    showPurcShipping.value = true
  }
  let selectedRowsIds = ref([])
  const addOrgRestockListToTable = (selectedRows: any[]) => {
    selectedRows.forEach((item: any) => {
      const index = form.value?.mmisOutboundApplyDetailsDtos.findIndex(
        (existingItem: any) => existingItem.mmisId == item.id
      )
      if (index != -1) {
        return
      }
      addOrgRestockedTableData(item)
    })
    selectedRowsIds.value = selectedRows.map(row => row.id)
    console.log(selectedRowsIds.value)

    showPurcShipping.value = false
  }
  const addOrgRestockedTableData = (item: any) => {
    console.log(item)
    form.value.mmisOutboundApplyDetailsDtos.push({
      key: item.id || Date.now(), // 添加key字段
      mmisId: item.id, // 物资ID
      name: item.name, // 物资名称
      modspec: item.modspec, // 规格型号
      meterUnitName: item.meterUnitName, // 计量单位
      refPrice: item.refPrice || 0, // 参考进价，确保有默认值
      actNum: item.actNum || 0, // 库存数量
      applyNum: item.applyNum || 0, // 申请数量
      num: item.applyNum || 0, // 待出库数量，初始值为申请数量
      amt: (item.applyNum || 0) * (item.refPrice || 0), // 金额计算
      remark: item.remark || '', // 备注
      wrhsAddrName: item.wrhsAddrName, // 仓库名称
      asetType: item.asetType, // 物资类代码
      att: item.att, // 物资图片
      attName: item.attName, // 图片名称
      mfgDate: item.mfgDate || '', // 生产日期
      exprinDate: item.exprinDate || '', // 到期日期
      asetBrad: item.asetBrad || '', // 品牌名称
      supplierName: item.supplierName || '', // 供应商
      storageBchno: item.storageBchno || '', // 存储批次号
    })
  }

  // -> 选择物资(样品库 + 库存物资)
  let showMatSelect = ref(false)
  type materialsItems = {
    id: string | number
    selection: string | number
    name: string
    key: string
    code: string
    itemNum: string | number
    meterUnitName: string
    modspec: string
    mtrType: string
    mtrUnit: string
    refPrice: string
    actNum: string | number
    asetName: string
    asetType: string
    asetTypeName: string
    asetBrad: string
    sourceNum: string
    expDate: string
    att: string
    attName: string
    baseUnitCoefficient: string | number
    remark: string
    storageBchno: string
  }
  type materialsItems1 = {
    id: string | number
    appyOrgId: string
    appyer: string
    auditBchno: string
    chkState: string
    crter: string
    createTime: string
    outStatus: string
    auditFlag: string
    storageBchno: string
  }
  //分页
  const paginationReactive = reactive({
    pageCount: 1,
    page: 1,
    pageSize: 10,
    showSizePicker: true,
  })
  let pagination = paginationReactive
  // 分页 翻页
  const onChange = (page: any) => {
    searchMmisAsetInfo(page)
  }
  //分页 改变页面大小
  const onUpdatePageSize = (pageSize: number) => {
    paginationReactive.pageSize = pageSize
    paginationReactive.page = 1
    searchMmisAsetInfo(paginationReactive.page)
  }
  // 查询物资
  let matSelectData = ref<materialsItems[]>([])
  const dataRef = ref([] as any[])
  const searchMmisAsetInfo = (currpage: number) => {
    let params = {
      name: queryForm.value.name,
      wrhsAddr: queryForm.value.wrhsAddr,
      wrhsCode: form.value.wrhsCode, // 添加仓库筛选
      pageNum: currpage,
      pageSize: paginationReactive.pageSize,
    }
    // 从物资汇总表去选择物资（使用matUniqueCode作为key）
    hasStroagedMatsInApplyByUniqueCode(params)
      .then((res: IPageRes) => {
        if (res.code == 200) {
          const total = res.data.total
          paginationReactive.page = currpage
          paginationReactive.pageCount = Math.ceil(total / paginationReactive.pageSize)
          // dataRef.value = res.data.records
          matSelectData.value = res.data.records
        }
      })
      .catch(_ => {
        window.$message.error('查询失败，请联系管理员')
      })
  }
  // -> 弹框显示搜索物资1

  const showSelectTableData = () => {
    // 检查是否已选择仓库
    if (!form.value.wrhsCode) {
      window.$message.warning('请先选择仓库')
      return
    }

    showMatSelect.value = true
    // 更新查询条件，添加仓库筛选
    queryForm.value.wrhsAddr = form.value.wrhsCode
    searchMmisAsetInfo(1)
  }
  let queryForm = ref({
    name: '',
    wrhsAddr: '',
    matUniqueCode: '', // 添加物资唯一编码字段
  })

  // -> 弹框显示搜索物资2
  //改动：新加从物资申请通过的记录中选择的功能
  let showAppliedMat = ref(false)
  const showSelectTableData2 = () => {
    // 检查是否已选择仓库
    if (!form.value.wrhsCode) {
      window.$message.warning('请先选择仓库')
      return
    }

    showAppliedMat.value = true
    // 更新查询条件，添加仓库筛选
    queryApMatForm.value.wrhsCode = form.value.wrhsCode
    searchAppliedMat(1)
  }
  let queryApMatForm = ref({
    appyOrgId: '',
    wrhsCode: '', // 添加仓库代码字段
  })
  const searchAppliedMat = (currpage: number) => {
    let params = {
      //审核状态：已通过
      appyOrgId: queryApMatForm.value.appyOrgId,
      chkState: '1',
      wrhsCode: queryApMatForm.value.wrhsCode, // 添加仓库筛选
      pageNum: currpage,
      pageSize: paginationReactive.pageSize,
    }
    queryWaitOutApplyList(params)
      .then((res: IPageRes) => {
        //分页
        const total = res.data.total
        paginationReactive.page = currpage
        paginationReactive.pageCount = Math.ceil(total / paginationReactive.pageSize)
        // 查询申请通过的记录放入DataTable
        matSelectData1.value = res.data.records
      })
      .catch(_ => {
        window.$message.error('查询失败，请联系管理员')
      })
  }
  //申请通过列表分页
  const paginationReactive1 = reactive({
    pageCount: 1,
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [5, 10, 20],
  })
  let pagination1 = paginationReactive1
  // 分页 翻页
  const onChange1 = (page: any) => {
    searchAppliedMat(page)
  }
  //分页 改变页面大小
  const onUpdatePageSize1 = (pageSize: number) => {
    paginationReactive1.pageSize = pageSize
    paginationReactive1.page = 1
    searchAppliedMat(paginationReactive1.page)
  }
  //显示物资申请详情
  let showMatApplyDetails = ref(false)

  //申请通过详情填入：从申请通过的中
  const matApplySelectCrudRef = ref()
  let checkRows1 = ref<DataTableRowKey[]>([])
  let selectedRows = ref<Array<any>>([])
  const handOrgApplyMatRowKeys = (keys: DataTableRowKey[]) => {
    checkRows1.value = keys
    let pageData = matApplySelectCrudRef.value.originData
    //添加新选中的行
    const newSelectedRows = pageData.filter(
      item => checkRows1.value.includes(item.key) && !selectedRows.value.includes(item)
    )

    selectedRows.value.push(...newSelectedRows)
    //删除未选中的行
    selectedRows.value = selectedRows.value.filter(item => checkRows1.value.includes(item.key))
  }
  let matSelectData1 = ref<materialsItems1[]>([])
  const matSelectColumns1 = ref<CRUDColumnInterface[]>([
    {
      title: '#',
      key: 'key',
      type: ContainerValueType.SELECTION,
      disabled: (row: any) => form.value.matApplyIds.includes(row.key),
      width: 50,
      fixed: 'left',
    },
    {
      title: '流水号',
      key: 'serialNum',
      width: 100,
    },
    {
      title: '院区',
      key: 'hosCampus',
      width: 50,
      render: (row: any) => {
        let label = allHosCampus.value.find((item: any) => item.value === row.hosCampus)?.label
        if (!label && !row.hosCampus) return null
        return h(NTag, { type: row.hosCampus === '0' ? 'success' : 'info' }, label || row.hosCampus)
      },
    },
    {
      title: '申请科室',
      key: 'appyOrgId',
      width: 80,
    },

    {
      title: '申请人',
      key: 'appyer',
      width: 80,
    },
    {
      title: '申领摘要',
      key: 'applySummary',
      width: 350,
      align: 'center',
      render: (row: any) => {
        const summaryItems = row.applySummary
          .split(';')
          .filter(Boolean)
          .map(item => {
            const [name, applyNum] = item.split('*')
            return { name, applyNum }
          })

        return (
          <NPopover trigger="hover" style={{ maxWidth: '600px' }}>
            {{
              trigger: () => (
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(3, 1fr)',
                    gridAutoRows: 'auto',
                    gap: '4px',
                    textAlign: 'center',
                    width: '100%',
                    height: '100%',
                    border: '1px solid #e4e4e4', // 浅灰色框线
                    padding: '8px',
                    borderRadius: '4px',
                  }}
                >
                  {summaryItems.slice(0, 6).map((item, index) => (
                    <div
                      key={index}
                      style={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        gridColumn: summaryItems.length === 1 ? '2 / span 1' : 'auto',
                        border: '1px solid #e4e4e4', // 浅灰色框线
                        padding: '4px',
                        borderRadius: '4px',
                        backgroundColor: '#f5f5f5', // 浅灰色背景
                      }}
                    >
                      {index === 5 && summaryItems.length > 6 ? '...' : `${item.name}*${item.applyNum}`}
                    </div>
                  ))}
                </div>
              ),
              default: () => (
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(3, 1fr)',
                    gap: '8px',
                    whiteSpace: 'nowrap',
                    maxHeight: '400px',
                    overflowY: 'auto',
                  }}
                >
                  {summaryItems.map((item, index) => (
                    <div
                      key={index}
                      style={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        border: '1px solid #e4e4e4', // 浅灰色框线
                        padding: '4px',
                        borderRadius: '4px',
                        backgroundColor: '#f5f5f5', // 浅灰色背景
                      }}
                    >
                      {`${item.name}*${item.applyNum}`}
                    </div>
                  ))}
                </div>
              ),
            }}
          </NPopover>
        )
      },
    },
    {
      title: '申请时间',
      key: 'createTime',
      width: 100,
    },
    {
      title: '出库状态',
      key: 'outStatus',
      width: 100,
      render: (row: any) => {
        switch (row.outStatus) {
          case '0':
            return h(
              NTag,
              { type: 'warning' },
              {
                default: () => '待出库',
              }
            )
          case '1':
            return h(
              NTag,
              { type: 'success' },
              {
                default: () => '已出库',
              }
            )
          case '2':
            return h(
              NTag,
              { type: 'error' },
              {
                default: () => '已撤销',
              }
            )
        }
      },
    },
    {
      title: '申请详情',
      key: 'details',
      width: 100,
      render: (row: any) => {
        return [
          h(JIcon, {
            name: 'detail',
            width: 20,
            height: 20,
            style: { cursor: 'pointer', marginRight: '10px' },
            onClick: () => {
              auditDetials(row)
              showMatApplyDetails.value = true
            },
          }),
        ]
      },
    },
  ])
  //申请出库详情查询
  let details = ref({
    form: { mmisOutboundApplyDetailsDtos: [] },
  })
  let auditData = ref<any>({ mmisOutboundApplyDetailsDtos: [] })
  const auditDetials = (tableRow: any) => {
    details.value.form = tableRow
    queryMmisApplyDetailList(tableRow).then((res: any) => {
      auditData.value.mmisOutboundApplyDetailsDtos = res.data
      details.value.form.mmisOutboundApplyDetailsDtos = res.data
    })
  }
  let auditDetails = ref([])
  const updateCmsContractDetails = ({ details, flag }: { details: AuditDetail[]; flag: boolean }) => {
    if (flag) {
      auditDetails.value = []
      form.value.auditDetails = []
    } else {
      form.value.auditDetails = details
      auditDetails.value = details
    }
  }
  // -> 物资申请详情
  let vo1 = reactive({
    key: '',
    itemNum: '',
    name: '',
    modspec: '',
    wrhsCode: '',
    wrhsName: '',
    wrhsAddr: '',
    wrhsAddrName: '',
    meterUnitName: '',
    itemCount: 1,
    num: null,
    refPrice: null,
    amt: null,
    mfgDate: '',
    exprinDate: '',
    remark: '',
    asetBrad: '',
    supplierName: '',
    hisNum: null,
    actNum: null,
    applyNum: null,
    storageBchno: '',
    att: '',
    attName: '',
  })
  // 申请出库详情列
  const matApplyDetailsColumns = ref<CRUDColumnInterface[]>([
    {
      title: '#',
      key: 'index',
      width: 30,
      align: 'center',
      render: (row: any, index: number) => index + 1,
    },
    { title: '物资名称', key: 'name', width: 100 },
    { title: '规格', key: 'modspec', width: 100 },
    // {
    //   title: '参考进价',
    //   key: 'price',
    //   width: 100,
    // },
    {
      title: '参考进价',
      key: 'refPrice',
      width: 100,
    },
    {
      title: '库存',
      key: 'actNum',
      width: 100,
      render: (row: any) => {
        return h(
          'span',
          {
            style: {
              color: row.actNum > 0 ? '#18A058' : 'lightcoral',
            },
          },
          row.actNum
        )
      },
    },
    { title: '计量单位', key: 'meterUnitName', width: 100 },
    {
      title: '领用申请数量',
      key: 'applyNum',
      width: 100,
    },
    {
      title: '备注',
      key: 'remark',
      width: 100,
    },
    // {
    //   title: '金额',
    //   key: 'amt',
    //   width: 100,
    // },

    {
      title: '仓库',
      key: 'wrhsAddrName',
      width: 100,
    },

    {
      title: '物资类代码',
      key: 'asetType',
      width: 100,
      render: (row: any) => {
        if (!row.asetType) {
          return
        }
        let arr: any = row.asetType
        if (arr) {
          let result = ''
          let a: any = allAsetType2.value.find((item: any) => item.value == arr)
          if (a) {
            result = a.label
          }
          return result
        }
        return row.asetType
      },
    },
    //这里选择的物资是来自于汇总表的，所以那边没有图片，如果一定需要给图片让用户看，需要在入库时存图片
    {
      title: '物资图片',
      key: 'att',
      width: 100,
      render: (row: any, index: any) => fileShowRender(row, index),
    },
  ])
  //  填入申请单,从申请列表中写
  //接收查询到的审核通过的详情数据
  const addSelectApplyMat = () => {
    //检查所有记录的hoscampus是否一致
    const hoscampus = selectedRows.value.map((row: any) => row.hosCampus)
    const uniqueHoscampus = [...new Set(hoscampus)]
    if (uniqueHoscampus.length > 1) {
      window.$message.error(`不同送货院区的申请不能组合，当前已选院区为${form.value.hosCampus}，请重新选择`)
      return
    }
    // 检查是否与已有详情中的hoscampus冲突
    if (form.value.mmisOutboundApplyDetailsDtos.length > 0) {
      const existingHoscampus = form.value.hosCampus
      if (existingHoscampus !== uniqueHoscampus[0]) {
        window.$message.error(`不能选择与已有申请不同院区的申请，当前已选院区为${form.value.hosCampus}`)
        return
      }
    }

    // 获取选中行的科室ID
    const applyOrgIds = selectedRows.value.map((row: any) => row.applyOrgId)
    const uniqueApplyOrgIds = [...new Set(applyOrgIds)]
    // 检查选中行是否来自不同科室
    if (uniqueApplyOrgIds.length > 1) {
      window.$message.error('不同科室的申请不能组合，请重新选择')
      return
    }
    // 检查是否与已有详情中的科室冲突
    if (form.value.outTargetOrgId && form.value.outTargetOrgId !== uniqueApplyOrgIds[0]) {
      window.$message.error('不能选择与已有申请不同科室的申请')
      return
    }

    // 检查是否已有导入的申请
    if (form.value.applyOrgId) {
      // 如果已有导入的申请,使用现有的applyOrgId
      diyFlag.value = true
      form.value.diyOrgName = form.value.applyOrgId
      console.log(form.value.applyOrgId)
    } else {
      // 如果是新的导入,禁用目标科室并清除diyOrgName
      diyFlag.value = true
      form.value.diyOrgName = ''
    }

    //设置当前导入的院区
    form.value.hosCampus = uniqueHoscampus[0]
    //设置当前导入的申请科室
    form.value.applyOrgId = uniqueApplyOrgIds[0]
    //设置当前导入的科室
    form.value.outTargetOrgId = uniqueApplyOrgIds[0]
    //设置当前导入的申请流水号
    form.value.matApplySerial = selectedRows.value.map((row: any) => row.serialNum).join(',')
    //设置当前导入的指定接收人
    form.value.outAppyer = selectedRows.value[0].crter

    // 获取选中行的申领备注，并组合去重
    const applyRemarks = selectedRows.value.map((row: any) => row.applyRemark)
    const uniqueApplyRemarks = [...new Set(applyRemarks)]
    form.value.applyRemark = uniqueApplyRemarks.join(',')

    //把选项给到物资明细 待改：物资所在的仓库，需要在sql中查询到 --》调出来的是样品库，没有仓库代码
    let rowDatas1 = selectedRows.value

    //遍历申请列表
    rowDatas1.forEach((bch: any) => {
      //查询当前行的审核详情
      queryMmisApplyDetailListNewKey(bch).then((res: any) => {
        let mmisDetailsArr = ref<any>([])
        mmisDetailsArr.value.push(res.data)
        mmisDetailsArr.value.forEach((item: any, index: number) => {
          //遍历itemRow，因为这里仍旧包含着一个数组
          item.forEach((d: any) => {
            //每次循环创建新的vo对象,避免引用同一个对象
            let selectVO1 = {
              key: '',
              itemNum: '',
              name: '',
              modspec: '',
              wrhsCode: '',
              wrhsName: '',
              wrhsAddr: '',
              wrhsAddrName: '',
              meterUnitName: '',
              itemCount: 1,
              num: null,
              actNum: null,
              applyNum: null,
              refPrice: null,
              amt: null,
              mfgDate: '',
              exprinDate: '',
              remark: '',
              asetBrad: '',
              supplierName: '',
              hisNum: null,
              storageBchno: '',
              att: '',
              attName: '',
            }

            //拷贝数据
            Object.keys(selectVO1).forEach((key: any) => {
              selectVO1[key] = d[key] ? d[key] : null
            })
            selectVO1.itemCount = 1
            selectVO1.hisNum = d.applyNum
            selectVO1.num = d.applyNum
            selectVO1.amt = d.applyNum * d.refPrice

            form.value.mmisOutboundApplyDetailsDtos.push(selectVO1)
          })
        })

        // 检查库存并设置数量
        const mergedDetails = form.value.mmisOutboundApplyDetailsDtos.reduce((acc: any[], curr: any) => {
          // 查找是否已存在相同的记录(除了actNum和num外都相同)
          const existingItem = acc.find((item: any) => {
            return Object.keys(curr).every(key => {
              if (key === 'actNum' || key === 'num') return true
              return item[key] === curr[key]
            })
          })

          if (existingItem) {
            // 合并记录,累加num
            existingItem.num += curr.num
            // 确保num不超过actNum
            if (existingItem.num > existingItem.actNum) {
              existingItem.num = existingItem.actNum
            }
          } else {
            // 处理新记录的actNum和num
            const newItem = { ...curr }
            if (newItem.actNum === undefined || newItem.actNum === null) {
              newItem.actNum = 0
            }
            if (newItem.num > newItem.actNum) {
              newItem.num = newItem.actNum
            }
            acc.push(newItem)
          }
          return acc
        }, [])

        form.value.mmisOutboundApplyDetailsDtos = mergedDetails
      })
    })

    // 保持每件细数不变,只相加领用数量和待出库数量
    const mergedDetails = form.value.mmisOutboundApplyDetailsDtos.reduce((acc: any[], curr: any) => {
      const existingItem = acc.find((item: any) => item.itemNum === curr.itemNum)
      if (existingItem) {
        // 合并相同货号的记录
        existingItem.hisNum += curr.hisNum
        existingItem.num += curr.num
        // 确保合并后的数量不超过库存
        existingItem.num = Math.min(existingItem.num, existingItem.actNum)
      } else {
        // 添加新的记录
        acc.push({ ...curr })
      }
      return acc
    }, [])

    // 更新表单中的物资明细
    form.value.mmisOutboundApplyDetailsDtos = mergedDetails

    console.log('合并后的物资明细:', form.value.mmisOutboundApplyDetailsDtos)

    //把申请ID保存并存过去
    checkRows1.value.forEach((k: any) => {
      form.value.matApplyIds.push(k)
    })
    //关闭弹框
    showAppliedMat.value = false
    // 清除选项
    checkRows1.value = []
    showMatSelect.value = false
  }

  //自定义导入
  const checkedRowKeys = ref([])
  let selectedHasStroagedRows = ref<Array<any>>([])
  const handleCheck = (checkedKeys: DataTableRowKey[]) => {
    checkRows.value = checkedKeys
    let pageData = matSelectCrudRef.value.originData
    //添加新选中的行
    const newSelectedRows = pageData.filter(
      item => checkRows.value.includes(item.key) && !selectedHasStroagedRows.value.includes(item)
    )

    selectedHasStroagedRows.value.push(...newSelectedRows)
    //删除未选中的行
    selectedHasStroagedRows.value = selectedHasStroagedRows.value.filter(item => checkRows.value.includes(item.key))
  }

  const clearSelection = () => {
    checkRows.value = []
  }

  //填入申请单，从库存中 ,这里是库存物资填入
  let selectForm = ref([])
  let checkRows = ref<DataTableRowKey[]>([])

  //过滤用：装form表单中已有的物资编码
  let formMatCode = ref([''])
  //禁用选择目标科室，在未自定义出库时
  let diyFlag = ref(true)
  const addSelections2Form = () => {
    // 获取选中的物资数据
    let rowDatas = selectedHasStroagedRows.value

    diyFlag.value = false

    // 克隆vo模板
    let selectVO = toRaw(vo)
    const cloneVo = structuredClone(selectVO)

    // 遍历选中的物资
    rowDatas.forEach((item: any) => {
      const itemRow = toRaw(item)

      // 重置selectVO为克隆的模板
      selectVO = JPGlobal.deepCopy(cloneVo)

      // 复制属性值
      Object.keys(selectVO).forEach((key: any) => {
        selectVO[key] = itemRow[key] ? itemRow[key] : null
      })

      // 设置数量为1
      selectVO.num = 1

      // 计算金额
      const price = parseFloat(selectVO.refPrice) || 0
      selectVO.amt = selectVO.num * price

      // 检查是否已存在相同物资
      const existingIndex = form.value.mmisOutboundApplyDetailsDtos.findIndex(
        (existing: any) => existing.key === selectVO.key
      )

      if (existingIndex === -1) {
        // 如果不存在,则添加到列表末尾
        form.value.mmisOutboundApplyDetailsDtos.push(selectVO)
      } else {
        // 如果已存在,检查是否需要更新数量
        const existing = form.value.mmisOutboundApplyDetailsDtos[existingIndex]

        // 确保不超过库存数量
        const newNum = Math.min(existing.num + 1, existing.actNum)

        if (newNum > existing.num) {
          existing.num = newNum
          // 更新金额
          existing.amt = newNum * existing.refPrice
        }
      }
    })

    // 清除选择
    clearSelection()
    // 关闭选择框
    showMatSelect.value = false

    console.log('选中的物资:', selectedHasStroagedRows.value)
    console.log('表单中的物资:', form.value.mmisOutboundApplyDetailsDtos)
    console.log('禁用选择目标科室:', diyFlag.value)
  }
  // 移除全部物资申请详情
  const deleteMmisDetail = () => {
    form.value.mmisOutboundApplyDetailsDtos = []
    diyFlag.value = true
    // 清除选择科室,后端根据diyOrgName是否有值判断是否在自定义出库
    form.value.diyOrgName = ''
  }
  // 用户信息回显
  const userStore = useUserStore()
  // 科室信息回显
  const orgList = ref([])
  // ->提交申请
  const submit = async () => {
    //使用try catch是用来跳出foreach循环的，捕获异常后结束本方法的执行
    try {
      form.value.mmisOutboundApplyDetailsDtos.forEach((item: any) => {
        if (item.num == undefined || item.num < 0) {
          window.$message.warning('请正确填写申请申请数量')
          throw new Error()
        }
      })
    } catch (e) {
      return
    }
    //  出库申请
    if (props.isAdd ? true : props.chkState !== '0') {
      // 如果没有申领的id集合,设置目标科室和申请人信息
      if (!form.value.matApplyIds || form.value.matApplyIds.length === 0) {
        // 按顺序执行
        await queryOrg({ activeFlag: '1' }).then((res: any) => {
          orgList.value = res.data
          form.value.outTagetOrg = orgList.value.find(org => org.orgId === form.value.outTargetOrgId)?.orgName
        })
        await querySelection({
          pageSize: 20,
          pageNum: 1,
          empCodeOrEmpName: form.value.outAppyer,
          hideLoadingBar: true,
        }).then((res: IPageRes) => {
          if (res.data.records && res.data.records.length > 0) {
            form.value.outAppyer = res.data.records[0].empName
          }
        })
        console.log(form.value.outAppyer)
        console.log(form.value.outTagetOrg)
      }
      newOutBoundApply(form.value).then(res => {
        if (res.code == 200) {
          window.$message.success('申请成功')
          //退出
          emits('close')
        }
      })
    } else {
      updateMmisOutboundApply(form.value).then(res => {
        if (res.code == 200) {
          window.$message.success('修改申请成功')
          //退出
          emits('close')
        }
      })
    }
  }
  const matSelectCrudRef = ref()
  //  需要禁用哪一行的数据
  let disabledSelectCode = ref([''])

  // 待选物资列
  const matSelectColumns = ref<CRUDColumnInterface[]>([
    {
      title: '#',
      key: 'key',
      dataType: 'string',
      type: ContainerValueType.SELECTION,
      width: 30,
      disabled: (row: any) => row.actNum <= 0,
    },
    {
      title: '#',
      key: 'index',
      width: 50,
      align: 'center',
      render: (row: any, index: number) => index + 1,
    },
    { title: '物资名称', key: 'name', width: 100 },
    { title: '物资唯一编码', key: 'matUniqueCode', width: 120 },
    {
      title: '合同标识',
      key: 'contractType',
      width: 80,
      render: (row: any) => {
        if (!row.matUniqueCode) {
          return null
        }
        
        // 优先检查第17-20位是否为2504（新合同）
        if (row.matUniqueCode.length >= 20) {
          const chars17to20 = row.matUniqueCode.substring(16, 20)
          if (chars17to20 === '2504') {
            return h(
              'span',
              {
                style: {
                  color: '#18a058',
                  fontWeight: 'bold',
                  fontSize: '12px',
                  backgroundColor: '#e8f5e9',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  border: '1px solid #91d5a6'
                }
              },
              '新合同'
            )
          }
        }
        
        // 再检查第7、8位是否为12（旧合同）
        if (row.matUniqueCode.length >= 8) {
          const chars78 = row.matUniqueCode.substring(6, 8)
          if (chars78 === '12' && /^\d+$/.test(chars78)) {
            return h(
              'span',
              {
                style: {
                  color: '#f0a020',
                  fontWeight: 'bold',
                  fontSize: '12px',
                  backgroundColor: '#fff7e6',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  border: '1px solid #ffd591'
                }
              },
              '旧合同'
            )
          }
        }
      }
    },
    { title: '创建时间', key: 'crterTime', width: 120 },
    { title: '货号', key: 'itemNum', width: 100 },
    { title: '规格', key: 'modspec', width: 100 },
    { title: '库位代码', key: 'wrhsAddr', width: 100 },
    { title: '库位名称', key: 'wrhsAddrName', width: 100 },
    { title: '计量单位', key: 'meterUnitName', width: 100 },
    { title: '每件细数', key: 'itemCount', width: 100 },
    {
      title: '库存',
      key: 'actNum',
      width: 100,
      render: (row: any) => {
        return h(
          'span',
          {
            style: {
              color: row.actNum > 0 ? '#18A058' : 'lightcoral',
            },
          },
          row.actNum
        )
      },
    },
    {
      title: '参考进价',
      key: 'refPrice',
      width: 100,
    },
    {
      title: '物资图片',
      key: 'att',
      width: 100,
      render: (row: any, index: any) => fileShowRender(row, index),
    },
    { title: '生产日期', key: 'mfgDate', width: 100 },
    { title: '到期日', key: 'exprinDate', width: 100 },
    { title: '备注', key: 'remark', width: 100 },
    { title: '品牌', key: 'asetBrad', width: 100 },
  ])

  //table图片显示render
  //附件预览
  let showAttPreview = ref(false)
  //附件信息
  let attPreviewInfo = ref({})
  //OSS地址预览
  let showPreview = ref(false)
  //OSS路径
  let previewOssPath = ref('')
  //Oss文件名称
  let previewOssPathName = ref('')
  const fileShowRender = (row: any, index: number) => {
    const iconProps = {
      width: 24,
      height: 24,
      style: {
        cursor: 'pointer',
      },
    }
    let curOptions = ref<Option[]>([])
    if (row['attName']) {
      curOptions.value = []
      let paths = row['attName'].split(',')
      let vals = row['att'].split(',')
      paths.forEach((path: string, idx: number) => {
        if (path) {
          curOptions.value.push({
            label: path,
            value: vals[idx],
          })
        }
      })
    }
    let vnodes = [
      h(
        NPopselect,
        {
          options: curOptions.value,
          onUpdateValue: (val: any, option: Option) => {
            showPreview.value = true
            previewOssPath.value = option.value as string
            previewOssPathName.value = option.label
          },
        },
        {
          default: () =>
            h(
              NPopover,
              {
                disabled: curOptions.value.length > 0,
              },
              {
                trigger: () =>
                  h(Icon, {
                    show: curOptions.value.length > 0,
                    name: 'preview',
                    ...iconProps,
                    onClick: () => {
                      showPreview.value = true
                      previewOssPath.value = row['att']
                      previewOssPathName.value = row['attName']
                    },
                  }),
                default: () => '查看文件',
              }
            ),
        }
      ),
    ]
    return h('div', {}, vnodes)
  }
  // -> 申请数据

  //下面的是打开弹出框的回显数据
  //所有用途？
  let allPurpose = ref<Array<any>>([])
  const queryAllPurpost = () => {
    queryMmisMaterialUsageCfgList({}).then(res => {
      res.data.forEach((item: any) => {
        allPurpose.value.push({
          label: item.usageName,
          value: item.usageCode,
        })
      })
    })
  }
  //所有仓库信息
  let allWrhsInfo = ref<Array<any>>([])
  const queryAllWrhsInfo = () => {
    const currentUserName = userStore.getUserInfo.nickname!

    // 先查询仓库管理员映射关系
    queryMmisWarehouseManagerMapping({})
      .then((mappingRes: any) => {
        if (mappingRes.code === 200 && mappingRes.data && mappingRes.data.length > 0) {
          // 获取当前用户管理的仓库代码列表
          const userWarehouseCodes = mappingRes.data
            .filter((item: any) => item.manager === currentUserName)
            .map((item: any) => item.wrhsCode)

          console.log('当前用户管理的仓库代码:', userWarehouseCodes)

          // 查询所有仓库信息
          queryAllMmisWrhsInfo({}).then(res => {
            // 清空现有数据
            allWrhsInfo.value = []

            // 如果用户有管理的仓库，则只显示这些仓库
            if (userWarehouseCodes.length > 0) {
              res.data.forEach((item: any) => {
                if (userWarehouseCodes.includes(item.wrhsCode)) {
                  allWrhsInfo.value.push({
                    label: item.wrhsName,
                    value: item.wrhsCode,
                  })
                }
              })

              // 如果当前选择的仓库不在用户权限范围内，则自动选择第一个有权限的仓库
              if (form.value.wrhsCode && !userWarehouseCodes.includes(form.value.wrhsCode)) {
                form.value.wrhsCode = userWarehouseCodes[0]
                window.$message.info(`已自动切换到您有权限的仓库: ${allWrhsInfo.value[0].label}`)
              }
            } else {
              // 如果用户没有管理的仓库，显示所有仓库（保持原有逻辑）
              res.data.forEach((item: any) => {
                allWrhsInfo.value.push({
                  label: item.wrhsName,
                  value: item.wrhsCode,
                })
              })
            }
          })
        } else {
          // 如果查询映射关系失败，显示所有仓库
          queryAllMmisWrhsInfo({}).then(res => {
            allWrhsInfo.value = []
            res.data.forEach((item: any) => {
              allWrhsInfo.value.push({
                label: item.wrhsName,
                value: item.wrhsCode,
              })
            })
          })
        }
      })
      .catch(() => {
        // 如果查询映射关系出错，显示所有仓库
        queryAllMmisWrhsInfo({}).then(res => {
          allWrhsInfo.value = []
          res.data.forEach((item: any) => {
            allWrhsInfo.value.push({
              label: item.wrhsName,
              value: item.wrhsCode,
            })
          })
        })
      })
  }
  //所以入出库数据 : 以后可能会只要出库类型的
  let allInvType = ref<Array<any>>([])
  const queryInvTypeCfg = () => {
    queryMmisInvTypeCfgList({}).then(res => {
      // allInvType.value = res.data
      let filterData = res.data.filter((d: any) => d.typeCode.startsWith('C'))
      filterData.forEach((item: any) => {
        allInvType.value.push({
          label: item.name,
          value: item.typeCode,
        })
      })
    })
    console.log(allInvType.value)
  }
  //查询所有计量方式
  let allMtrType = ref<Array<Option>>([])
  const queryAllMtrType = () => {
    queryMmisMeteringModeCfg({ pageSize: 9999999, pageNum: 1 }).then(res => {
      allMtrType.value = JPGlobal.getTreeNode(res.data.records, 'meterCode', '', 'meterName')
    })
  }
  // 单据号查询
  const queryDocNum = () => {
    queryMmisDocNumCk({}).then(res => {
      form.value.docmentNum = res.data.docmentNum
      form.value.manualDocNum = res.data.docmentNum
    })
  }
  //申请人信息回显，初始化
  const init = () => {
    //开单日期
    let today = new Date()
    let year = today.getFullYear()
    let month = (today.getMonth() + 1).toString().padStart(2, '0')
    let day = today.getDate().toString().padStart(2, '0')
    form.value.billDate = `${year}-${month}-${day}`
    //单据号查询
    queryDocNum()
    //操作人信息
    form.value.orgName = userStore.getUserInfo?.hrmUser?.hrmOrgName
    form.value.empName = userStore.getUserInfo.nickname!
    form.value.empCode = userStore.getUserInfo.hrmUser?.empCode
    form.value.appyer = userStore.getUserInfo.hrmUser?.empCode
    form.value.appyOrgId = userStore.getUserInfo.hrmUser?.hrmOrgId
    //初始化扩展表单
    form.value.mmisOutboundApplyDetailsDtos = []

    // 查询当前用户对应的仓库权限
    const currentUserName = userStore.getUserInfo.nickname!
    queryMmisWarehouseManagerMapping({})
      .then((res: any) => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          console.log('仓库管理员映射数据:', res.data)
          // 过滤出当前用户管理的仓库
          const userWarehouses = res.data.filter((item: any) => item.manager === currentUserName)
          if (userWarehouses.length > 0) {
            // 如果找到了当前用户管理的仓库，设置为第一个
            form.value.wrhsCode = userWarehouses[0].wrhsCode
            console.log('当前用户管理的仓库:', userWarehouses)
            window.$message.success(`已自动选择您管理的仓库: ${userWarehouses[0].wrhsCode}`)
          } else {
            // 如果没有找到，设置默认仓库
            form.value.wrhsCode = '0001'
          }
        } else {
          // 如果查询失败或没有数据，设置默认仓库
          form.value.wrhsCode = '0001'
        }

        // 在设置完仓库后，初始化其他数据
        //初始化用途
        queryAllPurpost()
        //初始化选择框数据
        queryInvTypeCfg()
        //计量方式
        queryAllMtrType()

        // 根据设置的仓库筛选库管人员
        filterWrhsManagerByWrhsCode(form.value.wrhsCode)
        // 根据设置的仓库选择审核流程
        selectAuditFlowByWrhsCode(form.value.wrhsCode)
      })
      .catch(() => {
        // 如果查询出错，设置默认仓库
        form.value.wrhsCode = '0001'

        // 在设置完仓库后，初始化其他数据
        //初始化用途
        queryAllPurpost()
        //初始化选择框数据
        queryInvTypeCfg()
        //计量方式
        queryAllMtrType()

        // 根据设置的仓库筛选库管人员
        filterWrhsManagerByWrhsCode(form.value.wrhsCode)
        // 根据设置的仓库选择审核流程
        selectAuditFlowByWrhsCode(form.value.wrhsCode)
      })
  }
  let allAsetType = ref<Array<Option>>([])
  let allAsetType1 = ref<Array<Option>>([])
  let allAsetType2 = ref<Array<Option>>([])
  //查询所有物资目录
  const queryAllAsetType = () => {
    queryMmisAsetType({}).then(res => {
      allAsetType.value = res.data
      allAsetType.value.forEach(rootNode => {
        traverseTree(rootNode)
      })
      allAsetType1.value.forEach((item: any) => {
        allAsetType2.value.push({
          label: item.name,
          value: item.code,
        })
      })
    })
  }
  //遍历内部数据
  const traverseTree = (node: any) => {
    allAsetType1.value.push(node)
    // 如果节点有子节点，递归遍历它们
    if (node.children && node.children.length > 0) {
      node.children.forEach((child: any) => {
        traverseTree(child)
      })
    }
  }
  const sysStore = useSysStore()
  //初始化流程选择
  const queryAllFlow = () => {
    queryAuditCfg({ flowCode: 'MMIS-OUTBOUND-FLOW', systemId: sysStore.getSystemInfo.systemId }).then((res: any) => {
      form.value.auditFlow = res.data
    })
  }
  //初始化库管数据
  let allWrhsManager = ref<Array<any>>([])

  const queryAllWrhsManager = async () => {
    const res = await queryMmisWrhsManager({})
    // 保存所有库管人员数据
    allWrhsManager.value = res.data.sort((a, b) => {
      if (a.wrhsCode !== b.wrhsCode) {
        return a.wrhsCode.localeCompare(b.wrhsCode)
      }
      return a.empCode.localeCompare(b.empCode)
    })

    console.log('所有库管人员:', allWrhsManager.value)

    // 如果已选择仓库，则筛选对应的库管人员
    if (form.value.wrhsCode) {
      filterWrhsManagerByWrhsCode(form.value.wrhsCode)
    } else {
      // 否则使用所有库管人员
      form.value.wrhsManager = allWrhsManager.value
      initManagerIndexMap()
    }
  }
  // 切换tabs
  let selectedManagerIndex = ref<number>(0)
  const handleNTabsChange = (value: string) => {
    // 根据empName找到对应的索引
    selectedManagerIndex.value = form.value.wrhsManager.findIndex(item => item.empName === value)
  }

  //tp1 待分配物资
  const assignedMaterialsColumns = ref<CRUDColumnInterface[]>([
    {
      title: '操作',
      key: 'actions',
      width: 50,
      align: 'center',
      render(row: any, index: number) {
        return h(
          NPopover,
          { style: 'padding: 5px', showArrow: true },
          {
            trigger: () =>
              h(JIcon, {
                name: 'delete',
                width: 20,
                height: 20,
                style: { cursor: 'pointer', marginRight: '10px' },
                onClick: (e: Event) => {
                  e.stopPropagation()
                  form.value.mmisOutboundApplyDetailsDtos.splice(index, 1)
                  if (form.value.mmisOutboundApplyDetailsDtos && form.value.mmisOutboundApplyDetailsDtos.length > 0) {
                  } else {
                    //如果删除完，一个也没剩，也是禁用自定义科室选择
                    diyFlag.value = true
                    form.value.diyOrgName = ''
                  }
                },
              }),
            default: () => '移除',
          }
        )
      },
    },
    {
      title: '#',
      key: 'index',
      width: 40,
      align: 'left',
      render: (row: any, index: number) => index + 1,
    },
    { title: '货号', key: 'itemNum', width: 100 },
    { title: '物资唯一编码', key: 'matUniqueCode', width: 120 },
    { title: '物资名称', key: 'name', width: 100 },
    { title: '规格', key: 'modspec', width: 100 },
    { title: '库位代码', key: 'wrhsAddr', width: 100 },
    {
      title: '库位名称',
      key: 'wrhsAddrName',
      width: 100,
    },
    { title: '计量单位', key: 'meterUnitName', width: 100 },
    { title: '每件细数', key: 'itemCount', width: 100 },
    //  物资申请过来的数量，也就是历史数量
    {
      title: '库存',
      key: 'actNum',
      width: 100,
      render: (row: any) => {
            return h(
              'span',
              {
                style: {
              color: row.actNum > 0 ? '#18A058' : 'lightcoral',
            },
          },
          row.actNum
        )
      },
    },
    { title: '领用数量', key: 'hisNum', width: 100 },
    {
      title: '待出库数量',
      key: 'num',
      width: 100,
      render: (row: any, index: number) => {
        return h(NInputNumber, {
          showButton: false,
          value: row.num,
          disabled: !(props.isAdd ? true : props.chkState == '0'),
          onUpdateValue: (val: number | null) => {
            const quantity = val || 0
            if (quantity <= row.actNum) {
              form.value.mmisOutboundApplyDetailsDtos[index].num = quantity
              const price = parseFloat(row.refPrice) || 0
              form.value.mmisOutboundApplyDetailsDtos[index].amt = quantity * price
            } else {
              window.$message.error('申请数量不能超过库存')
              const actNum = row.actNum || 0
              form.value.mmisOutboundApplyDetailsDtos[index].num = actNum
              const price = parseFloat(row.refPrice) || 0
              form.value.mmisOutboundApplyDetailsDtos[index].amt = actNum * price
            }
          },
        })
      },
    },
    {
      title: '参考进价',
      key: 'refPrice',
      width: 100,
    },
    {
      title: '金额',
      key: 'amt',
      width: 100,
    },
    {
      title: '备注',
      key: 'remark',
      width: 100,
      render: (row: any) => {
        return h(NInput, {
          type: 'textarea',
          rows: 1,
          value: row.remark,
          disabled: !(props.isAdd ? true : props.chkState == '0'),
          onInput: (val: string) => {
            row.remark = val
          },
        })
      },
    },
    { title: '生产日期', key: 'mfgDate', width: 100 },
    //  这里选择的是入库已经入库完成的物资，所以这里不用计算到期日
    { title: '到期日', key: 'exprinDate', width: 100 },
    //  这里渲染一个文本域

    { title: '品牌名称', key: 'asetBrad', width: 100 },
    { title: '供应商', key: 'supplierName', width: 100 },
    {
      title: '物资图片',
      key: 'att',
      width: 100,
      render: (row: any, index: any) => fileShowRender(row, index),
    },
  ])
  //待分配物资
  let waitAssignMaterials = ref<Array<any>>([])
  //选择物资出库任务
  const selectOutboundTask = (rowKeys: DataTableRowKey[]) => {
    // 从form.mmisOutboundApplyDetailsDtos中获取选中的数据
    waitAssignMaterials.value = form.value.mmisOutboundApplyDetailsDtos.filter((row: any) => rowKeys.includes(row.key))
    window.$message.info(`选择的值为: ${rowKeys}`)
    console.log('选中的行数据:', waitAssignMaterials.value)
  }

  // 存储库管id和对应的waitOutboundApplyDetailsDtos下标映射关系
  let managerIndexMap = ref<Map<string, number>>(new Map())

  // 初始化映射关系
  const initManagerIndexMap = () => {
    managerIndexMap.value = new Map()
    console.log('=========')
    console.log(allWrhsManager.value)
    allWrhsManager.value.forEach((manager, index) => {
      managerIndexMap.value.set(manager.id, index)
    })
    console.log(managerIndexMap.value)
    form.value.managerIndexMap = managerIndexMap.value
  }

  //分配物资出库任务
  const assignOutbound = (value: string, option: Option) => {
    window.$message.info(`选择的值为: ${value}, 标签为: ${option.label}`)
    console.log('待分配的物资:', waitAssignMaterials.value)

    // 找到选中的库管对应的索引
    const selectedManagerIndex = form.value.wrhsManager.findIndex(manager => manager.id === value)

    if (selectedManagerIndex !== -1) {
      // 确保waitOutboundApplyDetailsDtos[index]存在
      if (!form.value.waitOutboundApplyDetailsDtos[selectedManagerIndex]) {
        form.value.waitOutboundApplyDetailsDtos[selectedManagerIndex] = []
      }

      // 保存分配的物资数量
      const assignedCount = waitAssignMaterials.value.length

      // 添加分配动画效果
      const flowAnimation = document.querySelector(`.flow-branch:nth-child(${selectedManagerIndex + 1})`)
      if (flowAnimation) {
        flowAnimation.classList.add('flow-branch-active')

        // 添加临时动画类
        flowAnimation.classList.add('flow-branch-assigning')

        // 3秒后移除临时动画类
        setTimeout(() => {
          flowAnimation.classList.remove('flow-branch-assigning')
        }, 3000)
      }

      // 将选中的物资添加到对应库管的待出库列表中
      waitAssignMaterials.value.forEach(material => {
        form.value.waitOutboundApplyDetailsDtos[selectedManagerIndex].push({ ...material })

        // 从mmisOutboundApplyDetailsDtos中删除已分配的记录
        const index = form.value.mmisOutboundApplyDetailsDtos.findIndex(item => item.key === material.key)
        if (index > -1) {
          form.value.mmisOutboundApplyDetailsDtos.splice(index, 1)
        }
      })

      // 清空选中的物资
      waitAssignMaterials.value = []

      // 提示成功信息并显示动画效果
      window.$message.success(`已成功将 ${assignedCount} 项物资分配给 ${option.label}`)

      // 自动切换到对应库管的标签页
      handleNTabsChange(form.value.wrhsManager[selectedManagerIndex].empName)
    }
  }
  let allHosCampus = ref<Array<any>>([])

  // 根据仓库代码筛选库管人员
  const filterWrhsManagerByWrhsCode = (wrhsCode: string) => {
    if (!wrhsCode || !allWrhsManager.value.length) return

    // 筛选出与所选仓库匹配的库管人员
    const filteredManagers = allWrhsManager.value.filter(manager => manager.wrhsCode === wrhsCode)

    if (filteredManagers.length > 0) {
      // 更新库管人员列表
      form.value.wrhsManager = filteredManagers
      // 重新初始化库管索引映射
      initManagerIndexMap()
      window.$message.success(`已筛选出${filteredManagers.length}名${getCurrentWrhsName()}的库管人员`)
    } else {
      window.$message.warning(`未找到${getCurrentWrhsName()}的库管人员，请联系管理员添加`)
    }
  }

  // 根据仓库代码自动选择审核流程
  const selectAuditFlowByWrhsCode = (wrhsCode: string) => {
    if (!wrhsCode) return

    // 查询审核流程
    queryAuditCfg({
      flowCode: 'MMIS-OUTBOUND-FLOW',
      systemId: sysStore.getSystemInfo.systemId,
    }).then((res: any) => {
      if (res.code === 200 && res.data && res.data.length > 0) {
        // 根据仓库代码选择对应的审核流程
        let selectedFlow = res.data[0] // 默认选择第一个流程

        // 根据仓库代码匹配合适的审核流程
        for (const flow of res.data) {
          if (flow.details && flow.details.length > 0) {
            // 信息科库房(0003)对应信息科审核流程
            if (
              wrhsCode === '0003' &&
              flow.details.some((detail: any) => detail.chkDeptName && detail.chkDeptName.includes('信息科'))
            ) {
              selectedFlow = flow
              break
            }
            // 一级仓库(0001)对应总务科审核流程
            else if (
              wrhsCode === '0001' &&
              flow.details.some((detail: any) => detail.chkDeptName && detail.chkDeptName.includes('总务科'))
            ) {
              selectedFlow = flow
              break
            }
            // 二级维修库(0002)对应维修科审核流程
            else if (
              wrhsCode === '0002' &&
              flow.details.some((detail: any) => detail.chkDeptName && detail.chkDeptName.includes('维修科'))
            ) {
              selectedFlow = flow
              break
            }
          }
        }

        // 设置选中的审核流程
        if (selectedFlow) {
          const steps = JPGlobal.getSteps(selectedFlow)
          const details = JPGlobal.getAuditDetails(steps)
          form.value.mmisAuditDetails = details
          form.value.auditDetails = details
          form.value.chkerFlow = selectedFlow.id

          window.$message.success(`已自动选择审核流程: ${selectedFlow.flowName}`)
        }
      }
    })
  }

  // 获取当前选择的仓库名称
  const getCurrentWrhsName = () => {
    if (!form.value.wrhsCode) return '未选择仓库'
    const wrhs = allWrhsInfo.value.find(item => item.value === form.value.wrhsCode)
    return wrhs ? wrhs.label : '未知仓库'
  }

  onMounted(() => {
    queryAllFlow()
    queryAllWrhsManager()
    queryAllWrhsInfo() // 确保仓库列表被加载
    allHosCampus.value = JPGlobal.getDictByType('HOS_CAMPUS_CHOICE')
    //如果新增就初始化
    if (props.isAdd == true) {
      init()
      queryAllAsetType()
    } else {
      //如果是修改就回显
      detailInit()
      //初始化用途
      queryAllPurpost()
      //初始化选择框数据
      queryInvTypeCfg()
      //计量方式
      queryAllMtrType()

      // 在回显完成后，根据已有仓库筛选库管人员
      setTimeout(() => {
        if (form.value.wrhsCode) {
          filterWrhsManagerByWrhsCode(form.value.wrhsCode)
        }
      }, 500)
    }
  })

  //单行申请详情回显
  const detailInit = () => {
    form.value = props.details.form
    form.value.empName = form.value.appyer
    form.value.orgName = form.value.appyOrgId
  }
  //保存结构
  let form = ref<any>({
    //默认所属院区是主院区
    hosCampus: '0',
    //默认科室
    outTargetOrgId: '',
    outTagetOrg: '',
    //外面的备注
    remark: '',
    //申请的流水号
    matApplySerial: '',
    matApplyIds: [],
    billDate: '',
    docmentNum: '',
    manualDocNum: '',
    purpose: '001',
    applyRemark: '',
    wrhsCode: '0001', // 设置默认仓库为一级仓库
    invType: 'C1',
    orgName: '',
    empName: '',
    empCode: '',
    appyOrgId: '',
    appyer: '',
    diyOrgName: '',
    mmisOutboundApplyDetailsDtos: [],
    mmisAuditDetails: [],
    auditDetails: [],
    waitOutboundApplyDetailsDtos: [[]],
    //装库管信息
    wrhsManager: [],
    //装审核流程信息，所有审核流程
    auditFlow: [],
    //装库管id和对应的waitOutboundApplyDetailsDtos下标映射关系
    managerIndexMap: new Map(),
  })
  // 结构
  type FormInfoItem = {
    title: string
    formItems: CRUDColumnInterface[]
  }
  // title-输入框
  const formItems: FormInfoItem[] = [
    {
      title: '开单信息',
      formItems: [
        { title: '开单日期', key: 'billDate', disabled: !(props.isAdd ? true : props.chkState == '0') },
        { title: '单据号', key: 'docmentNum', disabled: true },
        { title: '手工单据号', key: 'manualDocNum', disabled: !(props.isAdd ? true : props.chkState == '0') },
        //   tp1 默认领用
        {
          title: '用途',
          key: 'purpose',
          type: ContainerValueType.SELECT,
          selection: allPurpose,
          clearable: true,
          filterable: true,
          disabled: !(props.isAdd ? true : props.chkState == '0'),
        },
        {
          title: '备注',
          key: 'applyRemark',
          inputType: 'textarea',
          disabled: !(props.isAdd ? true : props.chkState == '0'),
        },
        {
          title: '业务类别',
          key: 'invType',
          type: ContainerValueType.SELECT,
          selection: allInvType,
          clearable: true,
          filterable: true,
          disabled: !(props.isAdd ? true : props.chkState == '0'),
        },
        {
          title: '仓库',
          key: 'wrhsCode',
          type: ContainerValueType.SELECT,
          selection: allWrhsInfo,
          clearable: false,
          filterable: true,
          required: true,
          disabled: !(props.isAdd ? true : props.chkState == '0'),
          callBack: (val: any) => {
            // 当仓库变化时，重新筛选库管人员并自动选择审核流程
            filterWrhsManagerByWrhsCode(val)
            selectAuditFlowByWrhsCode(val)

            // 清空物资明细和待出库任务
            if (
              form.value.mmisOutboundApplyDetailsDtos.length > 0 ||
              form.value.waitOutboundApplyDetailsDtos.some(arr => arr && arr.length > 0)
            ) {
              window.$message.warning('切换仓库后，已清空物资明细和待出库任务列表')
              form.value.mmisOutboundApplyDetailsDtos = []
              form.value.waitOutboundApplyDetailsDtos = [[]]
              initManagerIndexMap()
            }
          },
        },
        {
          title: '审核流程',
          key: 'chkerFlow',
          width: 100,
          required: true,
          trigger: 'blur',
          message: '请选择审核流程',
          disabled: true,
          tableColumnShow: false,
          formItemRender: () =>
            h(
              AuditSelect,
              {
                flowCode: 'MMIS-OUTBOUND-FLOW',
                value: form.value.chkerFlow,
                'onUpdate:value': (val: string) => {
                  form.value.chkerFlow = val
                },
                onUpdateDetails: ({ details, flag }: { details: AuditDetail[]; flag: boolean }) => {
                  if (flag) {
                    form.value.mmisAuditDetails = []
                    form.value.auditDetails = []
                  } else {
                    form.value.mmisAuditDetails = details
                    form.value.auditDetails = details
                  }
                },
              },
              () => {}
            ),
        },
        { title: '业务部门', key: 'orgName', disabled: true },
        { title: '业务员', key: 'empName', disabled: true },
        {
          title: '院区',
          key: 'hosCampus',
          width: 100,
          type: ContainerValueType.SELECT,
          dictType: 'HOS_CAMPUS_CHOICE',
        },
        {
          title: '送达科室',
          key: 'outTargetOrgId',
          width: 100,
          type: ContainerValueType.ORG,
        },
        {
          title: '指定接收人',
          key: 'outAppyer',
          width: 100,
          type: ContainerValueType.EMP,
        },
      ],
    },
  ]

  // -> 物资申请详情
  let vo = reactive({
    key: '',
    itemNum: '',
    matUniqueCode: '', // 添加物资唯一编码字段
    name: '',
    modspec: '',
    wrhsCode: '',
    wrhsName: '',
    wrhsAddr: '',
    wrhsAddrName: '',
    meterUnitName: '',
    itemCount: 1,
    num: null,
    refPrice: null,
    amt: null,
    mfgDate: '',
    exprinDate: '',
    remark: '',
    asetBrad: '',
    supplierName: '',
    hisNum: null,
    actNum: null,
    applyNum: null,
    storageBchno: '',
    att: '',
    attName: '',
  })
  const addTableData = () => {
    form.value.mmisOutboundApplyDetailsDtos.push(vo)
  }
  let showDropdownRef = ref(false)
  let xRef = ref(0)
  let yRef = ref(0)
  // 右键菜单相关
  const showDropdown = ref(false)
  const x = ref(0)
  const y = ref(0)

  const options = [
    {
      label: '删除',
      key: 'delete',
    },
  ]

  const onClickoutside = () => {
    showDropdown.value = false
  }

  const handleSelect = (key: string) => {
    showDropdown.value = false
  }
  // 可以自定义选择出库的具体物资
  const rowProps = (row: any) => {
    return {
      onContextmenu: (e: MouseEvent) => {
        e.preventDefault()
        showDropdown.value = false
        nextTick().then(() => {
          showDropdown.value = true
          x.value = e.clientX
          y.value = e.clientY
        })
      },
    }
  }
  // 行样式
  const rowClassName = (row: any) => {
    return 'custom-row'
  }

  // 物资申请详情
  const materialsColumns = ref<CRUDColumnInterface[]>([
    {
      title: '#',
      key: 'code',

      type: ContainerValueType.SELECTION,
      width: 30,
    },
    {
      title: '#',
      key: 'index',
      width: 40,
      align: 'left',
      render: (row: any, index: number) => index + 1,
    },
    { title: '物资名称', key: 'name', width: 100 },
    { title: '货号', key: 'itemNum', width: 100 },
    { title: '规格', key: 'modspec', width: 100 },
    { title: '库位代码', key: 'wrhsAddr', width: 100 },
    { title: '库位名称', key: 'wrhsAddrName', width: 100 },
    { title: '计量单位', key: 'meterUnitName', width: 100 },
    { title: '每件细数', key: 'itemCount', width: 100 },
    {
      title: '库存',
      key: 'actNum',
      width: 100,
      render: (row: any) => {
            return h(
              'span',
              {
                style: {
              color: row.actNum > 0 ? '#18A058' : 'lightcoral',
            },
          },
          row.actNum
        )
      },
    },
    { title: '领用数量', key: 'hisNum', width: 100 },
    {
      title: '待出库数量',
      key: 'num',
      width: 100,
      render: (row: any, index: number) => {
        return h(NInputNumber, {
          showButton: false,
          value: row.num,
          disabled: !(props.isAdd ? true : props.chkState == '0'),
          onUpdateValue: (val: number | null) => {
            const quantity = val || 0
            if (quantity <= row.actNum) {
              form.value.mmisOutboundApplyDetailsDtos[index].num = quantity
              const price = parseFloat(row.refPrice) || 0
              form.value.mmisOutboundApplyDetailsDtos[index].amt = quantity * price
            } else {
              window.$message.error('申请数量不能超过库存')
              const actNum = row.actNum || 0
              form.value.mmisOutboundApplyDetailsDtos[index].num = actNum
              const price = parseFloat(row.refPrice) || 0
              form.value.mmisOutboundApplyDetailsDtos[index].amt = actNum * price
            }
          },
        })
      },
    },
    {
      title: '参考进价',
      key: 'refPrice',
      width: 100,
    },
    {
      title: '金额',
      key: 'amt',
      width: 100,
    },
    {
      title: '备注',
      key: 'remark',
      width: 100,
      render: (row: any) => {
        return h(NInput, {
          type: 'textarea',
          rows: 1,
          value: row.remark,
          disabled: !(props.isAdd ? true : props.chkState == '0'),
          onInput: (val: string) => {
            row.remark = val
          },
        })
      },
    },
    { title: '生产日期', key: 'mfgDate', width: 100 },
    { title: '到期日', key: 'exprinDate', width: 100 },
    { title: '品牌名称', key: 'asetBrad', width: 100 },
    { title: '供应商', key: 'supplierName', width: 100 },
    {
      title: '物资图片',
      key: 'att',
      width: 100,
      render: (row: any, index: any) => fileShowRender(row, index),
    },
    {
      title: '操作',
      key: 'actions',
      width: 50,
      align: 'center',
      render(row: any, index: number) {
        return h(
          NPopover,
          { style: 'padding: 5px', showArrow: true },
          {
            trigger: () =>
              h(JIcon, {
                name: 'delete',
                width: 20,
                height: 20,
                style: { cursor: 'pointer', marginRight: '10px' },
                onClick: (e: Event) => {
                  e.stopPropagation()
                  form.value.mmisOutboundApplyDetailsDtos.splice(index, 1)
                  if (form.value.mmisOutboundApplyDetailsDtos && form.value.mmisOutboundApplyDetailsDtos.length > 0) {
                  } else {
                    //如果删除完，一个也没剩，也是禁用自定义科室选择
                    diyFlag.value = true
                    form.value.diyOrgName = ''
                  }
                },
              }),
            default: () => '移除',
          }
        )
      },
    },
  ])

  // 文件导出
  const tableDataRef = ref()

  const a = new Date().getTime() //获取到当前时间戳
  const b = new Date(a) //创建一个指定的日期对象
  const exportName = (now: any) => {
    const year = now.getFullYear() //年份
    const month = now.getMonth() + 1 //月份（0-11）
    const date = now.getDate() //天数（1到31）
    return '物资库房(' + year + '年' + month + '月' + date + '日导出出库申请详情清单)'
  }
  //导出出库取货详情excel
  let exportConfig = ref<ExcelType>({
    tableRef: tableDataRef,
    excelName: exportName(b),
    exportFunc: queryMmisOutboundApplyReqDetails,
    formData: () => details.value.form,
  })

  //tp1 导出Excel
  const exportExcel = () => {
    exportExcelNow(exportConfig.value)
  }

  // 计算已分配物资的百分比
  const getDistributionPercentage = () => {
    const totalItems = getTotalItemsCount()
    const distributedItems = getDistributedCount()

    if (totalItems === 0) return 0
    return Math.round((distributedItems / totalItems) * 100)
  }

  // 获取总物资数量
  const getTotalItemsCount = () => {
    // 未分配的物资数量
    const unassignedCount = form.value.mmisOutboundApplyDetailsDtos.length

    // 已分配的物资数量
    const assignedCount = form.value.waitOutboundApplyDetailsDtos.reduce((total, items) => {
      return total + (items ? items.length : 0)
    }, 0)

    return unassignedCount + assignedCount
  }

  // 获取已分配物资数量
  const getDistributedCount = () => {
    return form.value.waitOutboundApplyDetailsDtos.reduce((total, items) => {
      return total + (items ? items.length : 0)
    }, 0)
  }

  defineExpose({ submit })
</script>

<style scoped>
  .task-distribution-header {
    margin: 16px 0;
    transition: all 0.3s ease;
  }

  .task-distribution-header:hover {
    transform: translateY(-2px);
  }

  .distribution-status {
    transition: all 0.3s ease;
  }

  .animated-row {
    transition: all 0.3s ease;
  }

  .animated-row:hover {
    background-color: rgba(24, 160, 88, 0.05) !important;
    transform: translateX(4px);
  }

  /* 添加表格行的动画效果 */
  .n-data-table :deep(tr.n-data-table-tr) {
    transition: all 0.3s ease;
  }

  .n-data-table :deep(tr.n-data-table-tr:hover) {
    background-color: rgba(24, 160, 88, 0.05) !important;
    transform: translateX(4px);
  }

  /* 添加进度条动画 */
  .n-progress :deep(.n-progress-content) {
    transition: all 0.5s ease;
  }

  /* 添加标签页切换动画 */
  .n-tabs :deep(.n-tab-pane) {
    transition: opacity 0.3s ease;
  }

  /* 添加库管卡片悬停效果 */
  .manager-card {
    transition: all 0.3s ease;
  }

  .manager-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  /* 分支动画效果样式 */
  .distribution-flow-animation {
    margin: 10px auto;
    padding: 12px;
    background-color: #f9fcff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .flow-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .flow-source {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 16px;
    background-color: #e8f5e9;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
    min-width: 100px;
  }

  .flow-title {
    font-weight: bold;
    color: #18a058;
    margin-bottom: 2px;
    font-size: 12px;
  }

  .flow-count {
    font-size: 18px;
    font-weight: bold;
    color: #18a058;
  }

  .flow-icon {
    margin-top: 2px;
    color: #18a058;
  }

  .flow-branches {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px 20px;
    width: 100%;
    padding: 0 10px;
  }

  .flow-branch {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 75px;
    transition: all 0.3s ease;
    position: relative;
    margin-bottom: 5px;
  }

  .flow-branch-active {
    transform: scale(1.05);
  }

  .flow-line {
    position: relative;
    height: 35px;
    width: 2px;
    background-color: #d9d9d9;
    margin-bottom: 5px;
  }

  .flow-branch-active .flow-line {
    background-color: #18a058;
  }

  .flow-branch-active .flow-line::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, #18a058, #18a058);
    transform: translateX(-50%);
    animation: flow-down 2s infinite;
  }

  @keyframes flow-down {
    0% {
      height: 0;
      opacity: 1;
    }
    100% {
      height: 100%;
      opacity: 0.2;
    }
  }

  .flow-dot {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #d9d9d9;
  }

  .flow-branch-active .flow-dot {
    background-color: #18a058;
  }

  .flow-dot-pulse {
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(24, 160, 88, 0.7);
    }
    70% {
      box-shadow: 0 0 0 6px rgba(24, 160, 88, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(24, 160, 88, 0);
    }
  }

  .flow-arrow {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 50%) rotate(45deg);
    width: 8px;
    height: 8px;
    border-right: 2px solid #d9d9d9;
    border-bottom: 2px solid #d9d9d9;
  }

  .flow-branch-active .flow-arrow {
    border-right-color: #18a058;
    border-bottom-color: #18a058;
    animation: arrow-pulse 1.5s infinite;
  }

  @keyframes arrow-pulse {
    0% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.5;
    }
  }

  .flow-target {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 6px 8px;
    background-color: #f5f5f5;
    border-radius: 8px;
    width: 100%;
    transition: all 0.3s ease;
  }

  .flow-branch-active .flow-target {
    background-color: #e8f5e9;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  .flow-target-name {
    font-weight: bold;
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    text-align: center;
  }

  .flow-branch-active .flow-target-name {
    color: #18a058;
  }

  .flow-target-count {
    font-size: 16px;
    font-weight: bold;
    color: #666;
  }

  .flow-branch-active .flow-target-count {
    color: #18a058;
  }

  /* 分配动画效果 */
  .flow-branch-assigning .flow-line::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 0;
    background: linear-gradient(to bottom, #18a058, #18a058);
    transform: translateX(-50%);
    animation: flow-assign 1.5s ease-in-out;
  }

  @keyframes flow-assign {
    0% {
      height: 0;
      opacity: 1;
    }
    50% {
      height: 100%;
      opacity: 1;
    }
    100% {
      height: 100%;
      opacity: 0.5;
    }
  }

  .flow-branch-assigning .flow-target {
    animation: target-pulse 1.5s ease-in-out;
  }

  @keyframes target-pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(24, 160, 88, 0.3);
    }
    50% {
      transform: scale(1.1);
      box-shadow: 0 0 10px 5px rgba(24, 160, 88, 0.5);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(24, 160, 88, 0.3);
    }
  }

  /* 分支动画效果样式 - 横向布局 */
  .flow-container-horizontal {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 0;
    height: 100%;
  }

  .flow-branches-horizontal {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
    gap: 20px;
    margin-left: 30px;
    height: 100%;
  }

  .flow-branch-horizontal {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 60px;
    transition: all 0.3s ease;
    position: relative;
  }

  .flow-line-horizontal {
    position: relative;
    width: 40px;
    height: 2px;
    background-color: #d9d9d9;
    margin-right: 5px;
  }

  .flow-branch-active .flow-line-horizontal {
    background-color: #18a058;
  }

  .flow-branch-active .flow-line-horizontal::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    height: 2px;
    width: 100%;
    background: linear-gradient(to right, #18a058, #18a058);
    transform: translateY(-50%);
    animation: flow-right 2s infinite;
  }

  @keyframes flow-right {
    0% {
      width: 0;
      opacity: 1;
    }
    100% {
      width: 100%;
      opacity: 0.2;
    }
  }

  .flow-arrow-horizontal {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(50%, -50%) rotate(-45deg);
    width: 8px;
    height: 8px;
    border-top: 2px solid #d9d9d9;
    border-right: 2px solid #d9d9d9;
  }

  .flow-branch-active .flow-arrow-horizontal {
    border-top-color: #18a058;
    border-right-color: #18a058;
    animation: arrow-pulse 1.5s infinite;
  }

  /* 分支动画效果样式 - 中间发散布局 */
  .flow-container-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    gap: 15px;
    height: 100%;
    position: relative;
  }

  .flow-source-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 16px;
    background-color: #e8f5e9;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
    min-width: 100px;
  }

  .flow-branches-container {
    display: flex;
    width: 100%;
    justify-content: space-between;
    position: relative;
  }

  .flow-branches-container::before {
    content: '';
    position: absolute;
    top: -15px;
    left: 50%;
    width: 2px;
    height: 15px;
    background-color: #18a058;
    transform: translateX(-50%);
  }

  .flow-side {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 48%;
  }

  .flow-side-left {
    align-items: flex-end;
  }

  .flow-side-right {
    align-items: flex-start;
  }

  .flow-branch-t {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 80px;
    transition: all 0.3s ease;
    position: relative;
  }

  .flow-line-horizontal {
    position: absolute;
    top: 0;
    height: 2px;
    background-color: #d9d9d9;
  }

  .flow-line-left {
    right: 50%;
    width: 40px;
  }

  .flow-line-right {
    left: 50%;
    width: 40px;
  }

  .flow-branch-active .flow-line-horizontal {
    background-color: #18a058;
  }

  .flow-branch-active .flow-line-horizontal::before {
    content: '';
    position: absolute;
    top: 0;
    height: 2px;
    width: 100%;
    background: linear-gradient(to right, #18a058, #18a058);
    animation: flow-right 2s infinite;
  }

  .flow-line-vertical {
    position: relative;
    height: 30px;
    width: 2px;
    background-color: #d9d9d9;
    margin-bottom: 5px;
  }

  .flow-branch-active .flow-line-vertical {
    background-color: #18a058;
  }

  .flow-branch-active .flow-line-vertical::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, #18a058, #18a058);
    animation: flow-down 2s infinite;
  }
</style>
