# Crud 组件更新日志

## [2025-05-15]

### ✨ 新增功能

1.  **查询按钮加载状态 (`queryLoading`)**

    - **涉及组件**: `j-container` 和 `buttons`
    - **描述**: 现在查询按钮会在数据加载过程中显示加载状态，提升用户体验
    - **实现方式**:
      - 在 `container` 组件中添加 `queryLoading` 属性并传递给 `buttons` 组件
      - 在数据加载开始时设置 `loading = true`，加载完成时设置 `loading = false`
      - 分页切换、Tab切换和普通查询都会触发加载状态

2.  **自动响应查询状态**
    - **行为变更**:
      - 查询、分页切换、标签页切换时自动显示加载状态
      - 无需手动控制按钮加载状态，组件内部自动处理

## [2025-05-09]

### ✨ 新增功能

1.  **可配置的默认分页大小 (`defaultPageSize`)**

    - **Prop**: `defaultPageSize`
    - **类型**: `Number`
    - **默认值**: `20`
    - **描述**: 现在可以通过 `defaultPageSize` prop 来指定 `crud` 组件及其内部 `j-container` 组件在初始加载时每页显示的条目数量。

2.  **可配置的分页选项 (`pageSizes`)**

    - **Prop**: `pageSizes`
    - **类型**: `Array<Number>`
    - **默认值**: `[10, 20, 30, 40, 100, 500, 5000]`
    - **描述**: `crud` 组件及其内部 `j-container` 组件的分页条数选择器选项 (`page-sizes`) 现在可以通过 `pageSizes` prop 进行配置。如果 `defaultPageSize` prop 提供的值不在 `pageSizes` 数组中，该值会被自动包含进去并按升序排列。

3.  为 headerStyle 默认添加一些高度

### 🚀 优化与行为变更

1.  **自动启用虚拟滚动 (`virtualScroll`)**
    - **Prop**: `virtualScroll`
    - **类型**: `Boolean`
    - **默认值**: `false`
    - **行为变更**:
      - 当 `defaultPageSize` (或传递给 `j-container` 的 `defaultPageSize`) 大于或等于 `1000` 时，表格将 **自动强制开启虚拟滚动**，以优化大数据量下的渲染性能。
      - 当 `defaultPageSize` 小于 `2000` 时，`virtualScroll` prop 的原始行为保持不变，即由用户通过此 prop 控制是否启用虚拟滚动。
