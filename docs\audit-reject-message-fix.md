# 审批拒绝消息通知修复 🔧

## 问题现象

在测试重构后的审批系统时，发现审批拒绝场景下出现以下错误：

```
2025-06-11 15:35:33.044  INFO  [http-nio-10704-exec-2]  
com.jp.med.core.modules.common.processor.impl.NotificationProcessor : 
发送审批拒绝消息失败，没有审批详情，批次号：30001-HQ8SC255H07277E6C7E81F4178
```

## 问题分析

### 根本原因
在审批拒绝的场景下，`NotificationProcessor` 中的 `sendAuditRejectMessage` 方法需要从 `dto.getMessageSup()` 获取消息信息，但是：

1. **原有逻辑缺失**：重构后的 `DatabaseProcessor` 只在审批成功时查询审批详情
2. **消息信息缺失**：审批拒绝时，当前的 `dto` 对象中没有 `messageSup` 信息
3. **查询范围不足**：没有查询到包含消息信息的审批详情记录

### 原有版本的处理方式
原有版本中，审批详情在保存时就包含了 `messageSup` 信息：

```java
// 保存时设置消息信息
auditDetail.setMessageSup(JSON.toJSONString(appMsgSup));
auditDetail.setMessagePayload(JSON.toJSONString(auditPayload));
```

在审批拒绝时，可以直接从当前记录获取这些信息。

## 修复方案

### 修复策略
无论审批成功还是失败，都需要查询审批详情来获取完整的消息信息。

### 具体修复

在 `DatabaseProcessor.handleSingleAudit()` 方法中：

```java
// 无论审批成功还是失败，都需要查询审批详情来获取消息信息
AuditDetail queryDto = new AuditDetail();
queryDto.setAuditBchno(dto.getAuditBchno());
queryDto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
queryDto.setRecordTableName(dto.getRecordTableName());
List<AuditDetail> details = commonAuditReadMapper.queryAuditDetail(queryDto);

if (CollectionUtil.isEmpty(details)) {
    throw new AppException("您不在审核名单中");
}

// 排序并设置到上下文中
details.sort(Comparator.comparing(AuditDetail::getChkSeq));
context.setAuditDetailList(details);

// 将当前审批详情的消息信息设置到dto中（用于拒绝消息通知）
if (AuditConst.STATE_FAIL.equals(dto.getChkState())) {
    // 查找当前审批人的详情记录，获取消息信息
    details.stream()
        .filter(detail -> dto.getSysUser().getHrmUser().getEmpCode().equals(detail.getChker()))
        .findFirst()
        .ifPresent(currentDetail -> {
            dto.setMessageSup(currentDetail.getMessageSup());
            dto.setMessagePayload(currentDetail.getMessagePayload());
        });
    
    // 审批失败时更新结果
    updateAuditRes(dto.getAuditBchno(), AuditConst.RES_FAIL, dto.getResultTableName());
}
```

### 修复要点

1. **统一查询逻辑**：无论成功还是失败都查询审批详情
2. **消息信息回填**：在拒绝场景下，将查询到的消息信息设置回当前 dto
3. **精确匹配**：通过审批人代码匹配找到当前审批人的详情记录
4. **保持兼容性**：不影响原有的成功场景处理逻辑

## 修复效果

### 修复前
```
发送审批拒绝消息失败，没有审批详情，批次号：xxx
```

### 修复后
```
发送审批拒绝消息成功，批次号：xxx
```

## 技术细节

### 关键代码逻辑

1. **查询审批详情**：
   ```java
   List<AuditDetail> details = commonAuditReadMapper.queryAuditDetail(queryDto);
   ```

2. **查找当前审批人记录**：
   ```java
   details.stream()
       .filter(detail -> dto.getSysUser().getHrmUser().getEmpCode().equals(detail.getChker()))
       .findFirst()
   ```

3. **回填消息信息**：
   ```java
   .ifPresent(currentDetail -> {
       dto.setMessageSup(currentDetail.getMessageSup());
       dto.setMessagePayload(currentDetail.getMessagePayload());
   });
   ```

### 数据流向

1. **保存阶段**：`messageSup` 和 `messagePayload` 被序列化保存到数据库
2. **审批阶段**：从数据库查询包含消息信息的审批详情
3. **通知阶段**：使用查询到的消息信息发送通知

## 验证方法

### 测试场景
1. **审批拒绝**：提交审批 → 审批人拒绝 → 检查发起人是否收到拒绝通知
2. **审批通过**：提交审批 → 审批人通过 → 检查下一审批人或发起人是否收到通知

### 预期结果
- ✅ 审批拒绝时，发起人收到拒绝通知消息
- ✅ 审批通过时，下一审批人或发起人收到相应通知
- ✅ 所有消息内容格式正确，包含任务名称、审批人、时间等信息

## 总结

这次修复解决了重构后审批拒绝消息通知失败的问题，确保了：

1. **功能完整性**：所有审批场景的消息通知都能正常工作
2. **数据一致性**：消息信息从数据库正确获取和传递
3. **架构兼容性**：修复方案符合重构后的设计模式架构
4. **业务连续性**：保持了与原有版本相同的用户体验

通过这次修复，重构后的审批系统现在完全具备了原有版本的所有消息通知功能！🎉
