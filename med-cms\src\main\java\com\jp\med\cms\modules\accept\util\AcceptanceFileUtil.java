package com.jp.med.cms.modules.accept.util;

import com.jp.med.cms.modules.accept.dto.CmsAcceptanceAttachmentDto;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.DateUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.ArrayList;

/**
 * 验收附件保存工具类
 * 统一管理验收相关的文件上传到OSS的逻辑
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public class AcceptanceFileUtil {

    /**
     * OSS存储路径常量
     */
    public static final String ACCEPT_FILE_PATH = "acceptFile/";
    public static final String ACCEPT_APPLY_PATH = "acceptFile/apply/";
    public static final String ACCEPT_EXECUTE_PATH = "acceptFile/execute/";
    public static final String ACCEPT_REPORT_PATH = "acceptFile/report/";

    /**
     * 文件类型常量
     */
    public static final String FILE_TYPE_APPLY = "APPLY";           // 申请附件
    public static final String FILE_TYPE_ACCEPTANCE = "ACCEPTANCE"; // 验收附件
    public static final String FILE_TYPE_REPORT = "REPORT";         // 验收报告

    /**
     * 批量上传验收申请附件
     * 
     * @param files 文件列表
     * @param applyId 验收申请ID
     * @param hospitalId 医院ID
     * @param creator 创建人
     * @return 附件记录列表
     */
    public static List<CmsAcceptanceAttachmentDto> uploadApplyFiles(
            List<MultipartFile> files, Integer applyId, String hospitalId, String creator) {
        return uploadFiles(files, applyId, hospitalId, creator, FILE_TYPE_APPLY, ACCEPT_APPLY_PATH);
    }

    /**
     * 批量上传验收执行附件
     * 
     * @param files 文件列表
     * @param applyId 验收申请ID
     * @param hospitalId 医院ID
     * @param creator 创建人
     * @return 附件记录列表
     */
    public static List<CmsAcceptanceAttachmentDto> uploadExecuteFiles(
            List<MultipartFile> files, Integer applyId, String hospitalId, String creator) {
        return uploadFiles(files, applyId, hospitalId, creator, FILE_TYPE_ACCEPTANCE, ACCEPT_EXECUTE_PATH);
    }

    /**
     * 批量上传验收报告附件
     * 
     * @param files 文件列表
     * @param applyId 验收申请ID
     * @param hospitalId 医院ID
     * @param creator 创建人
     * @return 附件记录列表
     */
    public static List<CmsAcceptanceAttachmentDto> uploadReportFiles(
            List<MultipartFile> files, Integer applyId, String hospitalId, String creator) {
        return uploadFiles(files, applyId, hospitalId, creator, FILE_TYPE_REPORT, ACCEPT_REPORT_PATH);
    }

    /**
     * 通用文件上传方法
     * 
     * @param files 文件列表
     * @param applyId 验收申请ID
     * @param hospitalId 医院ID
     * @param creator 创建人
     * @param fileType 文件类型
     * @param ossPath OSS存储路径
     * @return 附件记录列表
     */
    private static List<CmsAcceptanceAttachmentDto> uploadFiles(
            List<MultipartFile> files, Integer applyId, String hospitalId, 
            String creator, String fileType, String ossPath) {
        
        List<CmsAcceptanceAttachmentDto> attachments = new ArrayList<>();
        
        if (CollectionUtil.isEmpty(files)) {
            return attachments;
        }
        
        String currentTime = DateUtil.getCurrentTime(null);
        
        for (MultipartFile file : files) {
            try {
                // 上传文件到OSS
                String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_CMS, ossPath, file);
                
                // 创建附件记录
                CmsAcceptanceAttachmentDto attachment = new CmsAcceptanceAttachmentDto();
                attachment.setApplyId(applyId);
                attachment.setFileType(fileType);
                attachment.setFileName(file.getOriginalFilename());
                attachment.setFilePath(filePath);
                attachment.setFileSize(file.getSize());
                attachment.setFileExt(getFileExtension(file.getOriginalFilename()));
                attachment.setBucket(OSSConst.BUCKET_CMS);
                attachment.setUploadPerson(creator);
                attachment.setUploadTime(currentTime);
                attachment.setHospitalId(hospitalId);
                attachment.setCreateTime(currentTime);
                attachment.setCreator(creator);
                attachment.setIsDeleted(MedConst.NOT_DELETED);
                
                attachments.add(attachment);
                
            } catch (Exception e) {
                throw new RuntimeException("上传文件失败: " + file.getOriginalFilename(), e);
            }
        }
        
        return attachments;
    }

    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 扩展名
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 验证文件类型是否允许
     * 
     * @param fileName 文件名
     * @return 是否允许
     */
    public static boolean isAllowedFileType(String fileName) {
        String ext = getFileExtension(fileName);
        String[] allowedTypes = {"pdf", "doc", "docx", "xls", "xlsx", "jpg", "jpeg", "png", "bmp", "gif"};
        
        for (String allowedType : allowedTypes) {
            if (allowedType.equals(ext)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证文件大小是否超限
     * 
     * @param fileSize 文件大小（字节）
     * @param maxSizeMB 最大大小（MB）
     * @return 是否超限
     */
    public static boolean isFileSizeExceeded(long fileSize, int maxSizeMB) {
        long maxSizeBytes = maxSizeMB * 1024 * 1024L;
        return fileSize > maxSizeBytes;
    }

    /**
     * 格式化文件大小
     * 
     * @param fileSize 文件大小（字节）
     * @return 格式化后的大小字符串
     */
    public static String formatFileSize(long fileSize) {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
