import request from '@/utils/request'
import { ContentTypes, RequestType } from '@/types/enums/enums'

/**
 * 提交合同验收申请
 */
export const submitAcceptanceApply = (data: FormData) => {
  return request({
    url: 'cms/cmsAcceptanceApply/submit',
    method: RequestType.POST,
    contentType: ContentTypes.FORM_DATA,
    data: data
  })
}

/**
 * 查询合同验收申请列表
 */
export const queryAcceptanceApplyList = (params: any) => {
  return request({
    url: 'cms/cmsAcceptanceApply/pageList',
    method: RequestType.POST,
    data: params
  })
}

/**
 * 获取合同验收申请详情
 */
export const getAcceptanceApplyDetail = (id: number) => {
  return request({
    url: `cms/acceptance/apply/${id}`,
    method: RequestType.GET
  })
}

/**
 * 审批合同验收
 */
export const auditAcceptance = (data: any) => {
  return request({
    url: 'cms/acceptance/audit',
    method: RequestType.POST,
    data: data
  })
}

/**
 * 执行合同验收
 */
export const executeAcceptance = (data: FormData) => {
  return request({
    url: 'cms/cmsAcceptanceExecute/execute',
    method: RequestType.POST,
    contentType: ContentTypes.FORM_DATA,
    data: data
  })
}

/**
 * 保存验收草稿
 */
export const saveAcceptanceDraft = (data: any) => {
  return request({
    url: 'cms/acceptance/draft/save',
    method: RequestType.POST,
    data: data
  })
}

/**
 * 删除验收申请
 */
export const deleteAcceptanceApply = (id: number) => {
  return request({
    url: `cms/acceptance/apply/${id}`,
    method: RequestType.DEL
  })
} 