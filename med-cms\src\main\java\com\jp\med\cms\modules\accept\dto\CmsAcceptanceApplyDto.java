package com.jp.med.cms.modules.accept.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import org.springframework.web.multipart.MultipartFile;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 合同验收申请主表
 * <AUTHOR>
 * @email -
 * @date 2025-06-13 11:34:09
 */
@Data
@TableName("cms_acceptance_apply" )
public class CmsAcceptanceApplyDto extends CommonQueryDto {


        @TableId("id")
    private Integer id;


        @TableField("apply_no")
    private String applyNo;


        @TableField("contract_id")
    private Integer contractId;


        @TableField("contract_code")
    private String contractCode;


        @TableField("contract_name")
    private String contractName;


        @TableField("acceptance_type")
    private String acceptanceType;


        @TableField("acceptance_date")
    private String acceptanceDate;


        @TableField("actual_acceptance_date")
    private String actualAcceptanceDate;


        @TableField("applicant_org")
    private String applicantOrg;


        @TableField("applicant_org_name")
    private String applicantOrgName;


        @TableField("applicant")
    private String applicant;


        @TableField("applicant_name")
    private String applicantName;


        @TableField("apply_date")
    private String applyDate;


        @TableField("acceptance_person")
    private String acceptancePerson;


        @TableField("acceptance_person_name")
    private String acceptancePersonName;


        @TableField("acceptance_dept")
    private String acceptanceDept;


        @TableField("acceptance_dept_name")
    private String acceptanceDeptName;


        @TableField("related_business")
    private String relatedBusiness;


        @TableField("need_third_party")
    private Integer needThirdParty;


        @TableField("third_party_org")
    private String thirdPartyOrg;


        @TableField("acceptance_result")
    private String acceptanceResult;


        @TableField("acceptance_desc")
    private String acceptanceDesc;


        @TableField("status")
    private String status;


        @TableField("linked_element_id")
    private String linkedElementId;


        @TableField("hospital_id")
    private String hospitalId;


        @TableField("create_time")
    private String createTime;


        @TableField("creator")
    private String creator;


        @TableField("update_time")
    private String updateTime;


        @TableField("updater")
    private String updater;


        @TableField("is_deleted")
    private Integer isDeleted;

    // 文件上传字段（不映射到数据库）
    @TableField(exist = false)
    private List<MultipartFile> attFiles;

    // 验收要素数据（不映射到数据库）
    @TableField(exist = false)
    private String elementsData;

}
