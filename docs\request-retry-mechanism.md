# 请求重试机制文档

## 概述

为了解决 "Connection prematurely closed BEFORE response" 错误，我们在 `src/utils/request.ts` 中实现了自动重试机制。

## 功能特性

### 🔄 自动重试机制

- **默认重试次数**: 3 次
- **重试延迟**: 100ms（支持指数退避）
- **智能重试条件**: 只对特定类型的网络错误进行重试

### 🎯 重试触发条件

系统会在以下情况下自动重试：

1. `Connection prematurely closed BEFORE response` - 连接过早关闭
2. `ERR_NETWORK` - 网络连接错误
3. `ECONNRESET` - 连接重置
4. `ECONNABORTED` - 连接中断
5. 超时错误（包含 "timeout" 关键字）
6. 5xx 服务器错误（500-599）

### ⚙️ 配置选项

在调用 request 时，可以通过以下参数自定义重试行为：

```typescript
// 基本用法（使用默认配置）
request({
  url: '/api/data',
  method: 'GET',
})

// 自定义重试配置
request({
  url: '/api/data',
  method: 'GET',
  maxRetries: 5, // 最大重试次数
  retryDelay: 200, // 重试延迟时间(ms)
  retryCondition: error => {
    // 自定义重试条件
    return error.code === 'CUSTOM_ERROR'
  },
})

// 禁用重试
request({
  url: '/api/data',
  method: 'GET',
  maxRetries: 0,
})
```

### 📈 指数退避策略

重试延迟采用指数退避算法：

- 第 1 次重试：延迟 100ms
- 第 2 次重试：延迟 200ms
- 第 3 次重试：延迟 400ms
- 以此类推...

### 🔍 调试信息

重试过程中会在控制台输出调试信息：

```
请求失败，正在进行第 1 次重试... Connection prematurely closed BEFORE response
请求失败，正在进行第 2 次重试... Connection prematurely closed BEFORE response
```

### 🎨 用户体验优化

1. **无感知重试**: 用户不会感知到重试过程，只有在所有重试都失败后才会看到错误提示
2. **友好错误提示**: 将技术性错误信息转换为用户友好的提示
3. **加载状态管理**: 重试期间保持适当的加载状态

### 🚫 错误处理

- 重试失败后会显示友好的错误提示
- 过滤重复的错误消息，避免用户被打扰
- 特殊错误类型会有针对性的提示文案

## 实现原理

```mermaid
graph TD
    A[发起请求] --> B[executeRequestWithRetry]
    B --> C[executeRequest]
    C --> D{请求成功?}
    D -->|是| E[返回结果]
    D -->|否| F{需要重试?}
    F -->|否| G[抛出错误]
    F -->|是| H[延迟等待]
    H --> I[递归重试]
    I --> B
```

## 注意事项

1. **幂等性**: 确保重试的请求是幂等的，避免重复操作造成数据问题
2. **性能考虑**: 重试会增加请求时间，对于实时性要求高的场景可以减少重试次数
3. **服务器压力**: 大量重试可能对服务器造成压力，建议合理设置重试参数

## 兼容性

该重试机制完全向后兼容，现有代码无需修改即可享受重试功能。
