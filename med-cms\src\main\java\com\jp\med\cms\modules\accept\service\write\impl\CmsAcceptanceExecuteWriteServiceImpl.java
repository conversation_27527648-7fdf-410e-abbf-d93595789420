package com.jp.med.cms.modules.accept.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceExecuteWriteMapper;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceAttachmentWriteMapper;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceElementWriteMapper;
import com.jp.med.cms.modules.accept.mapper.write.CmsAcceptanceApplyWriteMapper;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceExecuteDto;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceAttachmentDto;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceElementDto;
import com.jp.med.cms.modules.accept.dto.CmsAcceptanceApplyDto;
import com.jp.med.cms.modules.accept.service.write.CmsAcceptanceExecuteWriteService;
import com.jp.med.cms.modules.accept.util.AcceptanceFileUtil;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.ULIDUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 验收执行记录表
 * <AUTHOR>
 * @email -
 * @date 2025-06-13 11:34:09
 */
@Service
@Transactional(readOnly = false)
public class CmsAcceptanceExecuteWriteServiceImpl extends ServiceImpl<CmsAcceptanceExecuteWriteMapper, CmsAcceptanceExecuteDto> implements CmsAcceptanceExecuteWriteService {

    @Autowired
    private CmsAcceptanceAttachmentWriteMapper attachmentWriteMapper;

    @Autowired
    private CmsAcceptanceElementWriteMapper elementWriteMapper;

    @Autowired
    private CmsAcceptanceApplyWriteMapper applyWriteMapper;

    @Override
    public void executeAcceptance(CmsAcceptanceExecuteDto dto) {
        String currentTime = DateUtil.getCurrentTime(null);
        String executeNo = "EXE" + ULIDUtil.generate();

        // 设置基本信息
        dto.setExecuteNo(executeNo);
        dto.setExecuteDate(currentTime);
        dto.setCreateTime(currentTime);
        dto.setIsDeleted(MedConst.NOT_DELETED);

        // 保存验收执行记录
        this.save(dto);

        // 处理验收附件上传
        if (CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            saveExecuteAttachments(dto.getAttFiles(), dto.getApplyId(), dto.getHospitalId(), dto.getCreator());
        }

        // 处理验收要素结果更新
        if (dto.getElementsResultData() != null && !dto.getElementsResultData().isEmpty()) {
            updateAcceptanceElementsResult(dto.getElementsResultData(), dto.getApplyId(), dto.getCreator());
        }

        // 更新验收申请状态
        updateApplyStatus(dto.getApplyId(), dto.getResult(), currentTime, dto.getUpdater());
    }

    /**
     * 保存验收执行附件
     */
    private void saveExecuteAttachments(List<MultipartFile> files, Integer applyId, String hospitalId, String creator) {
        // 使用工具类上传文件并获取附件记录
        List<CmsAcceptanceAttachmentDto> attachments = AcceptanceFileUtil.uploadExecuteFiles(
            files, applyId, hospitalId, creator);

        // 批量保存附件记录
        for (CmsAcceptanceAttachmentDto attachment : attachments) {
            attachmentWriteMapper.insert(attachment);
        }
    }

    /**
     * 更新验收要素结果
     */
    private void updateAcceptanceElementsResult(String elementsResultJson, Integer applyId, String updater) {
        String currentTime = DateUtil.getCurrentTime(null);

        try {
            JSONArray elementsArray = JSONArray.parseArray(elementsResultJson);
            for (int i = 0; i < elementsArray.size(); i++) {
                JSONObject elementObj = elementsArray.getJSONObject(i);

                Integer elementId = elementObj.getInteger("id");
                if (elementId != null) {
                    CmsAcceptanceElementDto element = new CmsAcceptanceElementDto();
                    element.setId(elementId);
                    element.setElementStatus(elementObj.getString("elementStatus"));
                    element.setAcceptanceResult(elementObj.getString("acceptanceResult"));
                    element.setAcceptancePerson(updater);
                    element.setAcceptanceDate(currentTime);
                    element.setUpdateTime(currentTime);
                    element.setUpdater(updater);

                    elementWriteMapper.updateById(element);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("更新验收要素结果失败", e);
        }
    }

    /**
     * 更新验收申请状态
     */
    private void updateApplyStatus(Integer applyId, String result, String currentTime, String updater) {
        CmsAcceptanceApplyDto apply = new CmsAcceptanceApplyDto();
        apply.setId(applyId);
        apply.setAcceptanceResult(result);
        apply.setActualAcceptanceDate(currentTime);
        apply.setStatus("COMPLETED");
        apply.setUpdateTime(currentTime);
        apply.setUpdater(updater);

        applyWriteMapper.updateById(apply);
    }


}
