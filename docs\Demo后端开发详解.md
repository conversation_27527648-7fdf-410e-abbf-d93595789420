# 🔧 实习生Demo后端开发详解

## 🎯 代码生成器使用指南

### 📋 代码生成器功能概述
本项目集成了强大的代码生成器，可以根据数据库表自动生成完整的CRUD代码：

- **实体类**(Entity)、DTO、VO自动生成
- **Mapper接口**(读写分离)自动生成  
- **Service层**(读写分离)自动生成
- **Controller层**自动生成
- **前端API文件**自动生成
- **XML映射文件**自动生成

### 🚀 使用步骤

#### Step 1: 访问代码生成器
```
前端页面: http://localhost:8080/generator
后端接口: /generate/sys/generator/code
```

#### Step 2: 配置生成参数
```properties
# generator.properties 配置示例
mainPath=com.jp.med.leave
package=com.jp.med.leave.modules.application  
moduleName=leave
moduleRealName=LeaveApplication
author=实习生
email=<EMAIL>
tablePrefix=leave_
```

#### Step 3: 选择表并生成
```vue
<!-- 代码生成器界面操作 -->
1. 输入表名筛选
2. 勾选需要生成代码的表
3. 选择生成类型(带路径/只生成文件)
4. 点击"代码生成"按钮
5. 下载生成的代码包
```

### 🏗️ 生成的代码结构示例

```java
// 1. 实体类 LeaveApplicationEntity.java
@Entity
@TableName("leave_application")
public class LeaveApplicationEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    private String applicantId;
    private String applicantName;
    private String leaveType;
    private Date startDate;
    private Date endDate;
    private BigDecimal days;
    private String reason;
    private String status;
    private String processInstanceId;
    // getters and setters...
}

// 2. DTO类 LeaveApplicationDto.java  
public class LeaveApplicationDto {
    @NotBlank(message = "申请人ID不能为空")
    private String applicantId;
    
    @NotBlank(message = "申请人姓名不能为空") 
    private String applicantName;
    
    @NotBlank(message = "请假类型不能为空")
    private String leaveType;
    
    @NotNull(message = "开始日期不能为空")
    private Date startDate;
    
    @NotNull(message = "结束日期不能为空")
    private Date endDate;
    
    @DecimalMin(value = "0.5", message = "请假天数不能少于0.5天")
    private BigDecimal days;
    
    private String reason;
    // getters and setters...
}

// 3. VO类 LeaveApplicationVo.java
public class LeaveApplicationVo {
    private String id;
    private String applicantName;
    private String leaveTypeText;
    private String startDate;
    private String endDate;
    private String days;
    private String statusText;
    private String createdTime;
    // getters and setters...
}

// 4. 读取Mapper LeaveApplicationReadMapper.java
@Mapper
public interface LeaveApplicationReadMapper extends BaseMapper<LeaveApplicationEntity> {
    
    /**
     * 查询请假申请列表
     */
    List<LeaveApplicationVo> queryList(@Param("dto") LeaveApplicationDto dto);
    
    /**
     * 查询请假申请详情
     */
    LeaveApplicationVo queryDetail(@Param("id") String id);
}

// 5. 写入Mapper LeaveApplicationWriteMapper.java  
@Mapper
public interface LeaveApplicationWriteMapper extends BaseMapper<LeaveApplicationEntity> {
    // 继承BaseMapper即可，特殊写入操作可在此扩展
}

// 6. 读取Service LeaveApplicationReadService.java
public interface LeaveApplicationReadService extends IService<LeaveApplicationEntity> {
    
    /**
     * 分页查询请假申请列表
     */
    PageInfo<LeaveApplicationVo> queryList(LeaveApplicationDto dto);
    
    /**
     * 查询请假申请详情
     */
    LeaveApplicationVo queryDetail(String id);
}

// 7. 写入Service LeaveApplicationWriteService.java
public interface LeaveApplicationWriteService extends IService<LeaveApplicationEntity> {
    
    /**
     * 保存请假申请
     */
    boolean saveApplication(LeaveApplicationDto dto);
    
    /**
     * 更新请假申请
     */
    boolean updateApplication(LeaveApplicationDto dto);
    
    /**
     * 删除请假申请
     */
    boolean deleteApplication(String id);
}

// 8. Controller LeaveApplicationController.java
@RestController
@RequestMapping("/leave/application")
@Api(tags = "请假申请管理")
public class LeaveApplicationController {
    
    @Autowired
    private LeaveApplicationReadService readService;
    
    @Autowired  
    private LeaveApplicationWriteService writeService;
    
    @GetMapping("/list")
    @ApiOperation("查询请假申请列表")
    public CommonResult<PageInfo<LeaveApplicationVo>> queryList(LeaveApplicationDto dto) {
        PageInfo<LeaveApplicationVo> pageInfo = readService.queryList(dto);
        return CommonResult.success(pageInfo);
    }
    
    @PostMapping("/add")
    @ApiOperation("新增请假申请")
    public CommonResult<Boolean> add(@RequestBody @Valid LeaveApplicationDto dto) {
        boolean result = writeService.saveApplication(dto);
        return CommonResult.success(result);
    }
    
    @PutMapping("/update")
    @ApiOperation("更新请假申请")
    public CommonResult<Boolean> update(@RequestBody @Valid LeaveApplicationDto dto) {
        boolean result = writeService.updateApplication(dto);
        return CommonResult.success(result);
    }
    
    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除请假申请")
    public CommonResult<Boolean> delete(@PathVariable String id) {
        boolean result = writeService.deleteApplication(id);
        return CommonResult.success(result);
    }
}
```

---

## 🔄 BPM工作流集成详解

### 🚀 流程启动实现

```java
@Service
@Slf4j
public class LeaveApplicationWriteServiceImpl implements LeaveApplicationWriteService {
    
    private static final String PROCESS_KEY = "leave_approval_process";
    
    @Autowired
    private BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;
    
    @Override
    @Transactional
    public boolean saveApplication(LeaveApplicationDto dto) {
        // 1. 保存申请数据
        LeaveApplicationEntity entity = BeanUtil.copyProperties(dto, LeaveApplicationEntity.class);
        entity.setStatus("DRAFT");
        boolean saved = this.save(entity);
        
        if (saved) {
            // 2. 启动BPM流程
            try {
                String processInstanceId = createBpmProcess(entity);
                
                // 3. 更新流程实例ID和状态
                LambdaUpdateWrapper<LeaveApplicationEntity> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(LeaveApplicationEntity::getProcessInstanceId, processInstanceId)
                           .set(LeaveApplicationEntity::getStatus, "PENDING")
                           .eq(LeaveApplicationEntity::getId, entity.getId());
                this.update(null, updateWrapper);
                
                log.info("✅ 请假申请提交成功，流程实例ID: {}", processInstanceId);
                
            } catch (Exception e) {
                log.error("❌ BPM流程启动失败", e);
                throw new AppException("流程启动失败: " + e.getMessage());
            }
        }
        
        return saved;
    }
    
    /**
     * 创建BPM流程实例
     */
    private String createBmpProcess(LeaveApplicationEntity application) {
        log.info("🚀 开始创建BMP流程实例: 业务ID={}", application.getId());
        
        // A. 查询审批人员
        String deptManager = getDeptManager(application.getDeptId());
        String hrManager = getHrManager();
        
        if (StringUtils.isBlank(deptManager)) {
            throw new AppException("未找到部门经理，请联系管理员配置");
        }
        
        try {
            // 创建流程实例请求DTO
            BpmProcessInstanceCreateReqDTO reqDTO = new BpmProcessInstanceCreateReqDTO();
            reqDTO.setUserId(application.getApplicantId())
                  .setProcessDefinitionKey(PROCESS_KEY)
                  .setBusinessKey(String.valueOf(application.getId()));
            
            // 设置流程变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("applicantId", application.getApplicantId());
            variables.put("applicantName", application.getApplicantName());
            variables.put("leaveType", application.getLeaveType());
            variables.put("days", application.getDays());
            variables.put("reason", application.getReason());
            reqDTO.setVariables(variables);
            
            // 设置审批人员
            Map<String, List<String>> assignees = new HashMap<>();
            assignees.put("deptManagerApproval", Arrays.asList(deptManager));
            if (application.getDays().compareTo(new BigDecimal("3")) > 0) {
                assignees.put("hrApproval", Arrays.asList(hrManager));
            }
            reqDTO.setStartUserSelectAssignees(assignees);
            
            // 调用BPM服务创建流程实例
            CommonFeignResult result = bpmProcessInstanceFeignApi.createProcessInstance(reqDTO);
            
            if (!"200".equals(result.get("code").toString())) {
                throw new AppException("创建BMP流程实例失败: " + result.get("msg"));
            }
            
            String processInstanceId = result.get("data").toString();
            log.info("✅ BMP流程实例创建成功: {}", processInstanceId);
            
            return processInstanceId;
            
        } catch (Exception e) {
            log.error("❌ 创建BMP流程实例异常", e);
            throw new AppException("创建BMP流程实例失败: " + e.getMessage());
        }
    }
}
```

### 📨 BPM消息处理器

```java
/**
 * 请假申请BMP消息处理器
 * 继承AbstractBmpApproveMessageHandle，处理审批状态变化
 */
@Slf4j
@Component
public class LeaveApplicationBmpMessageHandler extends AbstractBmpApproveMessageHandle {
    
    @Autowired
    private LeaveApplicationWriteService leaveApplicationWriteService;
    
    @Autowired
    private SysMessageFeignService sysMessageFeignService;
    
    /**
     * 指定处理的流程标识
     */
    @Override
    public String[] getProcessIdentifier() {
        return new String[]{"leave_approval_process"};
    }
    
    /**
     * 监听消息队列
     */
    @RabbitListener(queues = "leave_approval_process")
    @Override
    public void onMessage(BmpProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
        log.info("📩 收到请假申请BMP消息: {}", msg);
        receiveMessage0(msg); // 调用父类方法处理消息
    }
    
    /**
     * 处理流程创建状态
     */
    @Override
    protected void handleCreate(BmpProcessInstanceStatus message) {
        log.info("🆕 请假申请流程创建: 业务ID={}", message.getBusinessKey());
        
        updateApplicationStatus(message.getBusinessKey(), "PENDING");
        sendNotification(message.getBusinessKey(), "您的请假申请已提交，正在审批中", "CREATED");
    }
    
    /**
     * 处理审批通过状态
     */
    @Override
    protected void handleApproved(BmpProcessInstanceStatus message) {
        log.info("✅ 请假申请审批通过: 业务ID={}", message.getBusinessKey());
        
        updateApplicationStatus(message.getBusinessKey(), "APPROVED");
        sendNotification(message.getBusinessKey(), "您的请假申请已审批通过", "APPROVED");
        
        // 更新人事系统请假记录
        updateHrmLeaveRecord(message.getBusinessKey());
    }
    
    /**
     * 处理审批拒绝状态
     */
    @Override
    protected void handleRejected(BmpProcessInstanceStatus message) {
        log.info("❌ 请假申请审批拒绝: 业务ID={}", message.getBusinessKey());
        
        updateApplicationStatus(message.getBusinessKey(), "REJECTED");
        sendNotification(message.getBusinessKey(), "您的请假申请已被拒绝", "REJECTED");
    }
    
    /**
     * 处理审批中状态
     */
    @Override
    protected void handleRunning(BmpProcessInstanceStatus message) {
        log.info("🔄 请假申请审批中: 业务ID={}", message.getBusinessKey());
        
        // 发送审批通知给下一个审批人
        sendApprovalNotification(message);
    }
    
    /**
     * 处理审批取消状态
     */
    @Override
    protected void handleCancelled(BmpProcessInstanceStatus message) {
        log.info("🚫 请假申请已取消: 业务ID={}", message.getBusinessKey());
        
        updateApplicationStatus(message.getBusinessKey(), "CANCELLED");
        sendNotification(message.getBusinessKey(), "您的请假申请已取消", "CANCELLED");
    }
    
    /**
     * 更新申请状态
     */
    private void updateApplicationStatus(String businessKey, String status) {
        try {
            LambdaUpdateWrapper<LeaveApplicationEntity> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(LeaveApplicationEntity::getStatus, status)
                       .eq(LeaveApplicationEntity::getId, businessKey);
            leaveApplicationWriteService.update(null, updateWrapper);
            
            log.info("✅ 更新请假申请状态成功: 业务ID={}, 状态={}", businessKey, status);
            
        } catch (Exception e) {
            log.error("❌ 更新请假申请状态失败: 业务ID={}", businessKey, e);
        }
    }
    
    /**
     * 发送消息通知
     */
    private void sendNotification(String businessKey, String content, String type) {
        try {
            LeaveApplicationEntity application = leaveApplicationWriteService.getById(businessKey);
            if (application == null) {
                log.error("❌ 未找到请假申请: 业务ID={}", businessKey);
                return;
            }
            
            SysMessageDto messageDto = new SysMessageDto();
            messageDto.setTitle("请假申请通知");
            messageDto.setPushText(content);
            messageDto.setCreator("SYSTEM");
            messageDto.setPushTime(new Date());
            messageDto.setReadFlag(0);
            messageDto.setUsers(new String[]{application.getApplicantId()});
            
            sysMessageFeignService.sendMessage(messageDto);
            
            log.info("✅ 消息通知发送成功: 业务ID={}, 类型={}", businessKey, type);
            
        } catch (Exception e) {
            log.error("❌ 消息通知发送失败: 业务ID={}", businessKey, e);
        }
    }
}
```

### 🔧 配置文件

```yaml
# application.yml - BMP相关配置
spring:
  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    
# BMP流程配置
bpm:
  processes:
    leave_approval:
      key: leave_approval_process
      name: 请假审批流程
      category: HR
      
  assignees:
    dept_manager: dept_manager_001
    hr_manager: hr_manager_001
```

这样实习生就能通过代码生成器快速搭建基础CRUD功能，然后集成完整的BMP工作流处理！🎯 