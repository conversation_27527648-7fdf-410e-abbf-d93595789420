package com.jp.med.cms.modules.accept.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import org.springframework.web.multipart.MultipartFile;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 验收执行记录表
 * <AUTHOR>
 * @email -
 * @date 2025-06-13 11:34:09
 */
@Data
@TableName("cms_acceptance_execute" )
public class CmsAcceptanceExecuteDto extends CommonQueryDto {


        @TableId("id")
    private Integer id;


        @TableField("apply_id")
    private Integer applyId;


        @TableField("execute_no")
    private String executeNo;


        @TableField("execute_date")
    private String executeDate;


        @TableField("execute_person")
    private String executePerson;


        @TableField("execute_person_name")
    private String executePersonName;


        @TableField("execute_dept")
    private String executeDept;


        @TableField("execute_dept_name")
    private String executeDeptName;


        @TableField("result")
    private String result;


        @TableField("result_desc")
    private String resultDesc;


        @TableField("suggestions")
    private String suggestions;


        @TableField("third_party_org")
    private String thirdPartyOrg;


        @TableField("third_party_person")
    private String thirdPartyPerson;


        @TableField("third_party_report")
    private String thirdPartyReport;


        @TableField("issues_found")
    private String issuesFound;


        @TableField("corrective_actions")
    private String correctiveActions;


        @TableField("follow_up_required")
    private Integer followUpRequired;


        @TableField("acceptor_signature")
    private String acceptorSignature;


        @TableField("supplier_signature")
    private String supplierSignature;


        @TableField("hospital_id")
    private String hospitalId;


        @TableField("create_time")
    private String createTime;


        @TableField("creator")
    private String creator;


        @TableField("update_time")
    private String updateTime;


        @TableField("updater")
    private String updater;


        @TableField("is_deleted")
    private Integer isDeleted;

    // 文件上传字段（不映射到数据库）
    @TableField(exist = false)
    private List<MultipartFile> attFiles;

    // 验收要素结果数据（不映射到数据库）
    @TableField(exist = false)
    private String elementsResultData;

}
