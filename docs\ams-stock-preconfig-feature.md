# 资产库存预配置使用科室功能

## 功能概述

在资产未入库时支持预配置使用科室功能，用户可以输入资产原值并配置科室分配方案，保存资产时自动创建折旧分配记录。

## 功能特点

- ✅ 支持未入库时预配置使用科室
- ✅ 输入原值返回分配方案
- ✅ 支持用户自定义分配比例
- ✅ 兼容复制功能场景
- ✅ 后端保存时自动处理预分配
- ✅ 事务一致性保障

## 使用流程

### 1. 新建资产预配置
1. 在资产库存页面新建资产
2. 点击"预配置使用科室"按钮
3. 输入资产原值
4. 添加科室并设置分配比例
5. 保存预配置
6. 保存资产时自动创建折旧分配记录

### 2. 复制资产预配置
1. 复制已有资产（包含使用科室信息）
2. 点击"预配置使用科室"按钮
3. 系统自动加载已有科室并平均分配
4. 用户可调整分配比例
5. 保存预配置
6. 保存资产时自动创建折旧分配记录

## 技术实现

### 前端实现
- **AssetDeprAsgnEditor.vue**: 增加预配置模式支持
- **amsStock/index.vue**: 集成预配置功能

### 后端实现
- **AmsPropertyDto**: 添加`preConfigAllocation`字段
- **AmsPropertyInboundContext**: 处理预分配逻辑

### 数据格式
- **deptUse**: 科室ID列表，逗号分隔
- **preConfigAllocation**: 科室ID:比例格式，如"001:0.6,002:0.4"

## 测试场景

### 场景1: 新建资产预配置
1. 新建资产，deptUse为空
2. 预配置编辑器显示空白状态
3. 手动添加科室并设置比例
4. 保存后自动创建分配记录

### 场景2: 复制资产预配置
1. 复制资产，deptUse包含科室信息
2. 预配置编辑器自动加载科室
3. 自动平均分配作为初始值
4. 用户可调整后保存

### 场景3: 数据过滤验证
1. 添加科室后删除部分科室
2. 系统自动过滤空科室数据
3. 只保存有效的分配记录

## 注意事项

1. 只有固定资产才会创建折旧分配记录
2. 预分配失败不影响资产入库
3. 分配比例总和必须为100%
4. 支持最多4位小数精度
