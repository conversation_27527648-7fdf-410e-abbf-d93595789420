# App风格菜单系统 📱

本文档介绍了基于gateway系统的App风格菜单展示方案，提供类似手机App的用户体验。

## 🎯 系统概述

### 设计理念
- **桌面端**：网格卡片布局，类似应用商店风格
- **移动端**：手机App风格，支持大卡片（2x2）展示
- **数据复用**：基于现有sideBar菜单数据结构
- **智能适配**：根据设备类型自动选择合适的展示方式

### 核心特性
✅ 响应式布局设计  
✅ 智能卡片尺寸分配  
✅ 搜索功能支持  
✅ 警告徽章显示  
✅ 触摸友好交互  
✅ 子菜单展开功能  

## 📐 布局规则

### 卡片尺寸规则
```typescript
// 卡片尺寸判断逻辑
if (hasChildren(item) && getChildrenCount(item) > 3) {
  // 大卡片（2x2）：子菜单超过3个的父菜单
  return 'large-card'
} else if (hasChildren(item)) {
  // 中等卡片（1x1）：有子菜单但数量≤3的父菜单
  return 'medium-card'
} else {
  // 小卡片（1x1）：叶子菜单
  return 'small-card'
}
```

### 菜单层级处理
- **一级菜单**：作为卡片标题显示
- **二级菜单**：作为卡片内容或子功能展示
- **三级菜单**：TODO - 暂时显示开发中提示

## 🛠️ 文件结构

```
src/views/modules/home/
├── app-menu.vue          # 桌面端App菜单
├── app-menu-mob.vue      # 移动端App菜单
├── index.vue             # 原有工作台（保持兼容）
└── components/           # 菜单相关组件（可扩展）

src/views/modules/demo/
└── app-menu-demo.vue     # 演示页面

docs/
└── app-menu-system.md   # 本文档
```

## 🚀 使用方法

### 1. 路由配置
系统会根据设备类型自动加载对应组件：

```typescript
// 路由加载优先级
if (isMobileDevice) {
  // 移动端：app-menu-mob.vue > app-menu.vue > index.vue
  component = getComp('views/modules/home/<USER>') 
    || getComp('views/modules/home/<USER>')
    || getComp('views/modules/home/<USER>')
} else {
  // 桌面端：app-menu.vue > index.vue
  component = getComp('views/modules/home/<USER>')
    || getComp('views/modules/home/<USER>')
}
```

### 2. 菜单数据结构
复用现有的sideBar菜单数据：

```typescript
// 菜单项结构
interface MenuItem {
  name: string                    // 菜单名称
  path?: string                   // 路由路径
  children?: MenuItem[]           // 子菜单
  meta?: {
    displayName?: string          // 显示名称
    hide?: boolean               // 是否隐藏
  }
  warnNum?: number               // 警告数量
}
```

### 3. 自定义图标
系统提供智能图标匹配：

```typescript
// 图标匹配规则
const getMenuIcon = (item: any) => {
  const menuName = getMenuTitle(item).toLowerCase()
  
  if (menuName.includes('设置')) return SettingsOutline
  if (menuName.includes('人员')) return PeopleOutline
  if (menuName.includes('报表')) return BarChartOutline
  // ... 更多匹配规则
  
  return GridOutline // 默认图标
}
```

## 🎨 样式定制

### 桌面端样式
```scss
.menu-grid {
  @apply grid gap-4;
  @apply grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6;
}

.menu-item-large {
  @apply col-span-2 row-span-2 p-6;
}

.menu-item-small {
  @apply p-4 aspect-square;
}
```

### 移动端样式
```scss
.menu-grid {
  @apply grid grid-cols-2 gap-4;
}

.large-card {
  @apply col-span-2 p-6 min-h-32;
}

.normal-card {
  @apply p-4 aspect-square;
}
```

## 📱 移动端特性

### 大卡片展示
```vue
<!-- 大卡片（2x2格子） -->
<div class="menu-card large-card">
  <div class="card-header">
    <div class="card-icon large">
      <n-icon size="36">{{ menuIcon }}</n-icon>
    </div>
    <div class="expand-icon">
      <n-icon size="16"><ChevronForwardOutline /></n-icon>
    </div>
  </div>
  
  <div class="card-body">
    <h3 class="card-title">{{ menuTitle }}</h3>
    <p class="card-subtitle">{{ childrenCount }}个子功能</p>
  </div>
  
  <div class="card-preview">
    <!-- 子功能预览 -->
    <span v-for="child in children.slice(0, 3)">
      {{ child.name }}
    </span>
  </div>
</div>
```

### 抽屉式子菜单
```vue
<!-- 移动端使用抽屉展示子菜单 -->
<n-drawer v-model:show="showSubmenuDrawer" width="100%">
  <n-drawer-content :title="submenuTitle">
    <div class="submenu-grid">
      <div v-for="item in submenuItems" class="submenu-card">
        <!-- 子菜单项 -->
      </div>
    </div>
  </n-drawer-content>
</n-drawer>
```

## 🔧 配置选项

### 系统图标配置
```typescript
const systemIconMap: Record<number, string> = {
  1: 'sys-core',      // 系统管理
  2: 'sys-hrm',       // 人力资源
  4: 'sys-pms',       // 绩效管理
  5: 'sys-purms',     // 采购管理
  6: 'sys-mmis',      // 物资管理
  11: 'sys-ams',      // 资产管理
  16: 'sys-oa',       // OA系统
  // 可根据需要扩展
}
```

### 快捷功能配置
```typescript
// 移动端快捷功能（常用功能）
const quickMenus = computed(() => {
  // 可以根据使用频率、用户配置等来定义
  return allMenus.slice(0, 4) // 显示前4个
})
```

## 🎯 最佳实践

### 1. 菜单组织
- **一级菜单**：按功能模块分类（如：系统管理、业务管理）
- **二级菜单**：具体功能页面（如：用户管理、角色管理）
- **避免过深**：尽量控制在2-3级以内

### 2. 图标选择
- **语义化**：图标要与功能相关
- **一致性**：同类功能使用相似图标
- **简洁性**：避免过于复杂的图标

### 3. 卡片布局
- **信息层次**：重要功能使用大卡片
- **视觉平衡**：合理分配大小卡片比例
- **响应式**：确保不同屏幕尺寸下的良好展示

## 🐛 常见问题

### Q: 如何添加新的菜单图标？
A: 在 `getMenuIcon` 函数中添加匹配规则：
```typescript
if (menuName.includes('新功能')) return NewIcon
```

### Q: 如何自定义大卡片的判断条件？
A: 修改 `isLargeCard` 函数的判断逻辑：
```typescript
const isLargeCard = (item: any) => {
  return hasChildren(item) && getChildrenCount(item) > 5 // 改为5个
}
```

### Q: 如何隐藏某些菜单？
A: 在菜单数据中设置 `meta.hide = true`：
```typescript
{
  name: '隐藏菜单',
  meta: { hide: true }
}
```

## 🔮 开发计划

### 已完成 ✅
- [x] 基础App菜单布局
- [x] 移动端专用组件
- [x] 大卡片展示功能
- [x] 搜索功能
- [x] 警告徽章显示

### 开发中 🚧
- [ ] 三级菜单支持
- [ ] 菜单个性化配置
- [ ] 使用频率统计
- [ ] 菜单收藏功能
- [ ] 主题色彩配置

### 计划中 📋
- [ ] 菜单拖拽排序
- [ ] 快捷键支持
- [ ] 菜单使用分析
- [ ] 多语言支持

## 📚 相关文档

- [移动端适配指南](./mobile-adaptation.md)
- [响应式主题配置](./mobile-quick-start.md)
- [Gateway系统说明](../src/types/gateway/index.ts)
- [SideBar组件文档](../src/views/layout/components/sideBar/)

## 🎉 总结

App风格菜单系统为用户提供了现代化的菜单体验：

🎯 **用户体验**：类似手机App的直观操作  
📱 **响应式设计**：适配各种设备尺寸  
🔧 **易于维护**：复用现有菜单数据结构  
🚀 **扩展性强**：支持自定义配置和主题  

通过这套系统，用户可以更高效地访问各种功能模块，特别是在移动端提供了优秀的触摸体验。
