<template>
  <div class="leftside-mobile-test">
    <j-crud
      :columns="columns"
      :query-method="queryMethod"
      :add-method="addMethod"
      :update-method="updateMethod"
      :del-method="delMethod"
      :left-side-mode="true"
      :left-side-title="'用户组'"
      name="用户"
    >
      <!-- 左侧用户组 -->
      <template #leftSide>
        <div class="user-group-sidebar">
          <div class="group-header">
            <n-button type="primary" size="small" @click="addGroup" block>
              <template #icon>
                <n-icon :component="AddOutline" />
              </template>
              新增用户组
            </n-button>
          </div>
          
          <div class="group-list">
            <div
              v-for="group in userGroups"
              :key="group.id"
              class="group-item"
              :class="{ active: selectedGroupId === group.id }"
              @click="selectGroup(group.id)"
            >
              <div class="group-info">
                <n-icon :component="PeopleOutline" class="group-icon" />
                <span class="group-name">{{ group.name }}</span>
                <n-badge :value="group.userCount" :max="99" class="group-badge" />
              </div>
              <div class="group-actions">
                <n-button size="tiny" quaternary @click.stop="editGroup(group)">
                  <template #icon>
                    <n-icon :component="CreateOutline" />
                  </template>
                </n-button>
                <n-popconfirm @positive-click="deleteGroup(group.id)">
                  <template #trigger>
                    <n-button size="tiny" quaternary type="error" @click.stop>
                      <template #icon>
                        <n-icon :component="TrashOutline" />
                      </template>
                    </n-button>
                  </template>
                  确定要删除这个用户组吗？
                </n-popconfirm>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="userGroups.length === 0" class="empty-groups">
            <n-empty description="暂无用户组" />
          </div>
        </div>
      </template>
    </j-crud>

    <!-- 用户组编辑弹窗 -->
    <n-modal v-model:show="showGroupModal" :mask-closable="false">
      <n-card
        style="width: 400px"
        :title="groupForm.id ? '编辑用户组' : '新增用户组'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form ref="groupFormRef" :model="groupForm" :rules="groupRules">
          <n-form-item label="组名" path="name">
            <n-input v-model:value="groupForm.name" placeholder="请输入用户组名称" />
          </n-form-item>
          <n-form-item label="描述" path="description">
            <n-input
              v-model:value="groupForm.description"
              type="textarea"
              placeholder="请输入用户组描述"
              :rows="3"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="showGroupModal = false">取消</n-button>
            <n-button type="primary" @click="saveGroup">确定</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { AddOutline, PeopleOutline, CreateOutline, TrashOutline } from '@vicons/ionicons5'

// 用户组数据
const userGroups = ref([
  { id: 1, name: '管理员组', description: '系统管理员', userCount: 3 },
  { id: 2, name: '普通用户组', description: '普通用户权限', userCount: 15 },
  { id: 3, name: '访客组', description: '只读权限', userCount: 8 },
  { id: 4, name: '开发组', description: '开发人员权限', userCount: 5 },
  { id: 5, name: '测试组', description: '测试人员权限', userCount: 4 }
])

const selectedGroupId = ref(1)
const showGroupModal = ref(false)
const groupFormRef = ref()

// 用户组表单
const groupForm = ref({
  id: null,
  name: '',
  description: ''
})

const groupRules = {
  name: [
    { required: true, message: '请输入用户组名称', trigger: 'blur' }
  ]
}

// 用户表格列配置
const columns = ref([
  { title: '员工编号', key: 'empNo', width: 120 },
  { title: '姓名', key: 'name', width: 100 },
  { title: '邮箱', key: 'email', width: 200 },
  { title: '电话', key: 'phone', width: 150 },
  { title: '状态', key: 'status', width: 100 }
])

// 模拟用户数据
const userData = ref([
  { id: 1, empNo: 'E001', name: '张三', email: '<EMAIL>', phone: '13800138001', status: '1', groupId: 1 },
  { id: 2, empNo: 'E002', name: '李四', email: '<EMAIL>', phone: '13800138002', status: '1', groupId: 1 },
  { id: 3, empNo: 'E003', name: '王五', email: '<EMAIL>', phone: '13800138003', status: '0', groupId: 2 },
  { id: 4, empNo: 'E004', name: '赵六', email: '<EMAIL>', phone: '13800138004', status: '1', groupId: 2 },
  { id: 5, empNo: 'E005', name: '钱七', email: '<EMAIL>', phone: '13800138005', status: '1', groupId: 3 }
])

// 方法
const selectGroup = (groupId: number) => {
  selectedGroupId.value = groupId
  // 这里可以触发用户列表的重新查询
  console.log('选中用户组:', groupId)
}

const addGroup = () => {
  groupForm.value = { id: null, name: '', description: '' }
  showGroupModal.value = true
}

const editGroup = (group: any) => {
  groupForm.value = { ...group }
  showGroupModal.value = true
}

const saveGroup = async () => {
  try {
    await groupFormRef.value?.validate()
    
    if (groupForm.value.id) {
      // 编辑
      const index = userGroups.value.findIndex(g => g.id === groupForm.value.id)
      if (index !== -1) {
        userGroups.value[index] = { ...userGroups.value[index], ...groupForm.value }
      }
      window.$message.success('用户组更新成功')
    } else {
      // 新增
      const newGroup = {
        ...groupForm.value,
        id: Date.now(),
        userCount: 0
      }
      userGroups.value.push(newGroup)
      window.$message.success('用户组创建成功')
    }
    
    showGroupModal.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const deleteGroup = (groupId: number) => {
  const index = userGroups.value.findIndex(g => g.id === groupId)
  if (index !== -1) {
    const groupName = userGroups.value[index].name
    userGroups.value.splice(index, 1)
    window.$message.success(`用户组 "${groupName}" 删除成功`)
    
    // 如果删除的是当前选中的组，切换到第一个组
    if (selectedGroupId.value === groupId && userGroups.value.length > 0) {
      selectedGroupId.value = userGroups.value[0].id
    }
  }
}

// CRUD 方法
const queryMethod = (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 根据选中的用户组过滤数据
      const filteredData = userData.value.filter(user => user.groupId === selectedGroupId.value)
      resolve({
        code: 200,
        data: {
          records: filteredData,
          total: filteredData.length,
          current: params.pageNum || 1,
          size: params.pageSize || 10
        }
      })
    }, 500)
  })
}

const addMethod = (data: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newUser = {
        ...data,
        id: Date.now(),
        groupId: selectedGroupId.value
      }
      userData.value.push(newUser)
      
      // 更新用户组的用户数量
      const group = userGroups.value.find(g => g.id === selectedGroupId.value)
      if (group) {
        group.userCount++
      }
      
      resolve({ code: 200, message: '新增成功' })
    }, 500)
  })
}

const updateMethod = (data: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = userData.value.findIndex(user => user.id === data.id)
      if (index !== -1) {
        userData.value[index] = { ...userData.value[index], ...data }
      }
      resolve({ code: 200, message: '更新成功' })
    }, 500)
  })
}

const delMethod = (data: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = userData.value.findIndex(user => user.id === data.id)
      if (index !== -1) {
        userData.value.splice(index, 1)
        
        // 更新用户组的用户数量
        const group = userGroups.value.find(g => g.id === data.groupId)
        if (group && group.userCount > 0) {
          group.userCount--
        }
      }
      resolve({ code: 200, message: '删除成功' })
    }, 500)
  })
}

onMounted(() => {
  console.log('LeftSide 移动端测试页面已加载')
})
</script>

<style lang="less" scoped>
.leftside-mobile-test {
  height: 100vh;
}

.user-group-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--n-color-target);
}

.group-header {
  padding: 16px;
  border-bottom: 1px solid var(--n-border-color);
}

.group-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.group-item:hover {
  background: var(--n-color-target-hover);
}

.group-item.active {
  background: var(--n-color-primary-opacity);
  border-left-color: var(--n-color-primary);
}

.group-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.group-icon {
  color: var(--n-color-primary);
  font-size: 16px;
  flex-shrink: 0;
}

.group-name {
  font-weight: 500;
  color: var(--n-text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.group-badge {
  margin-left: 4px;
}

.group-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.group-item:hover .group-actions {
  opacity: 1;
}

.empty-groups {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .group-item {
    padding: 16px 12px;
  }
  
  .group-actions {
    opacity: 1; /* 移动端始终显示操作按钮 */
  }
  
  .group-name {
    font-size: 14px;
  }
}
</style>
