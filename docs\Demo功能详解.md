# 🚀 实习生Demo高级功能详解

## 🔐 用户上下文与权限控制

### 📋 UserContext 用户上下文

UserContext是系统中的核心组件，基于ThreadLocal实现，用于存储当前请求的用户信息和权限数据。

```java
/**
 * 用户上下文 - 基于ThreadLocal的线程安全实现
 */
@Data
public class UserContext {
    private static final ThreadLocal<UserContext> threadLocal = new ThreadLocal<>();

    /**
     * 员工编码
     */
    private String empCode;

    /**
     * 组织ID
     */
    private String orgId;

    /**
     * 用户信息
     */
    private SysUser sysUser;

    /**
     * 系统类型 (Web/App)
     */
    private String sysType;

    /**
     * 医院ID
     */
    private String hospitalId;

    // 静态方法提供便捷访问
    public static void set(UserContext context) {
        threadLocal.set(context);
    }

    public static UserContext get() {
        return threadLocal.get();
    }

    public static void remove() {
        threadLocal.remove();
    }

    public static String getEmpCode() {
        UserContext context = get();
        return context != null ? context.empCode : null;
    }

    /**
     * 权限检查方法
     * @param permissionKey 权限关键字
     * @return 是否有权限
     */
    public static boolean hasPermission(String permissionKey) {
        if (get() == null || UserContext.getSysUser() == null) {
            return false;
        }

        SysUser sysUser = UserContext.getSysUser();
        // admin用户拥有所有权限
        if ("admin".equals(sysUser.getUsername())) {
            return true;
        }

        List<SysRole> permissionList = sysUser.getPermissionList();
        return permissionList != null &&
                permissionList.stream()
                        .map(SysRole::getName)
                        .anyMatch(name -> name.contains(permissionKey));
    }

    /**
     * 获取当前用户的组织ID（数据权限控制）
     * @param permissionKey 权限关键字
     * @return 组织ID或null
     */
    public static String getCurrentOrgId(String permissionKey) {
        if (get() == null || UserContext.getSysUser() == null) {
            return null;
        }

        SysUser sysUser = UserContext.getSysUser();
        // 如果有指定权限，返回null（表示可以查看所有数据）
        if (hasPermission(permissionKey)) {
            return null;
        }

        // 否则只能查看本组织数据
        return sysUser.getHrmUser() != null ? sysUser.getHrmUser().getHrmOrgId() : null;
    }
}
```

### 🎯 在请假Demo中的应用

```java
@Service
public class LeaveApplicationWriteServiceImpl implements LeaveApplicationWriteService {
    
    @Override
    public boolean saveApplication(LeaveApplicationDto dto) {
        // 1. 从用户上下文获取当前用户信息
        String currentEmpCode = UserContext.getEmpCode();
        SysUser currentUser = UserContext.getSysUser();
        
        if (StringUtils.isBlank(currentEmpCode)) {
            throw new AppException("用户未登录");
        }
        
        // 2. 设置申请人信息
        dto.setApplicantId(currentEmpCode);
        dto.setApplicantName(currentUser.getRealName());
        dto.setDeptId(currentUser.getHrmUser().getHrmOrgId());
        dto.setDeptName(currentUser.getHrmUser().getOrgName());
        
        // 3. 权限检查
        if (!UserContext.hasPermission("LEAVE_APPLY")) {
            throw new AppException("您没有请假申请权限");
        }
        
        // 4. 数据权限控制 - 只能申请自己的请假
        if (!currentEmpCode.equals(dto.getApplicantId())) {
            throw new AppException("不能代他人申请请假");
        }
        
        // 5. 保存申请数据
        LeaveApplicationEntity entity = BeanUtil.copyProperties(dto, LeaveApplicationEntity.class);
        entity.setCreatedBy(currentEmpCode);
        entity.setCreatedTime(new Date());
        
        return this.save(entity);
    }
    
    @Override
    public PageInfo<LeaveApplicationVo> queryList(LeaveApplicationDto dto) {
        // 数据权限控制
        String currentOrgId = UserContext.getCurrentOrgId("LEAVE_MANAGE_ALL");
        if (StringUtils.isNotBlank(currentOrgId)) {
            // 普通用户只能查看本部门的请假申请
            dto.setCurSysOrgId(currentOrgId);
        }
        // 管理员可以查看所有申请（currentOrgId为null）
        
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<LeaveApplicationVo> list = leaveApplicationReadMapper.queryList(dto);
        return new PageInfo<>(list);
    }
}
```

---

## 🎯 AOP切面编程详解

### 📋 CommonQueryAspect 通用查询切面

这是系统中最重要的切面，负责用户上下文的初始化、权限验证、日志记录等功能。

```java
/**
 * 通用查询切面 - 统一处理用户认证、权限控制、日志记录
 */
@Slf4j
@Aspect
@Component
public class CommonQueryAspect {

    @Autowired
    private SysLogFeignService sysLogFeignService;

    /**
     * 切点定义 - 匹配所有Controller方法
     */
    @Pointcut("execution(* com.jp.med.*.modules.*.controller..*.*(..))")
    public void requestBefore() {
    }

    /**
     * 前置通知 - 在方法执行前进行用户认证和权限设置
     */
    @Before("requestBefore()")
    public void doBefore(JoinPoint joinPoint) {
        // 1. 清空ThreadLocal，避免线程复用时的数据污染
        UserContext.remove();

        // 2. 获取HTTP请求对象
        HttpServletRequest request = getCurrentRequest();
        
        // 3. 从请求头获取用户信息
        String userInfo = getHeaderValue(request, MedConst.HEADER_USER_INFO_KEY);
        String sysKey = getHeaderValue(request, MedConst.HEADER_SYS_KEY);
        
        if (StringUtils.isNotBlank(userInfo)) {
            // 4. 解析用户信息并设置上下文
            SysUser sysUser = JSONObject.parseObject(userInfo, SysUser.class);
            setupUserContext(sysUser);
            
            // 5. 为DTO对象注入用户信息和权限数据
            injectUserInfoToArgs(joinPoint.getArgs(), sysUser, sysKey);
            
            // 6. 记录请求日志
            logRequestInfo(request, joinPoint);
        }
    }

    /**
     * 环绕通知 - 记录方法执行时间和异常信息
     */
    @Around("execution(* com.jp.med.*.modules.*.controller..*.*(..))")
    public Object log(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = getCurrentRequest();
        
        // 跳过日志系统自身的请求
        if (request.getRequestURI().contains("/sys/log")) {
            return joinPoint.proceed();
        }
        
        long startTime = System.currentTimeMillis();
        Object result = null;
        
        try {
            result = joinPoint.proceed();
            
            // 记录成功日志
            long executionTime = System.currentTimeMillis() - startTime;
            saveLog(request, joinPoint, executionTime);
            
        } catch (Throwable throwable) {
            // 记录异常日志
            logException(request, joinPoint, throwable);
            throw throwable;
        }
        
        return result;
    }

    /**
     * 设置用户上下文
     */
    private void setupUserContext(SysUser sysUser) {
        try {
            UserContext context = new UserContext();
            context.setSysUser(sysUser);
            
            if (sysUser.getHrmUser() != null && !"admin".equals(sysUser.getUsername())) {
                context.setEmpCode(sysUser.getHrmUser().getEmpCode());
                context.setOrgId(sysUser.getHrmUser().getHrmOrgId());
            } else {
                context.setEmpCode("admin");
            }
            
            UserContext.set(context);
            log.debug("✅ 用户上下文设置成功: {}", sysUser.getUsername());
            
        } catch (Exception e) {
            log.error("❌ 设置用户上下文失败", e);
        }
    }

    /**
     * 为方法参数注入用户信息
     */
    private void injectUserInfoToArgs(Object[] args, SysUser sysUser, String sysKey) {
        for (Object arg : args) {
            if (arg == null) continue;
            
            try {
                // 使用反射调用DTO的setter方法
                injectUserInfo(arg, sysUser);
                injectHospitalId(arg, sysUser);
                injectOrgPermissions(arg, sysUser, sysKey);
                injectEmpPermissions(arg, sysUser, sysKey);
                
            } catch (Exception e) {
                log.debug("参数注入失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 注入用户信息
     */
    private void injectUserInfo(Object arg, SysUser sysUser) {
        invokeMethod(arg, "setSysUser", SysUser.class, sysUser);
    }

    /**
     * 注入医院ID
     */
    private void injectHospitalId(Object arg, SysUser sysUser) {
        invokeMethod(arg, "setHospitalId", String.class, sysUser.getHospitalId());
        UserContext.get().setHospitalId(sysUser.getHospitalId());
    }

    /**
     * 注入组织权限数据
     */
    private void injectOrgPermissions(Object arg, SysUser sysUser, String sysKey) {
        String routePath = getRoutePath(arg);
        String sysType = getSysType(arg);
        String authLevel = getAuthLevel(sysUser, routePath, sysType);
        
        if (MedConst.DATA_AUTO_LEVEL_2.equals(authLevel)) {
            String orgId = getByKey(sysKey, sysUser, authLevel);
            invokeMethod(arg, "setCurSysOrgId", String.class, orgId);
        }
    }

    /**
     * 注入员工权限数据
     */
    private void injectEmpPermissions(Object arg, SysUser sysUser, String sysKey) {
        String routePath = getRoutePath(arg);
        String sysType = getSysType(arg);
        String authLevel = getAuthLevel(sysUser, routePath, sysType);
        
        if (MedConst.DATA_AUTO_LEVEL_3.equals(authLevel)) {
            String empCode = getByKey(sysKey, sysUser, authLevel);
            invokeMethod(arg, "setCurEmpCode", String.class, empCode);
        }
    }

    /**
     * 通用反射方法调用
     */
    private void invokeMethod(Object target, String methodName, Class<?> paramType, Object value) {
        try {
            Method method = target.getClass().getMethod(methodName, paramType);
            method.invoke(target, value);
        } catch (Exception e) {
            // 方法不存在或调用失败，忽略
        }
    }

    /**
     * 获取权限级别
     * @param sysUser 用户信息
     * @param routePath 路由路径
     * @param sysType 系统类型
     * @return 权限级别
     */
    private String getAuthLevel(SysUser sysUser, String routePath, String sysType) {
        // 实现权限级别判断逻辑
        // MedConst.DATA_AUTO_LEVEL_1: 全部数据
        // MedConst.DATA_AUTO_LEVEL_2: 本组织数据
        // MedConst.DATA_AUTO_LEVEL_3: 个人数据
        
        if ("admin".equals(sysUser.getUsername())) {
            return MedConst.DATA_AUTO_LEVEL_1;
        }
        
        // 根据用户权限判断数据访问级别
        List<SysRole> permissions = sysUser.getPermissionList();
        if (permissions != null) {
            for (SysRole role : permissions) {
                if (role.getName().contains("ADMIN")) {
                    return MedConst.DATA_AUTO_LEVEL_1;
                } else if (role.getName().contains("MANAGER")) {
                    return MedConst.DATA_AUTO_LEVEL_2;
                }
            }
        }
        
        return MedConst.DATA_AUTO_LEVEL_3; // 默认个人数据
    }
}
```

### 🎯 在请假Demo中的切面应用

```java
/**
 * 请假申请特定切面
 */
@Slf4j
@Aspect
@Component
public class LeaveApplicationAspect {

    /**
     * 切点 - 请假申请相关方法
     */
    @Pointcut("execution(* com.jp.med.leave.modules.application.controller..*.*(..))")
    public void leaveApplicationMethods() {
    }

    /**
     * 前置通知 - 权限检查
     */
    @Before("leaveApplicationMethods()")
    public void checkPermission(JoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        
        // 根据方法名检查不同权限
        switch (methodName) {
            case "add":
            case "save":
                if (!UserContext.hasPermission("LEAVE_APPLY")) {
                    throw new AppException("您没有请假申请权限");
                }
                break;
            case "update":
                if (!UserContext.hasPermission("LEAVE_EDIT")) {
                    throw new AppException("您没有请假编辑权限");
                }
                break;
            case "delete":
                if (!UserContext.hasPermission("LEAVE_DELETE")) {
                    throw new AppException("您没有请假删除权限");
                }
                break;
            case "approve":
                if (!UserContext.hasPermission("LEAVE_APPROVE")) {
                    throw new AppException("您没有请假审批权限");
                }
                break;
        }
    }

    /**
     * 后置通知 - 业务日志记录
     */
    @AfterReturning(pointcut = "leaveApplicationMethods()", returning = "result")
    public void logBusinessOperation(JoinPoint joinPoint, Object result) {
        String methodName = joinPoint.getSignature().getName();
        String empCode = UserContext.getEmpCode();
        
        // 记录业务操作日志
        String operation = getOperationName(methodName);
        log.info("📝 请假业务操作: 用户[{}] 执行[{}]操作", empCode, operation);
        
        // 可以发送消息通知相关人员
        if ("add".equals(methodName) || "approve".equals(methodName)) {
            sendNotification(joinPoint.getArgs(), operation);
        }
    }

    /**
     * 异常通知 - 记录异常操作
     */
    @AfterThrowing(pointcut = "leaveApplicationMethods()", throwing = "exception")
    public void logException(JoinPoint joinPoint, Exception exception) {
        String methodName = joinPoint.getSignature().getName();
        String empCode = UserContext.getEmpCode();
        
        log.error("❌ 请假操作异常: 用户[{}] 执行[{}]时发生异常: {}", 
                empCode, methodName, exception.getMessage());
    }
}
```

---

## 📋 CommonQueryDto 通用查询DTO

### 🎯 基类设计

```java
/**
 * 通用查询DTO基类 - 所有查询DTO都应继承此类
 */
@Getter
@Setter
public class CommonQueryDto {

    /**
     * 用户信息（切面自动注入）
     */
    @TableField(exist = false)
    @ExcelIgnore
    private SysUser sysUser;

    /**
     * 系统类型 (Web/App)
     */
    @TableField(exist = false)
    @ExcelIgnore
    private String sysType;

    /**
     * 分页参数
     */
    @TableField(exist = false)
    @ExcelIgnore
    private Integer pageSize = 20;

    @TableField(exist = false)
    @ExcelIgnore
    private Integer pageNum = 1;

    /**
     * 时间范围查询
     */
    @TableField(exist = false)
    @ExcelIgnore
    private String startTime;

    @TableField(exist = false)
    @ExcelIgnore
    private String endTime;

    /**
     * 医院ID（数据权限）
     */
    @TableField(exist = false)
    @ExcelIgnore
    private String hospitalId;

    /**
     * 组织ID（数据权限）
     */
    @TableField(exist = false)
    @ExcelIgnore
    private String curSysOrgId;

    /**
     * 员工编码（数据权限）
     */
    @TableField(exist = false)
    @ExcelIgnore
    private String curEmpCode;

    /**
     * 路由路径（权限控制）
     */
    @TableField(exist = false)
    @ExcelIgnore
    private String routePath;

    /**
     * 是否分页
     */
    @ExcelIgnore
    @TableField(exist = false)
    private boolean notPaging = false;

    /**
     * 检查是否为管理员用户
     */
    public Boolean checkAdminUser() {
        return sysUser != null && "admin".equals(sysUser.getUsername());
    }

    /**
     * 检查是否为非管理员用户
     */
    public Boolean checkNotAdmin() {
        return !checkAdminUser();
    }

    /**
     * 获取路由路径（供切面使用）
     */
    public String getRoutePath() {
        return routePath;
    }

    /**
     * 获取系统类型（供切面使用）
     */
    public String getSysType() {
        return StrUtil.isBlank(sysType) ? "0" : sysType; // 默认Web端
    }
}
```

### 🎯 请假查询DTO示例

```java
/**
 * 请假申请查询DTO
 */
@Getter
@Setter
public class LeaveApplicationQueryDto extends CommonQueryDto {

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请人员工编码
     */
    private String applicantId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 请假类型
     */
    private String leaveType;

    /**
     * 审批状态
     */
    private String status;

    /**
     * 申请日期范围
     */
    private String[] dateRange;

    /**
     * 重写路由路径，用于权限控制
     */
    @Override
    public String getRoutePath() {
        return "/leave/application";
    }

    /**
     * 动态构建查询条件
     */
    public LambdaQueryWrapper<LeaveApplicationEntity> buildQueryWrapper() {
        LambdaQueryWrapper<LeaveApplicationEntity> wrapper = new LambdaQueryWrapper<>();
        
        // 基本查询条件
        wrapper.like(StringUtils.isNotBlank(applicantName), 
                    LeaveApplicationEntity::getApplicantName, applicantName)
               .eq(StringUtils.isNotBlank(applicantId), 
                  LeaveApplicationEntity::getApplicantId, applicantId)
               .eq(StringUtils.isNotBlank(leaveType), 
                  LeaveApplicationEntity::getLeaveType, leaveType)
               .eq(StringUtils.isNotBlank(status), 
                  LeaveApplicationEntity::getStatus, status);

        // 时间范围查询
        if (dateRange != null && dateRange.length == 2) {
            wrapper.between(LeaveApplicationEntity::getCreatedTime, 
                          dateRange[0], dateRange[1]);
        }

        // 数据权限控制
        if (StringUtils.isNotBlank(getCurSysOrgId())) {
            // 部门级权限：只能查看本部门数据
            wrapper.eq(LeaveApplicationEntity::getDeptId, getCurSysOrgId());
        }
        
        if (StringUtils.isNotBlank(getCurEmpCode())) {
            // 个人级权限：只能查看个人数据
            wrapper.eq(LeaveApplicationEntity::getApplicantId, getCurEmpCode());
        }
        
        // 排序
        wrapper.orderByDesc(LeaveApplicationEntity::getCreatedTime);
        
        return wrapper;
    }
}
```

---

## 🛡️ 权限控制最佳实践

### 📋 权限控制注解

```java
/**
 * 权限控制注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequirePermission {
    
    /**
     * 需要的权限码
     */
    String[] value() default {};
    
    /**
     * 权限检查类型
     */
    PermissionType type() default PermissionType.ANY;
    
    /**
     * 数据权限级别
     */
    DataLevel dataLevel() default DataLevel.ALL;
}

/**
 * 权限检查类型
 */
public enum PermissionType {
    ANY,    // 任意一个权限
    ALL     // 所有权限
}

/**
 * 数据权限级别
 */
public enum DataLevel {
    ALL,        // 全部数据
    ORG,        // 组织数据
    PERSONAL    // 个人数据
}
```

### 🎯 使用示例

```java
@RestController
@RequestMapping("/leave/application")
public class LeaveApplicationController {

    /**
     * 查询请假申请 - 根据用户权限自动控制数据范围
     */
    @GetMapping("/list")
    @RequirePermission(value = "LEAVE_VIEW", dataLevel = DataLevel.ORG)
    public CommonResult<PageInfo<LeaveApplicationVo>> queryList(LeaveApplicationQueryDto dto) {
        PageInfo<LeaveApplicationVo> pageInfo = leaveApplicationReadService.queryList(dto);
        return CommonResult.success(pageInfo);
    }

    /**
     * 新增请假申请 - 只能申请个人请假
     */
    @PostMapping("/add")
    @RequirePermission(value = "LEAVE_APPLY", dataLevel = DataLevel.PERSONAL)
    public CommonResult<Boolean> add(@RequestBody LeaveApplicationDto dto) {
        boolean result = leaveApplicationWriteService.saveApplication(dto);
        return CommonResult.success(result);
    }

    /**
     * 审批请假 - 需要审批权限
     */
    @PostMapping("/approve/{id}")
    @RequirePermission(value = {"LEAVE_APPROVE", "DEPT_MANAGER"}, type = PermissionType.ANY)
    public CommonResult<Boolean> approve(@PathVariable String id, 
                                       @RequestBody ApprovalDto approvalDto) {
        boolean result = leaveApplicationWriteService.approve(id, approvalDto);
        return CommonResult.success(result);
    }

    /**
     * 管理员功能 - 查看所有请假申请
     */
    @GetMapping("/admin/list")
    @RequirePermission(value = "LEAVE_ADMIN", dataLevel = DataLevel.ALL)
    public CommonResult<PageInfo<LeaveApplicationVo>> adminQueryList(LeaveApplicationQueryDto dto) {
        PageInfo<LeaveApplicationVo> pageInfo = leaveApplicationReadService.adminQueryList(dto);
        return CommonResult.success(pageInfo);
    }
}
```

### 🎯 实习生学习要点

1. **理解ThreadLocal**：掌握线程本地存储的原理和使用场景
2. **AOP切面编程**：学习如何使用注解实现横切关注点
3. **反射机制**：理解Java反射在框架中的应用
4. **权限设计**：学习RBAC权限模型的实现
5. **数据权限**：掌握基于组织架构的数据权限控制
6. **请求上下文**：理解Web请求的生命周期管理

通过这些高级功能，实习生可以深入理解企业级Java后端架构的核心技术！🚀 