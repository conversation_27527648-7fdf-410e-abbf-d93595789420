package com.jp.med.cms.modules.accept.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.cms.modules.accept.dto.CmsAcceptanceExecuteDto;
import com.jp.med.cms.modules.accept.service.read.CmsAcceptanceExecuteReadService;
import com.jp.med.cms.modules.accept.service.write.CmsAcceptanceExecuteWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 验收执行记录表
 * <AUTHOR>
 * @email -
 * @date 2025-06-13 11:34:09
 */
@Api(value = "验收执行记录表", tags = "验收执行记录表")
@RestController
@RequestMapping("cmsAcceptanceExecute")
public class CmsAcceptanceExecuteController {

    @Autowired
    private CmsAcceptanceExecuteReadService cmsAcceptanceExecuteReadService;

    @Autowired
    private CmsAcceptanceExecuteWriteService cmsAcceptanceExecuteWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询验收执行记录表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody CmsAcceptanceExecuteDto dto){
        return CommonResult.paging(cmsAcceptanceExecuteReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询验收执行记录表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CmsAcceptanceExecuteDto dto){
        return CommonResult.success(cmsAcceptanceExecuteReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增验收执行记录表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody CmsAcceptanceExecuteDto dto){
        cmsAcceptanceExecuteWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 执行验收（带附件）
     */
    @ApiOperation("执行合同验收")
    @PostMapping("/execute")
    public CommonResult<?> executeAcceptance(CmsAcceptanceExecuteDto dto){
        cmsAcceptanceExecuteWriteService.executeAcceptance(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改验收执行记录表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CmsAcceptanceExecuteDto dto){
        cmsAcceptanceExecuteWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除验收执行记录表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CmsAcceptanceExecuteDto dto){
        cmsAcceptanceExecuteWriteService.removeById(dto);
        return CommonResult.success();
    }

}
