# BPMN 拖拽功能修复说明

## 问题描述 🐛

原始的拖拽功能实现导致主页面无法滚动，主要问题包括：

1. **全局事件监听**：在 `document` 级别监听 `mousemove` 和 `touchmove` 事件
2. **事件阻止**：`touchmove` 事件无条件使用 `preventDefault()`，阻止了页面正常滚动
3. **缺乏边界检测**：没有区分事件是否发生在 BPMN 画布区域内

## 解决方案 ✅

### 1. 事件监听策略优化

**原始代码问题：**
```typescript
// 在 document 上监听所有触摸事件
document.addEventListener('touchmove', touchMoveHandler, { passive: false })
document.addEventListener('touchend', touchEndHandler)
```

**修复后：**
```typescript
// 只在画布元素上监听触摸事件
canvasElement.addEventListener('touchstart', touchStartHandler, { passive: false })
canvasElement.addEventListener('touchmove', touchMoveHandler, { passive: false })
canvasElement.addEventListener('touchend', touchEndHandler)
```

### 2. 条件性事件阻止

**原始代码问题：**
```typescript
const touchMoveHandler = (event: TouchEvent) => {
  if (event.touches.length === 1) {
    const touch = event.touches[0]
    handleMove(touch.clientX, touch.clientY)
    event.preventDefault() // 无条件阻止页面滚动
  }
}
```

**修复后：**
```typescript
const touchMoveHandler = (event: TouchEvent) => {
  if (event.touches.length === 1 && isDragging) {
    const touch = event.touches[0]
    handleMove(touch.clientX, touch.clientY)
    // 只在拖拽BPMN画布时阻止页面滚动
    event.preventDefault()
  }
}
```

### 3. 添加触摸开始处理

新增了 `touchstart` 事件处理，确保拖拽状态正确初始化：

```typescript
const touchStartHandler = (event: TouchEvent) => {
  if (event.touches.length === 1) {
    const touch = event.touches[0]
    handleStart(touch.clientX, touch.clientY)
    // 只在拖拽时阻止默认行为
    if (isDragging) {
      event.preventDefault()
    }
  }
}
```

### 4. 完善事件清理机制

更新了清理函数，正确移除画布元素上的事件监听器：

```typescript
const cleanup = () => {
  // 清理鼠标事件
  if (eventHandlers.mousemove) {
    document.removeEventListener('mousemove', eventHandlers.mousemove)
  }
  if (eventHandlers.mouseup) {
    document.removeEventListener('mouseup', eventHandlers.mouseup)
  }

  // 清理触摸事件（从画布元素上移除）
  if (bpmnModeler && isMobileDevice.value) {
    const canvas = bpmnModeler.get('canvas')
    const canvasElement = canvas.getContainer()
    
    if (eventHandlers.touchstart) {
      canvasElement.removeEventListener('touchstart', eventHandlers.touchstart)
    }
    if (eventHandlers.touchmove) {
      canvasElement.removeEventListener('touchmove', eventHandlers.touchmove)
    }
    if (eventHandlers.touchend) {
      canvasElement.removeEventListener('touchend', eventHandlers.touchend)
    }
  }

  // 重置状态
  isDragging = false
  bpmnModeler = null
}
```

## 修复效果 🎯

1. **页面滚动恢复正常**：主页面可以正常滚动，不再被 BPMN 拖拽功能干扰
2. **BPMN 拖拽功能保持**：在 BPMN 画布区域内的拖拽功能正常工作
3. **移动端体验优化**：触摸事件只在必要时阻止默认行为
4. **内存泄漏防止**：完善的事件清理机制防止内存泄漏

### 5. 修复触摸拖拽方向问题

**问题描述：**
移动端触摸拖拽的方向与用户直觉相反，特别是在横屏模式下坐标系发生旋转。

**问题分析：**
- 竖屏模式：拖拽方向相反
- 横屏模式：坐标系旋转导致方向错乱
  - 向上拖拽 → 实际向左移动
  - 向下拖拽 → 实际向右移动
  - 向右拖拽 → 实际向上移动
  - 向左拖拽 → 实际向下移动

**解决方案：**
```typescript
const isLandscape = () => {
  return window.innerWidth > window.innerHeight
}

const handleMove = (clientX: number, clientY: number, isTouchEvent = false) => {
  if (isDragging && bpmnModeler) {
    const dx = clientX - lastX
    const dy = clientY - lastY
    const canvas = bpmnModeler.get('canvas')

    // 移动端触摸拖拽需要特殊处理
    if (isTouchEvent && isMobileDevice.value) {
      if (isLandscape()) {
        // 横屏模式：坐标系旋转，需要交换并调整方向
        canvas.scroll({ dx: dy, dy: -dx })
      } else {
        // 竖屏模式：反向移动以符合用户直觉
        canvas.scroll({ dx: -dx, dy: -dy })
      }
    } else {
      // 桌面端保持原有逻辑
      canvas.scroll({ dx, dy })
    }

    lastX = clientX
    lastY = clientY
  }
}
```

## 技术要点 💡

- **事件委托优化**：将触摸事件从全局 `document` 移至具体的画布元素
- **条件性事件阻止**：只在实际拖拽时才阻止页面滚动
- **状态管理**：通过 `isDragging` 状态精确控制拖拽行为
- **方向修正**：移动端触摸拖拽使用反向滚动以符合用户直觉
- **资源清理**：确保组件卸载时正确清理所有事件监听器

## 兼容性说明 📱

- **桌面端**：鼠标事件保持在 `document` 级别，支持拖拽到画布外的场景
- **移动端**：触摸事件限制在画布元素内，避免干扰页面滚动
- **混合设备**：自动检测设备类型，应用相应的事件处理策略
