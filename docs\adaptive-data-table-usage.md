# 自适应数据表格组件使用指南 📱💻

## 🎯 组件概述

`AdaptiveDataTable` 是一个完全继承 NaiveUI DataTable 的自适应组件，在 PC 端保持完全一致的体验，在移动端自动切换为现代简洁的卡片模式。

## ✨ 核心特性

- ✅ **PC端完全继承** - 100%兼容 NaiveUI DataTable 的所有功能和API
- ✅ **移动端自动适配** - 根据设备类型自动切换卡片视图  
- ✅ **视图切换功能** - 移动端支持卡片/表格视图自由切换
- ✅ **XxN布局支持** - 默认2列布局，支持1-3列可配置
- ✅ **简洁设计风格** - 简洁现代的设计，无动画效果
- ✅ **TSX语法实现** - 方便类型继承和动态渲染

## 📦 基础用法

### 1. 导入组件

```vue
<script setup lang="ts">
import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'
</script>
```

### 2. 基础示例

```vue
<template>
  <adaptive-data-table
    :data="tableData"
    :columns="tableColumns"
    :loading="loading"
    mobile-title="用户列表"
    :card-columns="2"
    @row-click="handleRowClick"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'

const tableData = ref([
  { id: 1, name: '张三', age: 25, status: '在职' },
  { id: 2, name: '李四', age: 30, status: '离职' }
])

const tableColumns = ref([
  {
    key: 'name',
    title: '姓名',
    mobileTitle: true,  // 移动端主标题
    mobileOrder: 1
  },
  {
    key: 'age', 
    title: '年龄',
    mobileOrder: 2
  },
  {
    key: 'status',
    title: '状态',
    mobilePosition: 'header',  // 移动端头部显示
    mobileOrder: 3
  }
])

const handleRowClick = (row: any, index: number) => {
  console.log('点击行:', row, index)
}
</script>
```

## 🔧 Props 配置

### 继承的 NaiveUI DataTable Props

组件完全继承 NaiveUI DataTable 的所有属性：

- `data` - 表格数据
- `loading` - 加载状态
- `pagination` - 分页配置
- `checkedRowKeys` - 选中行
- `rowKey` - 行键值
- `bordered` - 是否显示边框
- `striped` - 是否显示斑马纹
- `size` - 表格尺寸
- `maxHeight` / `minHeight` - 高度限制
- `scrollX` - 横向滚动
- `virtualScroll` - 虚拟滚动
- 等等...

### 移动端专用 Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `useMobileView` | Boolean | true | 是否启用移动端视图 |
| `showMobileHeader` | Boolean | true | 是否显示移动端头部 |
| `mobileTitle` | String | '数据列表' | 移动端标题 |
| `showViewToggle` | Boolean | true | 是否显示视图切换按钮 |
| `showMobileConfig` | Boolean | true | 是否显示移动端配置按钮 |
| `cardColumns` | Number | 2 | 卡片列数（1-3） |
| `showActions` | Boolean | false | 是否显示操作按钮 |

## 📋 列配置扩展

### 移动端专用列属性

```typescript
interface ExtendedColumn {
  // 基础属性
  key?: string
  title?: string
  width?: number | string
  render?: (row: any, index: number) => any
  
  // 移动端扩展属性
  mobileTitle?: boolean      // 是否作为移动端主标题
  mobileSubtitle?: boolean   // 是否作为移动端副标题
  mobileShow?: boolean       // 移动端是否显示
  mobileOrder?: number       // 移动端显示顺序
  mobilePosition?: 'header' | 'body' | 'footer'  // 移动端显示位置
  mobileSpan?: number        // 移动端占用列数
}
```

### 列配置示例

```javascript
const columns = [
  {
    key: 'name',
    title: '姓名',
    mobileTitle: true,        // 主标题
    mobileOrder: 1,
    render: (row) => <span style="font-weight: 600;">{row.name}</span>
  },
  {
    key: 'position',
    title: '职位', 
    mobileSubtitle: true,     // 副标题
    mobileOrder: 2
  },
  {
    key: 'status',
    title: '状态',
    mobilePosition: 'header', // 头部显示
    mobileOrder: 3,
    render: (row) => <NTag type="success">{row.status}</NTag>
  },
  {
    key: 'department',
    title: '部门',
    mobileOrder: 4            // 主体区域显示
  },
  {
    key: 'joinDate',
    title: '入职日期',
    mobilePosition: 'footer', // 底部显示
    mobileOrder: 5
  },
  {
    key: 'email',
    title: '邮箱',
    mobileShow: false,        // 移动端隐藏
    mobileOrder: 6
  }
]
```

## 🎪 事件处理

组件支持所有 NaiveUI DataTable 的事件：

```vue
<adaptive-data-table
  :data="data"
  :columns="columns"
  @row-click="handleRowClick"
  @update:checked-row-keys="handleCheckedRowsChange"
  @update:filters="handleFiltersChange"
  @update:sorter="handleSorterChange"
  @update:page="handlePageChange"
  @update:page-size="handlePageSizeChange"
  @update:columns="handleColumnsUpdate"
/>
```

## 🎨 插槽支持

### 操作按钮插槽

```vue
<adaptive-data-table :data="data" :columns="columns" :show-actions="true">
  <template #actions="{ row, index }">
    <n-space :size="4">
      <n-button size="tiny" type="primary" @click="handleEdit(row)">
        编辑
      </n-button>
      <n-button size="tiny" type="error" @click="handleDelete(row)">
        删除
      </n-button>
    </n-space>
  </template>
</adaptive-data-table>
```

### 其他插槽

组件完全支持 NaiveUI DataTable 的所有插槽。

## 📱 移动端特性

### 卡片布局

- **头部区域**: 主标题、副标题、状态标签
- **主体区域**: 网格布局显示字段信息
- **底部区域**: 次要信息显示
- **操作区域**: 操作按钮（可选）

### 视图切换

移动端支持卡片视图和表格视图切换：
- 卡片视图：现代iOS风格，触摸友好
- 表格视图：移动端优化的表格显示

### 响应式设计

- 自动检测设备类型
- 响应式列数配置
- 触摸优化的交互体验

### 单列布局优化 🎯

当 `cardColumns=1` 时，组件会自动应用以下优化：

#### 🎨 视觉优化
- **更大内边距**: 从 16px 增加到 20px
- **更大字体**: 主标题使用 text-lg，副标题使用 text-base
- **更大间距**: 元素间距增加，提升可读性
- **更高卡片**: 最小高度从 120px 增加到 140px

#### 📐 布局优化
- **无网格间距**: xGap 设为 0，充分利用屏幕宽度
- **最小外边距**: 只保留 px-2 的左右边距
- **更大内容间距**: 各区域间距增加

#### 💡 使用场景
- 详细信息展示
- 用户资料卡片
- 产品详情页面
- 任何需要突出单个项目的场景

```vue
<!-- 单列布局示例 -->
<adaptive-data-table
  :data="detailData"
  :columns="detailColumns"
  :card-columns="1"
  mobile-title="详细信息"
  :show-actions="true"
/>
```

## 🚀 完整示例

参考测试页面：`src/views/test/adaptive-data-table-test.vue`

## 💡 最佳实践

1. **列配置优化**: 为移动端合理配置列的显示位置和顺序
2. **性能考虑**: 大数据量时启用虚拟滚动
3. **用户体验**: 提供清晰的操作反馈和加载状态
4. **响应式设计**: 考虑不同屏幕尺寸的显示效果

## 🔍 故障排除

### 常见问题

1. **移动端检测不准确**: 确保正确注入 `isMobileDevice`
2. **列配置不生效**: 检查列的 `key` 和 `realKey` 配置
3. **样式问题**: 确保引入了 TailwindCSS

### 调试技巧

- 使用浏览器开发者工具模拟移动设备
- 检查控制台是否有类型错误
- 验证数据格式是否正确

---

🎉 现在你可以在项目中使用这个强大的自适应数据表格组件了！
