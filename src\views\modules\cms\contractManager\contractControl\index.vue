<template>
  <j-crud
    :queryMethod="pageQueryCmsContract"
    name="合同管理"
    :queryForm="queryForm"
    :tabs="tabs"
    default-check-tab="0"
    ref="crudRef"
    :ext-table-buttons="extTableButtons"
  >
    <template #extendButtons>
      <!-- 后面在搞动态的，现在写死就这几个科室 采管科、信息科、总务科 -->
      <n-button
        type="info"
        @click="openUploadSignedContract"
        v-if="['1', '528001', '521001', '531001'].includes(userDept)"
      >
        上传已签合同
      </n-button>
    </template>
    <template #extendFormItems>
      <n-form-item label="合同名称">
        <n-input v-model:value="queryForm.ctName">合同名称</n-input>
      </n-form-item>
      <n-form-item label="合同编号">
        <n-input v-model:value="queryForm.ctCode">合同编号</n-input>
      </n-form-item>
      <n-form-item label="经办人">
        <n-input v-model:value="queryForm.responsiblePerson">经办人</n-input>
      </n-form-item>
      <n-form-item label="时间区间">
        <n-date-picker
          v-model:formatted-value="createTimeRange"
          value-format="yyyy-MM-dd"
          @update-formatted-value="createDateChange"
          :shortcuts="rangeShortcuts"
          :update-value-on-close="true"
          type="daterange"
          :clearable="true"
        ></n-date-picker>
      </n-form-item>
      <n-form-item label="合同类型">
        <!--        <n-input>合同类型</n-input>-->
        <n-select v-model:value="queryForm.typeCode"></n-select>
      </n-form-item>
      <!--      <n-form-item label="合同性质">-->
      <!--        &lt;!&ndash;        <n-input>合同性质</n-input>&ndash;&gt;-->
      <!--        <n-select v-model:value="queryForm.typeCode"></n-select>-->
      <!--      </n-form-item>-->
      <n-form-item label="付款情况">
        <n-select v-model:value="queryForm.businessStatus"></n-select>
      </n-form-item>
    </template>
    <template #content>
      <j-modal
        v-model:show="showSign"
        width="95%"
        height="95%"
        title="新建合同"
        :content-style="{ height: 'calc(100% )' }"
        :show-footer="false"
      >
        <editPDF @close="closeEditPdf"></editPDF>
      </j-modal>

      <!--   预览pdf文件   -->
      <j-preview
        v-model:show="showPreview1"
        :oss-path="previewOssPath1"
        :oss-path-name="previewOssPathName1"
        bucket="temp"
      />
      <!--      <j-modal-->
      <!--        v-model:show="showSign"-->
      <!--        width="95%"-->
      <!--        height="95%"-->
      <!--        title="新建合同"-->
      <!--        :content-style="{ height: 'calc(100% )' }"-->
      <!--        :show-footer="false"-->
      <!--      >-->
      <!--        <editPDF @close="closeEditPdf"></editPDF>-->
      <!--      </j-modal>-->

      <j-modal
        v-model:show="showDirectUpload"
        width="100%"
        height="100%"
        title="上传已签合同"
        :content-style="{ height: 'calc(100% )' }"
        :show-confirm-btn="isEdit"
        @confirm="saveDirectUploadSignedApply()"
      >
        <direct-upload-signed-ct @close="closeDirectUpload" ref="directUploadSignedCtRef"></direct-upload-signed-ct>
      </j-modal>

      <!-- 合同扫描件上传 -->
      <j-modal
        v-model:show="showUpload"
        width="60%"
        @confirm="saveContractFile"
        @close="closeUploadContract"
        title="合同扫描件上传"
        :content-style="{ height: 'calc(100% )' }"
      >
        <n-descriptions label-placement="left" label-style="width:20%" bordered :column="1" size="small">
          <n-descriptions-item>
            <template #label>相对方名称(客户) </template>
            {{ oppositeName }}
          </n-descriptions-item>
          <n-descriptions-item>
            <template #label> 合同编号</template>
            {{ ctCode }}
          </n-descriptions-item>

          <n-descriptions-item>
            <template #label> 签约状态</template>
            <n-gradient-text :type="ctStatusTextType" :size="24">
              {{ ctStatus }}
            </n-gradient-text>
          </n-descriptions-item>
          <n-descriptions-item>
            <template #label> 文件类型</template>
            <j-select dict-type="CONTRACT_FILE_TYPE" v-model:value="signFileApplyForm.attType"></j-select>
          </n-descriptions-item>
          <n-descriptions-item>
            <template #label> 签订日期</template>
            <n-date-picker v-model:formatted-value="signFileApplyForm.signTime" type="date" />
          </n-descriptions-item>

          <n-descriptions-item>
            <template #label> 纸质档存放地点</template>
            <n-form>
              <n-form-item label="存放科室" label-placement="left">
                <j-bus-hos-org v-model:value="signFileApplyForm.positionOrg"></j-bus-hos-org>
              </n-form-item>
              <n-form-item label="具体位置" label-placement="left">
                <n-input
                  type="textarea"
                  v-model:value="signFileApplyForm.placeDetail"
                  placeholder="注意：为管理规范，纸质档与电子档需一一对应，格式范例: xxx办公室一, 1号柜 - 3层 - 01 号文件夹"
                ></n-input>
              </n-form-item>
            </n-form>
          </n-descriptions-item>
          <n-descriptions-item>
            <template #label> 附件上传</template>
            <n-upload multiple directory-dnd :file-list="fileList" @update:file-list="fileChange">
              <n-upload-dragger>
                <div style="margin-bottom: 12px">
                  <j-icon name="upload" :width="40" :height="40"></j-icon>
                </div>
                <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
                <n-p depth="3" style="margin: 8px 0 0 0"> 支持单个和批量上传，上传完即时呈现下表 </n-p>
              </n-upload-dragger>
            </n-upload>
          </n-descriptions-item>
          <n-descriptions-item>
            <template #label> 备注说明</template>
            <n-input type="textarea" v-model:value="signFileApplyForm.remark"></n-input>
          </n-descriptions-item>
          <!-- <n-descriptions-item v-if="showNeedAuditSwitch">
            <template #label> 是否移交审批</template>
            <span style="width: 20px">否</span>
            <n-switch
              style="margin-left: 20px; width: 50px"
              :rubber-band="false"
              v-model:value="needAudti"
              size="large"
              :round="false"
            >
              <template #checked-icon>
                <n-icon :component="ArrowForwardOutline" />
              </template>
              <template #unchecked-icon> <n-icon :component="ArrowBackOutline" /> </template></n-switch
            ><span style="margin-left: 20px">是</span>
          </n-descriptions-item> -->
          <n-descriptions-item>
            <template #label> 选择职能科室审核人</template>
            <j-bus-emp-search
              :restricted-emp-type="['517', '518']"
              @update:value="value => (signFileApplyForm.firstChoic = value)"
            />
          </n-descriptions-item>
          <n-descriptions-item>
            <template #label>操作信息</template>
            <div style="display: flex">
              <div>
                <span>当前操作人: </span>
                <span>{{ signFileApplyForm.appyerName }}</span>
              </div>
              <br />
              <div style="width: 30px"></div>
              <div>
                <span>当前操作人科室: </span>
                <span>{{ signFileApplyForm.appyerOrg }}</span>
              </div>
              <br />
              <div style="width: 30px"></div>
              <div>
                <span>操作时间: </span>
                <span>{{ signFileApplyForm.operateTime }}</span>
              </div>
            </div>
          </n-descriptions-item>
        </n-descriptions>
        <n-descriptions style="margin-top: 20px" :column="6">
          <n-descriptions-item v-for="(item, idx) in contractFile" :key="idx">
            <div style="height: 80px; transform: translate(0)">
              <n-float-button shape="square" :width="70" :height="80" @click="showSignContractFile(item)">
                <j-icon name="hetongwenjian_cu"></j-icon>
                <template #description> 合同文件{{ idx + 1 }} </template>
              </n-float-button>
            </div>
          </n-descriptions-item>
        </n-descriptions>
      </j-modal>
      <!--   预览pdf文件   -->
      <j-preview
        v-model:show="showPreview2"
        :oss-path="previewOssPath3"
        :oss-path-name="previewOssPathName3"
        bucket="cms"
      />
      <!--  走延期    -->
      <j-modal v-model:show="showDelay" width="90%" title="合同延期修改" :content-style="{ height: 'calc(100% )' }">
        <!--        <j-title-line title="关键要素">-->
        <!--          <template #default>-->
        <!--            <div style="display: flex; flex-direction: row">-->
        <!--              <n-popconfirm placement="left" @positive-click="clearKeyElements">-->
        <!--                <template #trigger>-->
        <!--                  <j-icon name="delete" style="cursor: pointer" :width="18" :height="18" />-->
        <!--                </template>-->
        <!--                <span>移除全部</span>-->
        <!--              </n-popconfirm>-->
        <!--              <div style="width: 40px"></div>-->
        <!--              <n-popover placement="right">-->
        <!--                <template #trigger>-->
        <!--                  <j-icon name="add" style="cursor: pointer" :width="18" :height="18" @click="addKeyElement" />-->
        <!--                </template>-->
        <!--                添加-->
        <!--              </n-popover>-->
        <!--            </div>-->
        <!--          </template></j-title-line-->
        <!--        >-->
        <!--        <n-data-table :columns="delayColumns" :data="delayData"></n-data-table>-->
        <!--        <div>-->
        <!--          <n-button type="info" @click="saveDelay">保存</n-button> <n-button @click="reSetKeyElements">重置</n-button>-->
        <!--        </div>-->
        <n-scrollbar style="height: 100%">
          <n-space vertical>
            <delayApply
              ref="delayApplyRef"
              v-model:chk-state="queryForm.chkState"
              :details="details"
              @close="closeItemApply()"
            >
            </delayApply>
          </n-space>
        </n-scrollbar>
      </j-modal>

      <!--  走验收    -->
      <j-modal
        v-model:show="showAccept"
        width="95%"
        height="95%"
        title="合同验收"
        :show-footer="false"
        :content-style="{ height: 'calc(100% )' }"
      >
        <go-check-n-accept 
          :contract-data="currentAcceptContract" 
          :is-edit="true"
          @close="closeAcceptance"
          @success="onAcceptanceSuccess"
          ref="goCheckNAcceptRef"
        />
      </j-modal>

      <!--   走付款   -->
      <j-modal v-model:show="showPayment" width="100%" height="100%" title="编制付款条件">
        <!--        <n-form>-->
        <!--                    <n-row :gutter="18">-->
        <!--                      <n-col :span="6">-->
        <!--                        <n-form-item label="合同总额" label-placement="left">-->
        <!--                          <n-input-number :show-button="false" v-model:value="specialForm.totalAmt"></n-input-number>-->
        <!--                        </n-form-item>-->
        <!--                      </n-col>-->
        <!--                      <n-col :span="6">-->
        <!--                        <n-form-item label="付款方式" label-placement="left">-->
        <!--                          <j-select-->
        <!--                            :dictType="'PAYMENT_TYPE'"-->
        <!--                            :on-update:value="(value: any) => selectPaymentTerms(value)"-->
        <!--                            v-model:value="specialForm.paymentType"-->
        <!--                          ></j-select>-->
        <!--                        </n-form-item>-->
        <!--                      </n-col>-->
        <!--                      <n-col :span="6">-->
        <!--                        <n-form-item label="付款期数" label-placement="left">-->
        <!--                          <n-input-number :show-button="false" v-model:value="specialForm.paymentTerms"></n-input-number>-->
        <!--                        </n-form-item>-->
        <!--                      </n-col>-->
        <!--                    </n-row>-->
        <!--        </n-form>-->
        <!--        <n-data-table :columns="payColumns" :data="payMentData"> </n-data-table>-->
        <!--        <n-descriptions-->
        <!--          label-placement="left"-->
        <!--          bordered-->
        <!--          style="margin-top: 20px; width: 100%"-->
        <!--          :column="1"-->
        <!--          label-style="width:20%"-->
        <!--        >-->
        <!--          <n-descriptions-item>-->
        <!--            <template #label>备注说明</template>-->
        <!--            <n-input type="textarea"></n-input>-->
        <!--          </n-descriptions-item>-->
        <!--          <n-descriptions-item>-->
        <!--            <template #label>操作信息</template>-->
        <!--            <div style="display: flex">-->
        <!--              <div>-->
        <!--                <span>当前操作人: </span>-->
        <!--                <span>{{ goReimForm.appyerName }}</span>-->
        <!--              </div>-->
        <!--              <div style="width: 30px"></div>-->
        <!--              <div>-->
        <!--                <span>操作时间: </span>-->
        <!--                <span>{{ goReimForm.operateTime }}</span>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--          </n-descriptions-item>-->
        <!--        </n-descriptions>-->
        <contract-payment-reim
          :payMentData="payMentData"
          :contract="currentContract"
          ref="contractPaymentReimRef"
        ></contract-payment-reim>
      </j-modal>
      <j-modal
        title="合同作废申请"
        width="90%"
        height="100%"
        v-model:show="showGoApplyInvalidContract"
        @close="closeInvalidContractApply"
        @confirm="saveInvalidApply"
      >
        <n-scrollbar style="height: 100%">
          <n-space vertical>
            <InvalidContract ref="goInvalidContractRef" :details="details" @close="closeInvalidContractApply()">
            </InvalidContract>
          </n-space>
        </n-scrollbar>
      </j-modal>
      <!--合同终止-->
      <j-modal
        title="合同终止申请"
        width="90%"
        height="100%"
        v-model:show="showGoApplyTerminationContract"
        @close="closeTerminationContractApply"
        @confirm="saveTerminationApply"
      >
        <n-scrollbar style="height: 100%">
          <n-space vertical>
            <TerminationContract
              ref="goTerminationContractRef"
              :details="details"
              @close="closeTerminationContractApply()"
            >
            </TerminationContract>
          </n-space>
        </n-scrollbar>
      </j-modal>
      <!-- 查看上传已签合同详情 -->
      <process-instance-detail-modal
        v-model:show="processDetail.visible"
        :process-instance-id="processDetail.processInstanceCode"
        :other-props="{}"
      />
    </template>
  </j-crud>
</template>
<script lang="ts" setup>
  import { computed, h, onMounted, ref, VNode, provide } from 'vue'
  import { CRUDColumnInterface, JTab } from '@/types/comps/crud'
  import InvalidContract from '@/views/modules/cms/contractManager/contractControl/components/invalidContract.vue'
  import TerminationContract from '@/views/modules/cms/contractManager/contractControl/components/terminationContract.vue'
  import editPDF from '@/views/modules/cms/contractManager/contractDraft/components/index.vue'
  import { addCmsContractApply2, generateAuditPdf, pageQueryCmsContract } from '@/api/cms/contractManage/ContractWeb'
  import {
    NBadge,
    NButton,
    NDataTable,
    NDatePicker,
    NInput,
    NInputNumber,
    NPopover,
    NPopselect,
    NTag,
    UploadFileInfo,
  } from 'naive-ui'
  import { AuditExecution, Icon, Icon as JIcon } from '@jcomponents'
  import { ContainerValueType } from '@/types/enums/enums'
  import { Option } from '@/types/comps/common'
  import { addCmsContractDetail, queryCmsContractDetail } from '@/api/cms/contractManage/ContractDetailWeb'
  import { fileDownload } from '@/api/cms/opposite'
  import JPGlobal from '@jutil'
  import { getOutsideChain } from '@/api/common/common'
  import { queryOrg } from '@/api/hrm/hrmOrg'
  import { AuditDetail, IRes } from '@jtypes'
  import { querySysDict } from '@/api/sys/dict'
  import { queryDictData } from '@/api/hrm/dictManage/treeSelectDict'
  import { useUserStore } from '@/store'
  import { queryCmsContract } from '@/api/cms/contract/ContractWeb'
  import { row } from 'mathjs'
  import { goGenerateReim, queryCmsPaymentTerms } from '@/api/cms/contractManage/PaymentTermsWeb'
  import { addCmsKeyElements, queryCmsKeyElements, updateCmsKeyElements } from '@/api/cms/contractManage/KeyElementsWeb'
  import { ArrowBackOutline, ArrowForwardOutline } from '@vicons/ionicons5'
  import AuditSelect from '@/components/common/auditFlow/auditSelect.vue'
  import { updateDetails } from '@/api/hrm/config/hrmAuditCfg'
  import { useRoute } from 'vue-router'
  import { useRouter } from 'vue-router'
  import BackApply from '@/views/modules/cms/borrowing/backContract/components/backApply.vue'
  import delayApply from '@/views/modules/cms/contractManager/contractControl/components/delayApply.vue'
  import { queryContractType } from '@/api/cms/contractType.ts'
  import DirectUploadSignedCt from '@/views/modules/cms/contractManager/contractControl/components/directUploadSignedCt.vue'
  import { ElMessageBox } from 'element-plus'
  import * as ProcessInstanceApi from '@/api/bpm/processInstance'
  import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'
  import ContractPaymentReim from '@/views/modules/cms/contractManager/contractControl/components/contractPaymentReim.vue'
  import GoCheckNAccept from '@/views/modules/cms/contractManager/contractControl/components/goCheckNAccept.vue'
  import { submitAcceptanceApply } from '@/api/cms/contractAcceptance/index'

  // crud
  let crudRef = ref<any>()

  // 展开生成合同pdf
  let showSign = ref(false)
  const startSign = () => {
    showSign.value = true
  }

  //tp1 走作废
  let goInvalidContractRef = ref()
  let showGoApplyInvalidContract = ref(false)
  let details = ref({ form: null })
  const goInvalidContract = (row: any) => {
    details.value.form = row
    showGoApplyInvalidContract.value = true
  }

  const saveInvalidApply = () => {
    goInvalidContractRef.value.submitApply()
    showGoApplyInvalidContract.value = false
    crudRef.value.queryData()
  }
  //tp1 关闭合同作废
  const closeInvalidContractApply = () => {
    showGoApplyInvalidContract.value = false
    crudRef.value.queryData()
  }

  //tp1 走终止
  let goTerminationContractRef = ref()
  let showGoApplyTerminationContract = ref(false)

  const goTerminationContract = (row: any) => {
    details.value.form = row
    showGoApplyTerminationContract.value = true
  }

  const saveTerminationApply = () => {
    goTerminationContractRef.value.submitApply()
    showGoApplyTerminationContract.value = false
    crudRef.value.queryData()
  }
  //tp1 关闭合同终止
  const closeTerminationContractApply = () => {
    showGoApplyTerminationContract.value = false
    crudRef.value.queryData()
  }

  // 关闭编辑合同的界面
  const closeEditPdf = () => {
    showSign.value = false
  }

  //tp1 接入新审核使用的逻辑
  const typeOptions = ref([
    { label: '上传已签合同', value: '1', processDefinitionKey: 'CMS_SCYQ_APPLY' },
    { label: '合同作废', value: '2', processDefinitionKey: 'CMS_ZF_APPLY' },
    { label: '合同延期', value: '3', processDefinitionKey: 'CMS_YQ_APPLY' },
    { label: '合同验收', value: '4', processDefinitionKey: 'CMS_YS_APPLY' },
    { label: '合同终止', value: '5', processDefinitionKey: 'CMS_ZZ_APPLY' },
    { label: '合同续签', value: '6', processDefinitionKey: 'CMS_XQ_APPLY' },
  ])
  //流程定义key
  const processDefinitionKey = computed(() => {
    return typeOptions.value.find(item => item.value == '1')?.processDefinitionKey
  })
  //流程实例ID
  const processInstanceCode = ref<string>('')
  //展示流程详情
  let showDetail = ref(false)
  let showAdd = ref(false)
  //关闭model
  const close = () => {
    showAdd.value = false
    crudRef.value.queryData()
  }

  //TODO 合同扫描件上传
  let showUpload = ref(false)
  let showUpload1 = ref(false)
  //发票上传
  let fileList = ref<Array<UploadFileInfo>>([])
  let ossInfo = ref<{ items?: Array<any>; path?: string; name?: string }>({
    items: [],
  })
  // 文件改变
  const fileChange = (files: Array<UploadFileInfo>) => {
    fileList.value = files
  }
  //合同上传
  let contractFile = ref<{ value: string; label: string }[]>([])
  //  显示已经上传的文件
  let rowId = ref(1)
  let oppositeName = ref('')
  let ctCode = ref('')
  let ctStatus = ref('')
  let ctStatusTextType = ref('danger')
  let contractId = ref(0)

  const maintenanceContractData = ref(null)
  const isDetail = ref(false)
  const isEdit = ref(true)
  provide('maintenanceContractData', { maintenanceContractData, isDetail, isEdit })
  //草拟合同信息
  const maintenanceContract = (row: any, update: boolean) => {
    maintenanceContractData.value = row
    isDetail.value = true
    isEdit.value = update
    showDirectUpload.value = true
  }

  const restartAppllData = ref<any>({})
  provide('restartAppllData', restartAppllData)
  const restartApply = (row: any, update: boolean) => {
    restartAppllData.value = row
    isDetail.value = true
    isEdit.value = update
    showDirectUpload.value = true
  }

  //查看已签合同详情，这里用单独的界面
  const processDetail = ref<{
    visible: boolean
    processInstanceCode: string
  }>({
    visible: false,
    processInstanceCode: null,
  })
  const maintenanceContract1 = (row: any, update: boolean) => {
    processDetail.value.processInstanceCode = row.signedProcessInstanceCode
    processDetail.value.visible = true
  }

  const showContractFile = (row: any) => {
    showUpload.value = true
    contractId.value = row.id
    init()
    rowId.value = row.id
    oppositeName.value = row.oppositeName
    ctCode.value = row.ctCode
    ctStatus.value = row.ctStatus
    switch (row.chkState1) {
      case '0':
        //因为这时ct_status 0    chk_state 1 表示合同草拟审批通过且未签订
        ctStatus.value = '未签订'
        ctStatusTextType.value = 'danger'
        // ctStatusTextType.value = 'success'
        // ctStatusTextType.value = 'warning'
        break
      case '1':
        ctStatus.value = '已确认签订'
        ctStatusTextType.value = 'success'
        showNeedAuditSwitch.value = false
        break
      case '2':
        ctStatus.value = '审批未通过，需重新上传合同文件'
        ctStatusTextType.value = 'warning'
        break
    }
    queryCmsContractDetail({ applyId: row.id }).then((res: any) => {
      // 循环处理数据
      res.data.forEach(item => {
        // 将att和attName拆分成数组
        const atts = item.att.split(',')
        const attNames = item.attName.split(',')
        // 遍历数组，将数据添加到contractFile中
        atts.forEach((att, index) => {
          let attItem = {
            value: att,
            label: attNames[index],
          }
          contractFile.value.push(attItem)
        })
        signFileApplyForm.value.positionOrg = ''
        signFileApplyForm.value.placeDetail = ''
        if (item.placeDetail !== '') {
          signFileApplyForm.value.positionOrg = item.positionOrg
          signFileApplyForm.value.placeDetail = item.placeDetail
        }
      })
    })
  }

  let showDirectUpload = ref(false)
  let directUploadSignedCtRef = ref()
  //直接上传已签合同
  const openUploadSignedContract = () => {
    maintenanceContractData.value = null
    isDetail.value = false
    isEdit.value = true
    showDirectUpload.value = true
  }
  const saveDirectUploadSignedApply = async () => {
    try {
      const result = await directUploadSignedCtRef.value.submitUpload()
      if (result) {
        closeDirectUpload()
      }
    } catch (error) {
      console.error('提交失败:', error)
    }
  }
  const closeDirectUpload = () => {
    showDirectUpload.value = false
    window.$message.success('已签合同提交成功')
    crudRef.value.queryData()
  }
  // 预览已上传文件
  let showPreview2 = ref(false)
  let previewOssPath3 = ref('')
  let previewOssPathName3 = ref('')

  // 是否移交审批
  let needAudti = ref(true)
  // 是否展示是否移交审批开关
  let showNeedAuditSwitch = ref(true)
  let auditDetails = ref([])
  const updateCmsContractDetails = ({ details, flag }: { details: AuditDetail[]; flag: boolean }) => {
    if (flag) {
      auditDetails.value = []
      signFileApplyForm.value.auditDetails = []
    } else {
      signFileApplyForm.value.auditDetails = details
      auditDetails.value = details
    }
  }
  let signFileApplyForm = ref({
    appyerName: '',
    appyer: '',
    appyerOrg: '',
    appyerOrgId: '',
    operateTime: '',
    auditDetails: <AuditDetail[]>[],
    placeDetail: '',
    positionOrg: '',
    remark: '',
    attType: '1',

    firstChoic: '',
    signTime: null,
  })
  const showSignContractFile = (item: any) => {
    showPreview2.value = true
    previewOssPath3.value = item.value
    previewOssPathName3 = item.label
  }
  // 保存上传文件.或走审批
  const saveContractFile = () => {
    if (needAudti.value == true) {
      let formData = new FormData()
      //附件
      for (let i = 0; i < fileList.value.length; i++) {
        let f = fileList.value[i]
        formData.append('attFiles[' + i + ']', f.file as File)
      }
      formData.append('applyId', contractId.value as any)
      formData.append('id', contractId.value as any)
      // JPGlobal.addArrToFormData(formData, signFileApplyForm.value.auditDetails, 'auditDetails')
      JPGlobal.addArrToFormData(formData, auditDetails.value, 'auditDetails')

      // 添加表单数据
      for (let key in signFileApplyForm.value) {
        if (!formData.has(key)) {
          formData.append(key, (signFileApplyForm.value as any)[key])
        }
      }
      formData.delete('auditDetails')
      addCmsContractApply2(formData).then((res: any) => {
        if (res.code == '200') {
          window.$message.success('提交成功')
          showUpload.value = false
        }
      })
      showPreview1.value = false
    } else {
      uploadContractFiles()
      showUpload.value = false
    }
  }
  // 上传合同文件（已签订好的）及其他附件 TODO
  const uploadContractFiles = () => {
    let formData = new FormData()
    //附件
    for (let i = 0; i < fileList.value.length; i++) {
      let f = fileList.value[i]
      formData.append('attFiles[' + i + ']', f.file as File)
    }

    formData.append('applyId', rowId.value as any)
    formData.append('positionOrg', signFileApplyForm.value.positionOrg as any)
    formData.append('placeDetail', signFileApplyForm.value.placeDetail as any)
    formData.append('attType', signFileApplyForm.value.attType as any)
    formData.append('remark', signFileApplyForm.value.remark as any)
    formData.append('firstChoic', signFileApplyForm.value.firstChoic as any)
    formData.append('signTime', signFileApplyForm.value.signTime as any)
    addCmsContractDetail(formData).then((res: any) => {
      if (res.code == '200') {
        window.$message.success('上传成功')
      }
    })
    showPreview1.value = false
  }
  // 关闭上传合同文件
  const closeUploadContract = () => {
    contractFile.value = []
    showUpload.value = false
  }

  let payTermsData = ref([])
  let orgList = ref([''])
  let ctStatusData = ref([])
  //合同分类
  let contractType = ref<Option[]>([])
  let contractTypeNoTree = ref<Option[]>([])

  onMounted(() => {
    queryOrg({ activeFlag: '1' }).then((res: IRes) => {
      orgList.value = res.data
    })
    queryDictData({ codeType: 'PAYMENT_TYPE' }).then((res: any) => {
      payTermsData.value = res.data
    })
    init()
    queryDictData({ codeType: 'CT_STATUS' }).then((res: any) => {
      ctStatusData.value = res.data
    })
    queryContractType({}).then((res: any) => {
      let treeData = JPGlobal.getTreeNode(res.data, 'code', 'parentCode', 'name')
      contractType.value = treeData
      contractTypeNoTree.value = res.data
    })
  })

  //TODO 走延期
  let showDelay = ref(false)
  let delayData = ref([])
  let delayData1 = ref([])
  const updateKeyElements = (row: any) => {
    showDelay.value = true
    queryCmsKeyElements({ contractId: row.id }).then((res: any) => {
      delayData.value = res.data
      delayData1.value = res.data
    })
  }
  //TODO 走延期2
  let delayApplyRef = ref()
  const closeItemApply = () => {
    crudRef.value.queryData()
  }
  const goDelayApply = (row: any) => {
    details.value.form = row
    showDelay.value = true
  }

  //重置关键要素
  const reSetKeyElements = () => {
    queryCmsKeyElements({ contractId: delayData.value[0].contractId }).then((res: any) => {
      delayData.value = res.data
    })
  }
  const saveDelay = () => {
    updateCmsKeyElements({ dtoList: delayData.value, contractId: delayData1.value[0].contractId }).then((res: any) => {
      window.$message.success('延期成功')
    })
  }
  interface KeyElementNode {
    keyElement: string
    nodeRequire: string
    timeNode: any
    seq: number
  }
  // 关键要素节点
  let keyElementNode = {
    keyElement: '',
    nodeRequire: '',
    timeNode: '',
    seq: 0,
  }
  // 新增关键要素
  const addKeyElement = () => {
    // 创建一个新的节点对象，并更新seq
    const newNode: KeyElementNode = {
      ...keyElementNode,
      seq: delayData.value.length + 1, // seq设置为当前数组的长度加1
    }
    // 将新节点添加到数组中
    delayData.value.push(newNode)
  }
  //删除所有关键要素
  const clearKeyElements = () => {
    delayData.value.splice(0, delayData.value.length)
  }
  //删除一个关键要素
  const deleteOneKeyElement = (seq: number) => {
    // 使用filter方法删除seq为1的元素
    delayData.value = delayData.value.filter(element => element.seq !== seq)
    // 使用sort方法重新排序
    delayData.value.sort((a, b) => a.seq - b.seq)

    // 重置seq值，并按1,2,3,4,5...排序
    let currentSeq = 1
    delayData.value.forEach(element => {
      element.seq = currentSeq
      currentSeq++
    })
  }

  const delayColumns = ref<CRUDColumnInterface[]>([
    {
      title: '关键要素',
      key: 'keyElement',
      width: 50,
      render: (row: any) => {
        return h(
          NInput,
          {
            value: row.keyElement,
            onUpdateValue: (val: string | null) => {
              if (val) {
                row.keyElement = val
              }
            },
          },
          {}
        )
      },
    },
    {
      title: '节点控制要求',
      key: 'nodeRequire',
      width: 100,
      render: (row: any) => {
        return h(
          NInput,
          {
            value: row.nodeRequire,
            onUpdateValue: (val: string | null) => {
              if (val) {
                row.nodeRequire = val
              }
            },
          },
          {}
        )
      },
    },
    {
      title: '时间节点',
      key: 'timeNode',
      width: 50,
      render: (row: any) => {
        return h(
          NDatePicker,
          {
            type: 'date',
            valueFormat: 'yyyy-MM-dd',
            formattedValue: row.timeNode,
            onUpdateFormattedValue: (val: string | null) => {
              if (val) {
                row.timeNode = val
              }
            },
          },
          {}
        )
      },
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      width: 60,
      render: (row: any, idx: number) => {
        return h(JIcon, {
          name: 'delete',
          onClick: (e: Event) => {
            e.stopPropagation()
            deleteOneKeyElement(row.seq)
          },
        })
      },
    },
  ])

  //TODO 走付款
  let showPayment = ref(false)
  let goReimForm = ref({
    appyer: '',
    appyerName: '',
    operateTime: '',
  })
  // 用户信息回显
  const userStore = useUserStore()
  //当前登录用户科室
  let userDept = ref('')
  const init = () => {
    let today = new Date()
    let year = today.getFullYear()
    let month = (today.getMonth() + 1).toString().padStart(2, '0')
    let day = today.getDate().toString().padStart(2, '0')
    goReimForm.value.operateTime = `${year}-${month}-${day}`
    signFileApplyForm.value.operateTime = `${year}-${month}-${day}`

    goReimForm.value.appyerName = userStore.getUserInfo.nickname
    goReimForm.value.appyer = userStore.getUserInfo.hrmUser?.empCode

    signFileApplyForm.value.appyerName = userStore.getUserInfo.nickname
    signFileApplyForm.value.appyer = userStore.getUserInfo.hrmUser?.empCode
    signFileApplyForm.value.appyerOrg = userStore.getUserInfo.hrmUser?.hrmOrgName
    signFileApplyForm.value.appyerOrgId = userStore.getUserInfo.hrmUser?.hrmOrgId

    userDept.value = userStore.getUserInfo.hrmUser?.hrmOrgId || '1'
  }
  const contractPaymentReim = ref()
  let payMentData = ref()
  let currentContract = ref({})
  // 展示走付款
  const goFillOutReim = (row: any) => {
    currentContract.value = row
    queryCmsPaymentTerms({ contractId: row.id }).then((res: any) => {
      payMentData.value = res.data
      showPayment.value = true
    })
  }
  //写待申请报销数据
  const goGenerateWaitReimApply = (row: any) => {
    goGenerateReim({ id: row.id }).then((res: any) => {
      window.$message.success('生成成功，待申请报销')
    })
  }
  const router = useRouter()
  const goEcsContractReim = (row: any) => {
    router.push({
      path: '/ecs/reimMgt/contractReim',
      query: {
        ctCode: row.ctCode,
      },
    })
  }

  const payColumns = ref<CRUDColumnInterface[]>([
    { title: '付款阶段', key: 'stage', width: 50 },
    { title: '付款要求', key: 'requirements', width: 100 },
    { title: '比例', key: 'proportion', width: 50 },
    { title: '付款总额', key: 'currentPayAmt', width: 80 },
    { title: '计划付款时间', key: 'paymentTime', width: 80 },
    { title: '业务状态', key: 'businessStatus', width: 80 },
    {
      title: '操作',
      key: 'opt',
      width: 100,
      render: (row: any) => {
        return [
          h(
            NTag,
            {
              type: 'info',
              onClick: () => {
                goGenerateWaitReimApply(row)
              },
            },
            '生成待申请记录'
          ),
          h('div', { style: 'width:10px' }),
          h(
            NTag,
            {
              type: 'success',
              onClick: () => {
                goEcsContractReim(row)
              },
            },
            '  走报销  '
          ),
        ]
      },
    },
  ])
  let route = useRoute()
  // 查询条件
  let queryForm = ref({
    chkState: '1',
    chkState1: null,
    ctStatus: '0',
    ctName: '',
    ctCode: '',
    responsiblePerson: '',
    startTime: '',
    endTime: '',
    typeCode: '',
    id: route.query.id ? Number(route.query.id) : null,
    businessStatus: '',
  })
  // 日期区间
  let createTimeRange = ref<any>()
  const createDateChange = (value: [string, string] | null) => {
    if (value != null) {
      queryForm.value.startTime = value[0]
      queryForm.value.endTime = value[1]
    } else {
      queryForm.value.startTime = ''
      queryForm.value.endTime = ''
    }
  }

  // TODO 走验收
  let showAccept = ref(false)
  let currentAcceptContract = ref({})
  let goCheckNAcceptRef = ref()
  
  const goToAccept = async (row: any) => {
    try {
      // 首先创建验收申请
      const formData = new FormData()
      formData.append('contractId', row.id)
      formData.append('contractName', row.ctName)
      formData.append('contractCode', row.ctCode)
      formData.append('applyDate', new Date().toISOString().split('T')[0])
      formData.append('acceptanceDate', new Date().toISOString().split('T')[0])
      formData.append('applyReason', '合同验收申请')
      formData.append('hospitalId', row.hospitalId || 'zjxrmyy')

      // 调用创建验收申请的API
      const res = await submitAcceptanceApply(formData)

      if (res.code === '200') {
        // 将applyId添加到合同数据中
        currentAcceptContract.value = {
          ...row,
          applyId: res.data?.id || Math.floor(Math.random() * 1000) + 1 // 如果API返回了ID就使用，否则使用随机ID
        }

        showAccept.value = true
      } else {
        window.$message.error('创建验收申请失败：' + res.message)
      }
    } catch (error) {
      console.error('创建验收申请失败:', error)
      window.$message.error('创建验收申请失败，请重试')
    }
  }
  
  const closeAcceptance = () => {
    showAccept.value = false
  }
  
  const onAcceptanceSuccess = () => {
    showAccept.value = false
    crudRef.value.queryData()
    window.$message.success('验收申请提交成功')
  }

  const acceptColumns = ref<CRUDColumnInterface[]>([
    {
      title: '关键要素',
      key: 'keyElement',
      width: 50,
      render: (row: any) => {
        return h(
          NInput,
          {
            value: row.keyElement,
            onUpdateValue: (val: string | null) => {
              if (val) {
                row.keyElement = val
              }
            },
          },
          {}
        )
      },
    },
    {
      title: '节点控制要求',
      key: 'nodeRequire',
      width: 100,
      render: (row: any) => {
        return h(
          NInput,
          {
            value: row.nodeRequire,
            onUpdateValue: (val: string | null) => {
              if (val) {
                row.nodeRequire = val
              }
            },
          },
          {}
        )
      },
    },
    {
      title: '时间节点',
      key: 'timeNode',
      width: 50,
      render: (row: any) => {
        return h(
          NDatePicker,
          {
            type: 'date',
            valueFormat: 'yyyy-MM-dd',
            formattedValue: row.timeNode,
            onUpdateFormattedValue: (val: string | null) => {
              if (val) {
                row.timeNode = val
              }
            },
          },
          {}
        )
      },
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      width: 60,
      render: (row: any, idx: number) => {
        return h(JIcon, {
          name: 'tiaozhuan1',
          style: 'width:20px ;height:20px',
          onClick: (e: Event) => {
            e.stopPropagation()
            deleteOneKeyElement(row.seq)
          },
        })
      },
    },
  ])

  //待归档合同
  let columns1 = ref<CRUDColumnInterface[]>([
    { title: '序号', key: 'index', width: 80, align: 'center' },
    {
      title: '合同分类',
      key: 'ctTypeCode',
      width: 100,
      render: (row: any) => {
        const item = contractTypeNoTree.value.find(item => item.code === row.ctTypeCode)
        return h('i', {}, item ? item.name : row.ctTypeCode)
      },
    },
    {
      title: '统一编号',
      key: 'ctUnifiedCode',
      width: 100,
      render: (row: any) => {
        return [
          row.ctUnifiedCode,
          h(JIcon, {
            name: 'upload',
            width: 20,
            height: 20,
            offset: [-15, -25],
            style: { cursor: 'pointer', marginLeft: '20px' },
            onClick: (e: Event) => {
              e.stopPropagation()
              if (row.chkState == '1' && row.chkState1 == '9') {
                //草拟合同 信息维护
                maintenanceContract(row, true)
              } else {
                showContractFile(row)
              }
            },
          }),
        ]
      },
    },
    {
      title: '合同编号',
      key: 'ctCode',
      width: 100,
    },
    {
      title: '合同名称',
      key: 'ctName',
      width: 150,
      align: 'center',
      render: (row: any) => {
        return h(
          'span',
          {
            style: {
              fontSize: '16px',
              color: '#333',
              fontWeight: 'bold',
            },
          },
          row.ctName
        )
      },
    },
    {
      title: '订立申请时间',
      key: 'applyTime',
      width: 150,
      render: (row: any) => {
        const date = row.applyTime
        if (!date) return '/'
        const [datePart, timePart] = date.split(' ')
        if (!datePart || !timePart) return '/'
        const [year, month, day] = datePart.split('-')
        const [hour, minute, second] = timePart.split(':')
        if (!year || !month || !day || !hour || !minute || !second) return '/'
        return h('span', {}, [
          h('span', {}, year + '-'),
          h('span', { style: 'color: #8B4513' }, month + '-' + day),
          h('span', {}, ' '),
          h('span', { style: 'color: #006400' }, hour + ':' + minute + ':' + second),
        ])
      },
    },
    {
      title: '草案原件',
      key: 'att',
      width: 100,
      align: 'center',
      render: (row: any, index: any) => {
        if (!row.att) {
          return h(NTag, { type: 'info' }, { default: () => '直接归档' })
        }
        return fileShowRender(row, index)
      },
    },

    {
      title: '使用科室',
      key: 'manageOrg',
      width: 150,
      render: (row: any) => {
        let org: any = orgList.value.find((item: any) => item.orgId == row.manageOrg)
        if (org) {
          return org.orgName
        }
        return row.manageOrg
      },
    },
    { title: '相对方名称', key: 'oppositeName', width: 150 },
    {
      title: '合同状态',
      key: 'ctStatus',
      width: 100,
      render: (row: any) => {
        let text = ''
        let type = ''
        switch (row.ctStatus) {
          case '0':
            text = '未签订'
            type = 'warning'
            break
          case '1':
            text = '已签归档'
            type = 'success'
            break
          case '2':
            text = '已签归档驳回'
            type = 'error'
            break
          case '3':
            text = '已签待归档'
            type = 'info'
            break
          default:
            text = '未知状态'
            type = 'default'
        }
        return h(NTag, { type }, { default: () => text })
      },
    },
    {
      title: '合同金额',
      key: 'totalAmt',
      width: 100,
      render: (row: any) => {
        return h('span', {}, row.totalAmt ? `${row.totalAmt}元` : '/')
      },
    },
    {
      title: '合同有效期',
      key: 'validityStartDate',
      width: 200,
      align: 'center',
      render: (row: any) => {
        const startDate = !row.validityStartDate || row.validityStartDate === 'null' ? '' : row.validityStartDate
        const endDate = !row.validityEndDate || row.validityEndDate === 'null' ? '' : row.validityEndDate
        return h('div', {}, [
          h('span', { style: { color: '#2E8B57', fontSize: '16px' } }, startDate),
          h('span', ' ~ '),
          h('span', { style: { color: '#654321', fontSize: '16px' } }, endDate),
        ])
      },
    },
    { title: '对方联络人', key: 'contactName', width: 100 },
    { title: '联系电话', key: 'contactPhone', width: 100 },
    { title: '院方', key: 'responsiblePerson', width: 100 },
    { title: '联系方式', key: 'responsiblePhone', width: 100 },
    { title: '联系方式', key: 'responsiblePhone', width: 100 },
    { title: '合同份数', key: 'ctCopies', width: 100 },
  ])

  //已归档合同
  let columns = ref<CRUDColumnInterface[]>([
    { title: '序号', key: 'index', width: 80, align: 'center' },
    {
      title: '合同分类',
      key: 'ctTypeCode',
      width: 100,
      render: (row: any) => {
        const item = contractTypeNoTree.value.find(item => item.code === row.ctTypeCode)
        return h('i', {}, item ? item.name : row.ctTypeCode)
      },
    },
    {
      title: '统一编号',
      key: 'ctUnifiedCode',
      width: 100,
      render: (row: any) => {
        return [
          row.ctUnifiedCode,
          h(JIcon, {
            name: 'upload',
            width: 20,
            height: 20,
            offset: [-15, -25],
            style: { cursor: 'pointer', marginLeft: '20px' },
            onClick: (e: Event) => {
              e.stopPropagation()
              if (row.chkState == '1' && row.chkState1 == '9') {
                //草拟合同 信息维护
                maintenanceContract(row, true)
              } else {
                showContractFile(row)
              }
            },
          }),
        ]
      },
    },
    {
      title: '合同编号',
      key: 'ctCode',
      width: 100,
    },
    {
      title: '合同名称',
      key: 'ctName',
      width: 150,
      align: 'center',
      render: (row: any) => {
        return h(
          'span',
          {
            style: {
              fontSize: '16px',
              color: '#333',
              fontWeight: 'bold',
            },
          },
          row.ctName
        )
      },
    },
    {
      title: '归档申请时间',
      key: 'applyTime1',
      width: 150,
      render: (row: any) => {
        const date = row.applyTime1
        if (!date) return ''
        const [datePart, timePart] = date.split(' ')
        const [year, month, day] = datePart.split('-')
        const [hour, minute, second] = timePart.split(':')
        return h('span', {}, [
          h('span', {}, year + '-'),
          h('span', { style: 'color: #8B4513' }, month + '-' + day),
          h('span', {}, ' '),
          h('span', { style: 'color: #006400' }, hour + ':' + minute + ':' + second),
        ])
      },
    },
    {
      title: '草案原件',
      key: 'att',
      width: 100,
      align: 'center',
      render: (row: any, index: any) => {
        if (!row.att) {
          return h(NTag, { type: 'info' }, { default: () => '直接归档' })
        }
        return fileShowRender(row, index)
      },
    },

    {
      title: '使用科室',
      key: 'manageOrg',
      width: 150,
      render: (row: any) => {
        let org: any = orgList.value.find((item: any) => item.orgId == row.manageOrg)
        if (org) {
          return org.orgName
        }
        return row.manageOrg
      },
    },
    { title: '相对方名称', key: 'oppositeName', width: 150 },
    {
      title: '合同状态',
      key: 'ctStatus',
      width: 100,
      render: (row: any) => {
        let text = ''
        let type = ''
        switch (row.ctStatus) {
          case '0':
            text = '未签订'
            type = 'warning'
            break
          case '1':
            text = '已签归档'
            type = 'success'
            break
          case '2':
            text = '已签归档驳回'
            type = 'error'
            break
          case '3':
            text = '已签待归档'
            type = 'info'
            break
          default:
            text = '未知状态'
            type = 'default'
        }
        return h(NTag, { type }, { default: () => text })
      },
    },
    {
      title: '合同金额',
      key: 'totalAmt',
      width: 100,
      render: (row: any) => {
        return h('span', {}, row.totalAmt ? `${row.totalAmt}元` : '/')
      },
    },
    {
      title: '合同有效期',
      key: 'validityStartDate',
      width: 200,
      align: 'center',
      render: (row: any) => {
        return h('div', {}, [
          h('span', { style: { color: '#2E8B57', fontSize: '16px' } }, row.validityStartDate),
          h('span', ' ~ '),
          h('span', { style: { color: '#654321', fontSize: '16px' } }, row.validityEndDate),
        ])
      },
    },
    { title: '对方联络人', key: 'contactName', width: 100 },
    { title: '联系电话', key: 'contactPhone', width: 100 },
    { title: '院方', key: 'responsiblePerson', width: 100 },
    { title: '联系方式', key: 'responsiblePhone', width: 100 },
    { title: '联系方式', key: 'responsiblePhone', width: 100 },
    { title: '合同份数', key: 'ctCopies', width: 100 },
  ])

  //获取 tab columns
  const getColumns = (tabName: string) => {
    switch (tabName) {
      case '0':
        return columns1.value
        break
      default:
        return columns.value
    }
  }

  let curTab = ref('0')
  const tabChange = (tab: JTab) => {
    needAudti.value = true
    curTab.value = tab.name
    switch (tab.name) {
      case '0':
        queryForm.value.chkState1 = '9'
        queryForm.value.chkState = '1'
        // queryForm.value.ctStatus = '0'
        break
      case '3':
        queryForm.value.chkState1 = '0'
        queryForm.value.chkState = '1'
        // queryForm.value.ctStatus = '3'
        break
      case '1':
        queryForm.value.chkState1 = '1'
        queryForm.value.chkState = '1'
        break
      case '2':
        queryForm.value.chkState1 = '2'
        queryForm.value.chkState = '1'
        break
      case '4':
        queryForm.value.chkState1 = '4'
        queryForm.value.chkState = '1'
        break
    }
  }

  const tabs = ref<JTab[]>([
    {
      name: '0',
      tab: '待签订',
      useBadge: true,
      columns: getColumns('0'),
      tabChange: tabChange,
    },
    {
      name: '3',
      tab: '已签合同审批中',
      useBadge: true,
      columns: getColumns('3'),
      tabChange: tabChange,
    },
    {
      name: '1',
      tab: '已归档合同',
      useBadge: true,
      columns: getColumns('1'),
      tabChange: tabChange,
    },
    {
      name: '2',
      tab: '已驳回归档请求',
      useBadge: true,
      columns: getColumns('2'),
      tabChange: tabChange,
    },
    // {
    //   name: '2',
    //   tab: '未通过',
    //   useBadge: true,
    //   columns: getColumns('2'),
    //   tabChange: tabChange,
    // },
    {
      name: '4',
      tab: '已撤销',
      useBadge: true,
      columns: getColumns('4'),
      tabChange: tabChange,
    },
  ])

  // 日期范围选择快捷键
  const rangeShortcuts = {
    昨年: () => {
      return getLastYear()
    },
    上季: () => {
      return getLastQuarter()
    },
    上月: () => {
      return getLastMonth()
    },
    上周: () => {
      // 获取当前日期
      let today = new Date()
      // 获取当前是星期几
      let day = today.getDay()
      let startDate, endDate
      if (day == 0) {
        // 计算本周第一天的日期
        startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 13)
        // 计算本周最后一天的日期
        endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 7)
      } else {
        startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day - 6)
        endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day)
      }
      return [startDate.getTime(), endDate.getTime()]
    },
    本周: () => {
      return getCurrentWeek()
    },
    本月: () => {
      return getCurrentMonth()
    },
    本年: () => {
      return getCurrentYear()
    },
    本季: () => {
      return getCurrentQuarter()
    },
  }
  //获取上一个季度
  const getLastQuarter = () => {
    // 获取当前日期
    let now = new Date()

    // 获取当前年份
    let year = now.getFullYear()

    // 获取当前月份（0-11，需要转换为1-12）
    let month = now.getMonth() + 1

    // 确定上一个季度
    let lastQuarter = Math.ceil(month / 3) - 1

    // 如果当前季度是1，则上一个季度是去年第4季度
    if (lastQuarter === 0) {
      lastQuarter = 4
      year -= 1
    }

    // 计算上一个季度第一个月的开始日期
    let firstMonthOfLastQuarter = (lastQuarter - 1) * 3 + 1
    let firstDayOfLastQuarter = new Date(year, firstMonthOfLastQuarter - 1, 1)

    // 计算上一个季度最后一个月的结束日期
    let lastMonthOfLastQuarter = lastQuarter * 3
    let lastDayOfLastQuarter = new Date(year, lastMonthOfLastQuarter, 0)

    // 设置最后一天的时间为23:59:59.999
    lastDayOfLastQuarter.setHours(23, 59, 59, 999)

    // 获取开始日期和结束日期的时间戳
    let startDateTimestamp = firstDayOfLastQuarter.getTime()
    let endDateTimestamp = lastDayOfLastQuarter.getTime()

    // 返回时间戳数组
    return [startDateTimestamp, endDateTimestamp]
  }
  //获取当前季度
  const getCurrentQuarter = () => {
    // 获取当前日期
    let now = new Date()

    // 获取当前年份
    let year = now.getFullYear()

    // 获取当前月份（0-11，需要转换为1-12）
    let month = now.getMonth() + 1

    // 确定当前季度
    let quarter = Math.ceil(month / 3)

    // 计算当前季度第一个月的开始日期
    let firstMonthOfQuarter = (quarter - 1) * 3 + 1
    let firstDayOfQuarter = new Date(year, firstMonthOfQuarter - 1, 1)

    // 计算当前季度最后一个月的结束日期
    let lastMonthOfQuarter = quarter * 3
    let lastDayOfQuarter = new Date(year, lastMonthOfQuarter, 0)

    // 设置最后一天的时间为23:59:59.999
    lastDayOfQuarter.setHours(23, 59, 59, 999)

    // 获取开始日期和结束日期的时间戳
    let startDateTimestamp = firstDayOfQuarter.getTime()
    let endDateTimestamp = lastDayOfQuarter.getTime()

    // 返回时间戳数组
    return [startDateTimestamp, endDateTimestamp]
  }
  //获取今年开始/结束时间戳
  const getCurrentYear = () => {
    // 获取当前日期
    let now = new Date()

    // 获取当前年份
    let year = now.getFullYear()

    // 计算当前年份的第一天
    let firstDayOfYear = new Date(year, 0, 1)

    // 计算当前年份的最后一天
    // 下一年度的第一天减去1毫秒就是当前年份的最后一天
    let lastDayOfYear = new Date(year + 1, 0, 0)

    // 设置最后一天的时间为23:59:59.999
    lastDayOfYear.setHours(23, 59, 59, 999)

    // 获取开始日期和结束日期的时间戳
    let startDateTimestamp = firstDayOfYear.getTime()
    let endDateTimestamp = lastDayOfYear.getTime()

    // 返回时间戳数组
    return [startDateTimestamp, endDateTimestamp]
  }
  //获取昨年开始/结束时间戳
  const getLastYear = () => {
    // 获取当前日期
    let now = new Date()

    // 获取当前年份
    let currentYear = now.getFullYear()

    // 计算去年的年份
    let lastYear = currentYear - 1

    // 计算去年第一天
    let firstDayOfLastYear = new Date(lastYear, 0, 1)

    // 计算去年最后一天
    let lastDayOfLastYear = new Date(lastYear, 11, 31)

    // 设置最后一天的时间为23:59:59.999
    lastDayOfLastYear.setHours(23, 59, 59, 999)

    // 获取开始日期和结束日期的时间戳
    let startDateTimestamp = firstDayOfLastYear.getTime()
    let endDateTimestamp = lastDayOfLastYear.getTime()

    // 返回时间戳数组
    return [startDateTimestamp, endDateTimestamp]
  }

  //获取当前月份
  const getCurrentMonth = () => {
    // 获取当前日期
    let now = new Date()

    // 获取当前年份
    let year = now.getFullYear()

    // 获取当前月份（0-11，需要转换为1-12）
    let month = now.getMonth() + 1

    // 计算当前月份的第一天
    let startDate = new Date(year, month - 1, 1)

    // 计算当前月份的最后一天
    // 下一个月的第一天减去1毫秒就是当前月份的最后一天
    let endDate = new Date(year, month, 0)

    // 返回时间戳数组
    return [startDate.getTime(), endDate.getTime()]
  }
  //上个月
  const getLastMonth = () => {
    // 获取当前日期
    let now = new Date()
    // 获取当前年份
    let year = now.getFullYear()
    // 获取当前月份（0-11，需要转换为1-12）
    let month = now.getMonth()
    // 获取上一个月的年份和月份
    if (month === 0) {
      year--
      month = 12
    }
    // 计算上个月的第一天
    let startDate = new Date(year, month - 1, 1)
    // 计算上个月的最后一天
    let endDate = new Date(year, month, 0)
    // 返回时间戳数组
    return [startDate.getTime(), endDate.getTime()]
  }

  // 获取当前周
  const getCurrentWeek = () => {
    // 获取当前日期
    let today = new Date()
    // 获取当前是星期几
    let day = today.getDay()
    let startDate,
      endDate = null
    if (day == 0) {
      // 计算本周第一天的日期
      startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day - 6)
      // 计算本周最后一天的日期
      endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day)
    } else {
      startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day + 1)
      endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day + 7)
    }
    return [startDate.getTime(), endDate.getTime()]
  }
  const printApprovalForm = (row: any) => {
    console.log(row)
    generateAuditPdf(row).then((res: any) => {
      showPreview1.value = true
      previewOssPath1.value = res.data
      previewOssPathName1.value = res.data
    })
  }
  // 拓展表格按钮
  const extTableButtons = computed((): VNode[] => {
    switch (curTab.value) {
      case '0':
        return [
          h(
            NTag,
            {
              type: 'default',
              callback: (row: any) => {
                goInvalidContract(row)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '走作废'
          ),
          h(
            NTag,
            {
              type: 'default',
              callback: (row: any) => {
                printApprovalForm(row)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '审批表'
          ),
        ]

      case '3':
        return [
          h(
            NTag,
            {
              type: 'info',
              bordered: false,
              callback: (row: any) => {
                //TODO:   这里待做   待归档审核详情统一界面
                //草拟合同 信息维护
                maintenanceContract1(row, false)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '详情'
          ),
          h(
            NTag,
            {
              bordered: false,
              type: 'error',
              callback: (row: any) => {
                //TODO: 这里待做 撤销
                if (!row.signedProcessInstanceCode || !row.signedProcessInstanceCode.trim()) {
                  window.$message.warning('获取审批流程失败')
                  return
                }
                backApply(row)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '撤销'
          ),
          h(
            NTag,
            {
              type: 'default',
              callback: (row: any) => {
                printApprovalForm(row)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '审批表'
          ),
        ]
      case '2':
        return [
          h(
            NButton,
            {
              text: true,
              tag: 'a',
              type: 'info',
              callback: (row: any) => {
                maintenanceContract1(row, false)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '详情'
          ),
          h(
            NButton,
            {
              text: true,
              tag: 'a',
              type: 'success',
              callback: (row: any) => {
                maintenanceContract(row, true)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '重新发起'
          ),
          h(
            NTag,
            {
              type: 'default',
              callback: (row: any) => {
                printApprovalForm(row)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '审批表'
          ),
        ]
      case '4':
        return [
          h(
            NButton,
            {
              text: true,
              tag: 'a',
              type: 'info',
              callback: (row: any) => {
                maintenanceContract1(row, false)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '详情'
          ),
          h(
            NButton,
            {
              text: true,
              tag: 'a',
              type: 'success',
              callback: (row: any) => {
                maintenanceContract(row, true)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '重新发起'
          ),
          h(
            NTag,
            {
              type: 'default',
              callback: (row: any) => {
                printApprovalForm(row)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '审批表'
          ),
        ]
      default:
        return [
          h(
            NTag,
            {
              type: 'success',
              callback: (row: any) => {
                goFillOutReim(row)
              },
            },
            () => '走付款'
          ),
          h(
            NTag,
            {
              type: 'error',
              callback: (row: any) => {
                // updateKeyElements(row)
                goDelayApply(row)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '走延期'
          ),
          h(
            NTag,
            {
              type: 'warning',
              callback: (row: any) => {
                console.log('合同信息:', JSON.stringify(row, null, 2))
                goToAccept(row)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '走验收  '
          ),
          h(
            NTag,
            {
              type: 'default',
              callback: (row: any) => {
                goTerminationContract(row)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '走终止'
          ),
          h(
            NTag,
            {
              type: 'info',
              callback: (row: any) => {},
              style: {
                cursor: 'pointer',
              },
            },
            () => '走续签'
          ),
          h(
            NTag,
            {
              type: 'default',
              callback: (row: any) => {
                printApprovalForm(row)
              },
              style: {
                cursor: 'pointer',
              },
            },
            () => '审批表'
          ),
        ]
        break
    }
  })

  //撤销审批流程
  const backApply = async (row: any) => {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '取消原因不能为空',
    })
    // 发起取消
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.signedProcessInstanceCode, value)
    window.$message.success('取消成功')
    // 刷新列表
    await crudRef.value.queryData()
  }

  //OSS地址预览
  let showPreview1 = ref(false)
  //OSS路径
  let previewOssPath1 = ref('')
  //Oss文件名称
  let previewOssPathName1 = ref('')
  const fileShowRender = (row: any, index: number) => {
    const iconProps = {
      width: 24,
      height: 24,
      style: {
        cursor: 'pointer',
      },
    }
    let curOptions = ref<Option[]>([])
    if (row['attName']) {
      curOptions.value = []
      let paths = row['attName'].split(',')
      let vals = row['att'].split(',')
      paths.forEach((path: string, idx: number) => {
        if (path) {
          curOptions.value.push({
            label: path,
            value: vals[idx],
          })
        }
      })
    }
    let vnodes = [
      h(
        NPopselect,
        {
          options: curOptions.value,
          onUpdateValue: (val: any, option: Option) => {
            showPreview1.value = true
            previewOssPath1.value = option.value as string
            previewOssPathName1.value = option.label
          },
        },
        {
          default: () =>
            h(
              NPopover,
              {
                disabled: curOptions.value.length > 0,
              },
              {
                trigger: () =>
                  h(Icon, {
                    show: curOptions.value.length > 0,
                    name: 'preview',
                    ...iconProps,
                    onClick: () => {
                      if (row['att'].includes('originalDraftOfContract/')) {
                        showPreview2.value = true
                        previewOssPath3.value = row['att']
                        previewOssPathName3.value = row['attName']
                      } else {
                        showPreview1.value = true
                        previewOssPath1.value = row['att']
                        previewOssPathName1.value = row['attName']
                      }
                    },
                  }),
                default: () => '查看文件',
              }
            ),
        }
      ),
    ]
    return h('div', {}, vnodes)
  }
</script>
